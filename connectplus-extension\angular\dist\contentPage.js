/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./chrome/src/get-Selector.ts":
/*!************************************!*\
  !*** ./chrome/src/get-Selector.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getSelector: () => (/* binding */ getSelector)
/* harmony export */ });
function getSelector(docList, selectorList) {
    let foundSelector = null;
    for (var i = 0; i < selectorList.length; i++) {
        foundSelector = docList.querySelector(selectorList[i]);
        if (foundSelector && !foundSelector.textContent.includes("connections")) {
            return foundSelector;
        }
    }
    return foundSelector;
}


/***/ }),

/***/ "./chrome/src/linkedin-Api.ts":
/*!************************************!*\
  !*** ./chrome/src/linkedin-Api.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LINKEDIN_VOYAGER_BLENDED: () => (/* binding */ LINKEDIN_VOYAGER_BLENDED)
/* harmony export */ });
const LINKEDIN_VOYAGER_BLENDED = 'https://www.linkedin.com/voyager/api/search/blended';


/***/ }),

/***/ "./chrome/src/linkedin-company.ts":
/*!****************************************!*\
  !*** ./chrome/src/linkedin-company.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromCompanyPage: () => (/* binding */ getExecutiveFromCompanyPage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromCompanyPage(document, peopleJson) {
    let search_list;
    for (var i = 0; i < peopleJson["listing"].length; i++) {
        search_list = document.querySelectorAll(peopleJson["listing"][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    let executives = [];
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name"]);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name_link"]);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["imgUrl"]);
        const executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["executiveDesgination"]);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href && name_link.href !== null
                ? name_link.href.split("?")[0]
                : "",
            id: name_link &&
                name_link.href &&
                name_link.href !== null &&
                name_link?.href?.split("in/").length > 1
                ? name_link.href.split("in/")[1].split("?")[0].replace("/", "")
                : "unknown_" + name,
            companyName_desg: executiveDesgination?.textContent.trim(),
            sales: false,
        };
        if (executive.name_link !== "" &&
            name_link?.href?.split("in/").length > 1 &&
            name_link?.href?.split("in/")[1] !== "") {
            executives.push(executive);
        }
    }
    return executives;
}


/***/ }),

/***/ "./chrome/src/linkedin-connection.ts":
/*!*******************************************!*\
  !*** ./chrome/src/linkedin-connection.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromConnectionPage: () => (/* binding */ getExecutiveFromConnectionPage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromConnectionPage(document, peopleJson) {
    let search_list;
    for (var i = 0; i < peopleJson["listing"].length; i++) {
        search_list = document.querySelectorAll(peopleJson["listing"][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    let executives = [];
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name"]);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name_link"]);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["imgUrl"]);
        const executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["executiveDesgination"]);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href && name_link.href !== null
                ? name_link.href.split("?")[0]
                : "",
            id: name_link &&
                name_link.href &&
                name_link.href !== null &&
                name_link?.href?.split("in/").length > 1
                ? name_link.href.split("in/")[1].split("?")[0].replace("/", "")
                : "unknown_" + name,
            companyName_desg: executiveDesgination?.textContent.trim(),
            sales: false,
        };
        if (executive.name_link !== "" &&
            name_link?.href?.split("in/").length > 1 &&
            name_link?.href?.split("in/")[1] !== "") {
            executives.push(executive);
        }
    }
    return executives;
}


/***/ }),

/***/ "./chrome/src/linkedin-facetConnection.ts":
/*!************************************************!*\
  !*** ./chrome/src/linkedin-facetConnection.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromFacetConnectionPage: () => (/* binding */ getExecutiveFromFacetConnectionPage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromFacetConnectionPage(document, peopleJson) {
    let search_list;
    for (var i = 0; i < peopleJson["listing"].length; i++) {
        search_list = document.querySelectorAll(peopleJson["listing"][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    let executives = [];
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name"]);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name_link"]);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["imgUrl"]);
        const executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["executiveDesgination"]);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href && name_link.href !== null
                ? name_link.href.split("?")[0]
                : "",
            id: name_link &&
                name_link.href &&
                name_link.href !== null &&
                name_link?.href?.split("in/").length > 1
                ? name_link.href.split("in/")[1].split("?")[0].replace("/", "")
                : "unknown_" + name,
            companyName_desg: executiveDesgination?.textContent.trim(),
            sales: false,
        };
        if (executive.name_link !== "" &&
            name_link?.href?.split("in/").length > 1 &&
            name_link?.href?.split("in/")[1] !== "") {
            executives.push(executive);
        }
    }
    return executives;
}


/***/ }),

/***/ "./chrome/src/linkedin-people.ts":
/*!***************************************!*\
  !*** ./chrome/src/linkedin-people.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromPeoplePage: () => (/* binding */ getExecutiveFromPeoplePage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromPeoplePage(document, peopleJson) {
    let search_list;
    for (var i = 0; i < peopleJson["listing"].length; i++) {
        search_list = document.querySelectorAll(peopleJson["listing"][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    let executives = [];
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name"]);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name_link"]);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["imgUrl"]);
        const executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["executiveDesgination"]);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href && name_link.href !== null
                ? name_link.href.split("?")[0]
                : "",
            id: name_link &&
                name_link.href &&
                name_link.href !== null &&
                name_link?.href?.split("in/").length > 1
                ? name_link.href.split("in/")[1].split("?")[0].replace("/", "")
                : "unknown_" + name,
            companyName_desg: executiveDesgination?.textContent.trim(),
            sales: false,
        };
        if (executive.name_link !== "" &&
            name_link?.href?.split("in/").length > 1 &&
            name_link?.href?.split("in/")[1] !== "") {
            executives.push(executive);
        }
    }
    return executives;
}


/***/ }),

/***/ "./chrome/src/linkedin-profile.ts":
/*!****************************************!*\
  !*** ./chrome/src/linkedin-profile.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromProfilePage: () => (/* binding */ getExecutiveFromProfilePage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromProfilePage(document, profileJson) {
    // Delay function to wait for elements to load
    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    const nameEl = document?.getElementsByClassName("inline t-24 v-align-middle break-words")[0]?.innerHTML;
    const elementName = document?.getElementsByClassName("t-14 t-normal t-black--light pt1 break-words");
    const designationEl = document?.getElementsByClassName("text-body-medium break-words")[0]?.innerHTML;
    var name;
    if (nameEl?.length > 0) {
        name = nameEl;
    }
    else {
        name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, profileJson["name"]);
    }
    // Initialize company_name variable
    var company_name = "";
    // Function to extract current company name
    function getCurrentCompanyButtonText() {
        // Try finding the button by aria-label
        /* const button = document.querySelector(
          'ul.zxGRRhJZBSLQOtNlUGfBwCEoHbHVFDgDiSo button[aria-label^="Current company:"]'
        ); */
        const button = document.querySelector('ul > li > button[aria-label^="Current company:"]');
        // Check if button is found and get the company name from it
        if (button) {
            const ariaLabel = button.getAttribute("aria-label");
            if (ariaLabel && ariaLabel.includes("Current company:")) {
                const companyName = ariaLabel.replace("Current company:", "").trim();
                // Remove unwanted part after "Click to skip to experience card"
                const cleanCompanyName = companyName.replace(/\s*\.\s*Click to skip to experience card$/, "");
                return cleanCompanyName;
            }
        }
        // If not found, check for another potential selector (fallback)
        const companyFallback = document.querySelector(".pv-entity__secondary-title")?.textContent;
        if (companyFallback) {
            return companyFallback.trim();
        }
        // Return empty string if company name is not found
        return "";
    }
    // Wait for all necessary elements to load, this may need tuning depending on the actual page load times
    await delay(1500); // Wait for 2 seconds before proceeding
    // Get the current company name
    company_name = getCurrentCompanyButtonText();
    var name_link;
    if (elementName?.length > 0) {
        name_link = elementName[0]?.innerHTML;
    }
    else {
        name_link = window.location.href;
    }
    const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, profileJson["imgUrl"]);
    var executiveDesgination;
    if (designationEl?.length > 0) {
        executiveDesgination = designationEl;
    }
    else {
        const nameParentDiv = name?.closest("div");
        if (nameParentDiv?.length > 0) {
            executiveDesgination = nameParentDiv?.nextElementSibling;
        }
        else {
            executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, profileJson["executiveDesgination"]);
        }
    }
    const executives = {
        name: name && name !== null ? name : "",
        imgUrl: imgUrl && imgUrl.src !== null
            ? imgUrl.src.match(/data/g) === null
                ? imgUrl.src
                : "assets/img/ic_default_profile.svg"
            : "assets/img/ic_default_profile.svg",
        name_link: name_link,
        id: name_link && name_link?.split("in/").length > 1
            ? name_link.split("in/")[1].split("?")[0].replace("/", "")
            : "unknown_" + name,
        companyName_desg: executiveDesgination?.replace(/&amp;/g, "&"),
        company_domain: company_name,
        sales: false,
    };
    if (name_link.split("in/")[1].split("/").length <= 2 &&
        name !== "LinkedIn Member") {
        return [executives];
    }
}


/***/ }),

/***/ "./chrome/src/linkedin-sales-profile.ts":
/*!**********************************************!*\
  !*** ./chrome/src/linkedin-sales-profile.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromSalesProfilePage: () => (/* binding */ getExecutiveFromSalesProfilePage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromSalesProfilePage(document, peopleJson) {
    const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["name"]);
    // const name_link = window.location.href as any;
    const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["name_link"]);
    const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["imgUrl"]);
    const designation = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["executiveDesgination"]);
    const companyName = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["companyName"]);
    const companyNameLink = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(document, peopleJson["companyNameLink"]);
    const executive = {
        name: name && name !== null ? name.textContent.trim() : "",
        imgUrl: imgUrl && imgUrl.src !== null
            ? imgUrl.src.match(/data/g) === null
                ? imgUrl.src
                : "assets/img/ic_default_profile.svg"
            : "assets/img/ic_default_profile.svg",
        name_link: name_link && name_link !== null ? name_link : "",
        id: name_link &&
            name_link.href !== null &&
            name_link.href !== null &&
            name_link.split("people/").length > 1
            ? name_link.split("people/")[1].split(",")[0]
            : "unknown_" +
                name?.textContent.replace(new RegExp(" ", "gi"), "_").toLowerCase(),
        companyNameLink: companyNameLink && companyNameLink.href != null
            ? companyNameLink.href
            : "",
        companyName_desg: designation?.textContent + "|" + companyName?.textContent.trim(),
        sales: true,
    };
    return [executive];
}


/***/ }),

/***/ "./chrome/src/linkedin-sales.ts":
/*!**************************************!*\
  !*** ./chrome/src/linkedin-sales.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromSalesPage: () => (/* binding */ getExecutiveFromSalesPage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromSalesPage(document, peopleJson) {
    let executives = [];
    let search_list;
    for (var i = 0; i < peopleJson['listing'].length; i++) {
        search_list = document.querySelectorAll(peopleJson['listing'][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['name']);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['name_link']);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['imgUrl']);
        const designation = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['executiveDesgination']);
        const companyName = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['companyName']);
        const companyNameLink = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['companyNameLink']);
        const overallExperience = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['overallExperience']);
        const experienceinrole = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['experienceinrole']);
        const experience = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['experience']);
        const location = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson['location']);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href !== null
                ? name_link.href
                : "",
            id: name_link && name_link.href !== null && name_link.href !== null && name_link.href.split('people/').length > 1
                ? name_link.href.split('people/')[1].split(',')[0]
                : "unknown_" + name?.textContent.replace(new RegExp(" ", "gi"), "_").toLowerCase(),
            // : "unknown_" + name.replace(new RegExp(" ", "gi"), "_").toLowerCase(),
            companyNameLink: companyNameLink && companyNameLink.href != null
                ? companyNameLink.href
                : "",
            companyName_desg: designation?.textContent + '|' + companyName?.textContent.trim(),
            overallExperience: overallExperience && overallExperience !== null
                ? overallExperience.innerText
                : experience && experience !== null
                    ? experience.innerText
                    : "",
            experienceinrole: experienceinrole && experienceinrole !== null
                ? experienceinrole.innerText
                : "",
            location: location && location !== null ? location.innerText : "",
            sales: true
        };
        executives.push(executive);
    }
    return executives;
}
// export async function getFilterFromPage(document) {
//     let filters = [];
//     const filtersContainer = document.getElementsByClassName('search-filter__collapsible-section');
//     for (let i = 0; i < filtersContainer.length; i++) {
//         filters.push({
//             filterName: filtersContainer[i].querySelector('div>div>div.ember-view>div>label').textContent.trim().replace(new RegExp(" ", "gi"), "_").toLowerCase(),
//             filterValue: getFilterValue(filtersContainer[i].querySelectorAll('div>div>ul.list-style-none>li.artdeco-pill--blue'))
//         });
//     }
//     return filters;
// }
// function getFilterValue(filterValuesList) {
//     let filterValues = [];
//     for (let i = 0; i < filterValuesList.length; i++) {
//         filterValues.push(filterValuesList[i].textContent.trim());
//     }
//     return filterValues;
// }


/***/ }),

/***/ "./chrome/src/linkedin-search.ts":
/*!***************************************!*\
  !*** ./chrome/src/linkedin-search.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getExecutiveFromSearchPage: () => (/* binding */ getExecutiveFromSearchPage)
/* harmony export */ });
/* harmony import */ var _get_Selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./get-Selector */ "./chrome/src/get-Selector.ts");

async function getExecutiveFromSearchPage(document, peopleJson) {
    let search_list;
    for (var i = 0; i < peopleJson["listing"].length; i++) {
        search_list = document.querySelectorAll(peopleJson["listing"][i]);
        if (search_list.length > 0) {
            break;
        }
    }
    let executives = [];
    for (let i = 0; i < search_list.length; i++) {
        const name = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name"]);
        const name_link = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["name_link"]);
        const imgUrl = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["imgUrl"]);
        const executiveDesgination = (0,_get_Selector__WEBPACK_IMPORTED_MODULE_0__.getSelector)(search_list[i], peopleJson["executiveDesgination"]);
        const executive = {
            name: name && name !== null ? name.textContent.trim() : "",
            imgUrl: imgUrl && imgUrl.src !== null
                ? imgUrl.src.match(/data/g) === null
                    ? imgUrl.src
                    : "assets/img/ic_default_profile.svg"
                : "assets/img/ic_default_profile.svg",
            name_link: name_link && name_link.href && name_link.href !== null
                ? name_link.href.split("?")[0]
                : "",
            id: name_link &&
                name_link.href &&
                name_link.href !== null &&
                name_link?.href?.split("in/").length > 1
                ? name_link.href.split("in/")[1].split("?")[0].replace("/", "")
                : "unknown_" + name,
            companyName_desg: executiveDesgination?.textContent.trim(),
            sales: false,
        };
        if (executive.name_link !== "" &&
            name_link?.href?.split("in/").length > 1 &&
            name_link?.href?.split("in/")[1] !== "") {
            executives.push(executive);
        }
    }
    return executives;
}


/***/ }),

/***/ "./chrome/src/linkedin_html_parser_helper.ts":
/*!***************************************************!*\
  !*** ./chrome/src/linkedin_html_parser_helper.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   linkedin_html_parser_json: () => (/* binding */ linkedin_html_parser_json)
/* harmony export */ });
const linkedin_html_parser_json = {
    company_people: {
        executiveDesgination: ["div>section>div>div>div>div>div>div.lt-line-clamp"],
        imgUrl: ["div>section>div>div>div>a>img"],
        listing: [
            "div.artdeco-card>div>div>ul>li",
            "ul > li.org-people-profiles-module__profile-item",
            "ul > li.org-people-profiles-module__profile-ia-item",
        ],
        name: ["div>section>div>div>div>div>a>div.lt-line-clamp"],
        name_link: ["div>section>div>div>div>div>a"],
    },
    connections: {
        executiveDesgination: [
            "span.discover-person-card__occupation.t-14.t-black--light.t-normal",
            "span.mn-connection-card__occupation.t-14.t-black--light.t-normal",
            "div.discover-entity-type-card__info-container > a > span.discover-person-card__occupation.t-14.t-black--light.t-normal",
        ],
        imgUrl: [
            "div.ivm-view-attr__img-wrapper.display-flex > img",
            "a.mn-connection-card__picture > div.presence-entity > img.presence-entity__image",
        ],
        listing: [
            "ul > li.mn-connection-card",
            "ul > li.discover-fluid-entity-list--item",
        ],
        name: [
            "span.mn-connection-card__name.t-16.t-black.t-bold",
            "span.discover-person-card__name.t-16.t-black.t-bold",
            "div.discover-entity-type-card__info-container > a > span.discover-person-card__name.t-16.t-black.t-bold",
        ],
        name_link: [
            "div.mn-connection-card__details>a",
            "div.discover-entity-type-card__info-container > a",
            "div.discover-entity-type-card__info-container > a",
        ],
    },
    facet_connections: {
        executiveDesgination: ["div.search-result__info>p.search-result__truncate"],
        imgUrl: [
            "div.search-result__image-wrapper>a>figure>div>div>div.presence-entity>img",
        ],
        listing: ["div.search-result__info>a>h3>span>span>span.name"],
        name: ["div.search-result__info>a>h3>span>span>span.name"],
        name_link: ["div.search-result__info>a"],
    },
    message_profile: {
        executiveDesgination: [
            "div.msg-s-profile-card >div > div >div.artdeco-entity-lockup__subtitle",
        ],
        imgUrl: ["div.msg-conversation-card__presence-entity > img"],
        listing: ["div.msg-s-profile-card"],
        name: ["div > a.profile-card-one-to-one__profile-link"],
        name_link: ["div > a.profile-card-one-to-one__profile-link"],
    },
    people: {
        executiveDesgination: [
            "div.entity-result__info>p.search-result__truncate>div.entity-result__primary-subtitle",
            "div.entity-result__primary-subtitle",
            'ul[role="list"] > li > div > div > div > div:nth-child(2) > div>div:nth-child(2)',
        ],
        imgUrl: [
            "div.display-flex>a>div>div>img",
            "div.search-result__image-wrapper>a>figure>div>div>div.presence-entity>img",
            "div.entity-result__image-2>div>a>div>div>img",
            "div.entity-result__image>div>a>div>div>img",
            "div.display-flex>div>a>div>div>img",
        ],
        listing: [
            "ul > li.entity-result",
            "ul > li.reusable-search__result-container",
            "ul[role='list'] > li",
        ],
        name: [
            "div.search-result__info>a>h3>span>span>span.name",
            "div>span>span.entity-result__title-text>a>span>span",
            'ul[role="list"] > li > div > div > div > div > div > div > div > span > span > a > span > span[aria-hidden="true"]',
        ],
        name_link: [
            "div.entity-result__info>a",
            "div>span>span.entity-result__title-text>a",
            'ul[role="list"] > li > div > div > div > div > div > div > div > span > span > a ',
        ],
    },
    profile: {
        executiveDesgination: [
            "section.artdeco-card>div>div>div>h2",
            "div.pv-text-details__left-panel >div:nth-child(2)",
        ],
        imgUrl: [
            "div.pv-top-card__non-self-photo-wrapper>button>img",
            "div.presence-entity>img",
        ],
        name: [
            "a.ember-view.pv-text-details__about-this-profile-entrypoint>h1",
            "text-heading-xlarge inline t-24 v-align-middle break-words",
            "RIbnCAsTbWzbdDScQkPGXRrQHSaITKZWQhh inline t-24 v-align-middle break-words",
        ],
        name_link: [
            ".artdeco-entity-lockup > div > .artdeco-entity-lockup__title > a",
            ".result-lockup__profile-info.flex.flex-column > div > dl > dt>a",
        ],
    },
    sales_people: {
        companyName: [
            ".artdeco-entity-lockup > div >div.artdeco-entity-lockup__subtitle > a",
            ".result-lockup__position-company > a.ember-view > span",
        ],
        companyNameLink: [
            ".artdeco-entity-lockup > div >div.artdeco-entity-lockup__subtitle > a",
            ".result-lockup__position-company > a.ember-view",
        ],
        executiveDesgination: [
            ".artdeco-entity-lockup > div >div.artdeco-entity-lockup__subtitle > a",
            ".result-lockup__profile-info.flex.flex-column > div.horizontal-person-entity-lockup-4>dl>dd.result-lockup__highlight-keyword>span.t-14",
        ],
        experience: [
            "div.result-lockup__profile-info.flex.flex-column > div > dl > dd:nth-child(4) > span.t-12.t-black--light:not(.result-lockup__tenure",
        ],
        experienceinrole: [
            ".artdeco-entity-lockup > div >.artdeco-entity-lockup__metadata",
            "div.flex.flex-column > div > dl > dd:nth-child(4) > span.result-lockup__tenure.t-12.t-black--light:nth-child(1)",
        ],
        imgUrl: [".result-lockup__profile-info.flex.flex-column > div > dl > dt>a"],
        listing: [".artdeco-entity-lockup", ".search-results__result-container"],
        location: [
            ".artdeco-entity-lockup > div >.artdeco-entity-lockup__caption > span",
            "div.result-lockup__profile-info.flex.flex-column > div > dl > dd:nth-child(5) > ul > li",
        ],
        name: [
            ".artdeco-entity-lockup > div > .artdeco-entity-lockup__title",
            ".result-lockup__profile-info.flex.flex-column > div > dl > dt",
        ],
        name_link: [
            ".artdeco-entity-lockup > div > .artdeco-entity-lockup__title > a",
            ".result-lockup__profile-info.flex.flex-column > div > dl > dt>a",
        ],
        overallExperience: [
            ".artdeco-entity-lockup > div >.artdeco-entity-lockup__metadata",
            "div.result-lockup__profile-info.flex.flex-column > div > dl > dd:nth-child(4) > span.result-lockup__tenure.t-12.t-black--light:nth-child(2)",
        ],
    },
    sales_profile: {
        companyName: ["div.profile-topcard__summary-position>span>a"],
        companyNameLink: ["div.profile-topcard__summary-position>span>a"],
        executiveDesgination: ["div.profile-topcard-person-entity__content>dl>dd"],
        imgUrl: ["div.presence-entity--size-6>img"],
        name: [
            ".profile-topcard-person-entity-container",
            "span.profile-topcard-person-entity__name",
        ],
    },
    search: {
        executiveDesgination: [
            "div.search-result__info>p.search-result__truncate",
            "div.entity-result__primary-subtitle",
        ],
        imgUrl: [
            "div.display-flex>a>div>div>img",
            "div.search-result__image-wrapper>a>figure>div>div>div.presence-entity>img",
            "div.entity-result__image-2>div>a>div>div>img",
            "div.entity-result__image>div>a>div>div>img",
            "div.display-flex>div>a>div>div>img",
        ],
        listing: [
            "ul > li.search-result",
            "ul > li.reusable-search__result-container",
        ],
        name: [
            "div.search-result__info>a>h3>span>span>span.name",
            "div>span>span.entity-result__title-text>a>span>span",
        ],
        name_link: [
            "div.search-result__info>a",
            "div>span>span.entity-result__title-text>a",
        ],
    },
};


/***/ }),

/***/ "./chrome/src/model.ts":
/*!*****************************!*\
  !*** ./chrome/src/model.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),
/* harmony export */   CSRF_TOKEN: () => (/* binding */ CSRF_TOKEN),
/* harmony export */   Event: () => (/* binding */ Event),
/* harmony export */   GET_EXECUTIVE: () => (/* binding */ GET_EXECUTIVE),
/* harmony export */   LINKED_IN_COMPANY_PAGE: () => (/* binding */ LINKED_IN_COMPANY_PAGE),
/* harmony export */   LINKED_IN_CONNECTION_PAGE: () => (/* binding */ LINKED_IN_CONNECTION_PAGE),
/* harmony export */   LINKED_IN_USER_PAGE: () => (/* binding */ LINKED_IN_USER_PAGE),
/* harmony export */   LinkedInPages: () => (/* binding */ LinkedInPages),
/* harmony export */   LinkedInUrl: () => (/* binding */ LinkedInUrl),
/* harmony export */   SALES_NAVIGATOR_PAGE: () => (/* binding */ SALES_NAVIGATOR_PAGE)
/* harmony export */ });
const GET_EXECUTIVE = "getexecutive";
const BASE_URL = "http://localhost/linkedinextension/";
const SALES_NAVIGATOR_PAGE = "SALES_NAVIGATOR_PAGE";
const LINKED_IN_CONNECTION_PAGE = "LINKED_IN_CONNECTION_PAGE";
const LINKED_IN_USER_PAGE = "LINKED_IN_USER_PAGE";
const LINKED_IN_COMPANY_PAGE = "LINKED_IN_COMPANY_PAGE";
const Event = {
    CONTENT_PAGE: "CONTENT_PAGE",
    POPUP: "POPUP",
    BACKGROUND: "BACKGROUND",
    GET_SALES_PROFILE: "GET_SALES_PROFILE",
    GET_NORMAL_PROFILE: "GET_NORMAL_PROFILE",
    SHOW_DOWNLOAD_CONNECTION: "SHOW_DOWNLOAD_CONNECTION",
};
const LinkedInPages = {
    SALES_NAVIGATOR_LIST: "SALES_NAVIGATOR_PAGE",
    CONNECTION_PAGE: "CONNECTION_PAGE",
    USER_PROFILE: "USER_PROFILE",
    USER_FEED: "USER_FEED",
    COMPANY_PAGE: "COMPANY_PAGE",
    OTHER_PAGE: "OTHER_PAGE",
    CLEAR_ALL_EXECUTIVE: "CLEAR_ALL_EXECUTIVE",
    FACET_CONNECTION: "FACET_CONNECTION",
    PEOPLE: "PEOPLE",
    SEARCH: "SEARCH",
    SALES_NAVIGATOR_PROFILE: "SALES_NAVIGATOR_PROFILE",
};
const LinkedInUrl = {
    SALES_NAVIGATOR_LIST: "https://www.linkedin.com/sales/search/people",
    CONNECTION_URL: "https://www.linkedin.com/mynetwork/",
    SEARCH_URL: "https://www.linkedin.com/search/results/all/",
    HOME: "https://www.linkedin.com/",
    FEED: "https://www.linkedin.com/feed/",
    USER_PROFILE: "https://www.linkedin.com/in/",
    COMPANY_URL: "https://www.linkedin.com/company",
    FACET_CONNECTION: "https://www.linkedin.com/search/results/people/?facetConnectionOf",
    PEOPLE: "https://www.linkedin.com/search/results/people/",
    SALES_NAVIGATOR_PROFILE: "https://www.linkedin.com/sales/people",
};
const CSRF_TOKEN = "csrfToken";


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!***********************************!*\
  !*** ./chrome/src/contentPage.ts ***!
  \***********************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model */ "./chrome/src/model.ts");
/* harmony import */ var _linkedin_sales__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./linkedin-sales */ "./chrome/src/linkedin-sales.ts");
/* harmony import */ var _linkedin_connection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linkedin-connection */ "./chrome/src/linkedin-connection.ts");
/* harmony import */ var _linkedin_company__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./linkedin-company */ "./chrome/src/linkedin-company.ts");
/* harmony import */ var _linkedin_profile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./linkedin-profile */ "./chrome/src/linkedin-profile.ts");
/* harmony import */ var _linkedin_facetConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./linkedin-facetConnection */ "./chrome/src/linkedin-facetConnection.ts");
/* harmony import */ var _linkedin_people__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./linkedin-people */ "./chrome/src/linkedin-people.ts");
/* harmony import */ var _linkedin_search__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./linkedin-search */ "./chrome/src/linkedin-search.ts");
/* harmony import */ var _linkedin_sales_profile__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./linkedin-sales-profile */ "./chrome/src/linkedin-sales-profile.ts");
/* harmony import */ var _linkedin_html_parser_helper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./linkedin_html_parser_helper */ "./chrome/src/linkedin_html_parser_helper.ts");
/* harmony import */ var _linkedin_Api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./linkedin-Api */ "./chrome/src/linkedin-Api.ts");










//import { request } from "http";

var startpage;
var endpage;
var totalPage;
let lastpageSelector = document.querySelector("nav > ol > li:last-child");
let page = 1;
let executives = [];
let executiveDetail = [];
let scrollTimer;
var lastScrollTop = 0;
let lastPageButton = false;
let noScroll = true;
var port;
var linkedinHelper = _linkedin_html_parser_helper__WEBPACK_IMPORTED_MODULE_9__.linkedin_html_parser_json;
// content.js
// window.onload = () => {
//   const url = window.location.toString();
//   if (url.indexOf("?") > 0) {
//     const cleanUrl = url.substring(0, url.indexOf("?"));
//     window.history.replaceState({}, document.title, cleanUrl);
//   }
// };
const currentPage = window?.location?.href;
if (currentPage?.includes("linkedin/company")) {
    chrome.runtime.sendMessage({
        currentPage: currentPage,
        executiveLength: 0,
        executives: [],
        fromPage: "COMPANY_PAGE",
        isReset: false,
        type: "CONTENT_PAGE",
    }, () => { });
}
else {
    chrome.runtime.sendMessage({
        currentPage: currentPage,
        // Other properties can be sent here if needed
    }, () => { });
}
function scrapeCompanyWebsite() {
    const websiteHeader = Array.from(document.querySelectorAll("h3.text-heading-medium")).find((header) => header.textContent?.trim() === "Website");
    if (websiteHeader) {
        const parentDtElement = websiteHeader.closest("dt");
        const websiteElement = parentDtElement?.nextElementSibling?.querySelector("a");
        if (websiteElement) {
            const anchorElement = websiteElement;
            chrome.runtime.sendMessage({ websiteLink: anchorElement.href });
        }
        else {
        }
    }
    else {
        // if (window?.location?.href === LinkedInUrl.USER_PROFILE) {
        //   onLoad();
        // }
    }
}
function scrapeCompanyName() {
    const h1Element = document.querySelector("h1");
    if (h1Element) {
        const companyName = h1Element.textContent?.trim();
        if (isLinkidineHomme) {
            if (localStorage) {
                localStorage?.setItem("prevURL", "https://www.linkedin.com/feed/");
            }
        }
        chrome.runtime.sendMessage({ companyName: companyName });
    }
    else {
    }
}
scrapeCompanyWebsite();
scrapeCompanyName();
if (window?.location?.href === "https://www.linkedin.com/in/me/" ||
    window?.location?.href === "https://www.linkedin.com/in/" ||
    window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.USER_PROFILE)) {
    //setTimeout(() => {
    onLoad();
    // }, 2000);
}
else if (window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.COMPANY_URL)) {
    onLoad();
}
else if (window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.PEOPLE) ||
    window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.CONNECTION_URL)) {
    onLoad();
}
function isSalesNavigatorPage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.SALES_NAVIGATOR_LIST)
        ? true
        : false;
}
function isSalesProfilePage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.SALES_NAVIGATOR_PROFILE)
        ? true
        : false;
}
function isLinkedInConnectionPage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.CONNECTION_URL)
        ? true
        : false;
}
function isLinkedInCompanyPage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.COMPANY_URL)
        ? true
        : false;
}
function isLinkedInProfilePage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.USER_PROFILE)
        ? true
        : false;
}
function isLinkedInFacetConnectPage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.FACET_CONNECTION)
        ? true
        : false;
}
function isLinkedInPeoplePage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.PEOPLE) ? true : false;
}
function isLinkedInSearchPage() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.SEARCH_URL)
        ? true
        : false;
}
function isLinkidineHomme() {
    return window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.FEED) ? true : false;
}
let loadCount = 0;
let documentLoadWait = setInterval(() => {
    loadCount = +1;
    if (document.querySelector(linkedinHelper["profile"].name[0])) {
        clearInterval(documentLoadWait);
        onLoad();
    }
    if (loadCount == 500) {
        onLoad();
    }
}, 100);
if (window?.location?.href ===
    "https://www.linkedin.com/mynetwork/invite-connect/connections/") {
    onLoad();
    chrome.runtime.sendMessage({
        page: _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.USER_PROFILE,
        type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
        executives: [],
    });
    (0,_linkedin_connection__WEBPACK_IMPORTED_MODULE_2__.getExecutiveFromConnectionPage)(document, linkedinHelper["connections"]).then((executiveFromConnectionPage) => {
        if (executiveFromConnectionPage) {
            sendExecutiveToBackground(executiveFromConnectionPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE, false, true);
        }
    });
}
function WaitToLoad() {
    document.onreadystatechange = function () {
        if (document.readyState === "interactive") {
            return true;
        }
    };
    return true;
}
function onLoad() {
    chrome.runtime.sendMessage({
        currentPage: window?.location?.href,
    }, () => { });
    const prevURL = localStorage?.getItem("prevURL");
    if (isLinkedInProfilePage()) {
        if (window?.location?.href !== prevURL) {
            chrome.runtime.sendMessage({
                page: _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.USER_PROFILE,
                type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
                executives: [],
            });
            if (localStorage) {
                localStorage?.setItem("prevURL", window?.location?.href);
            }
            (0,_linkedin_profile__WEBPACK_IMPORTED_MODULE_4__.getExecutiveFromProfilePage)(document, linkedinHelper["profile"]).then((executiveFromProfilePage) => {
                if (executiveFromProfilePage) {
                    sendExecutiveToBackground(executiveFromProfilePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.USER_PROFILE, false);
                }
            });
        }
    }
    if (isSalesNavigatorPage()) {
        (0,_linkedin_sales__WEBPACK_IMPORTED_MODULE_1__.getExecutiveFromSalesPage)(document, linkedinHelper["sales_people"]).then((executiveFromSalesPage) => {
            if (executiveFromSalesPage) {
                sendExecutiveToBackground(executiveFromSalesPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_LIST, false, true, lastpageSelector && lastpageSelector.textContent !== null
                    ? parseInt(lastpageSelector.textContent.replace("…", "").trim())
                    : 1);
            }
        });
    }
    if (isSalesProfilePage()) {
        chrome.runtime.sendMessage({
            page: _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_PROFILE,
            type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
            executives: [],
        });
        (0,_linkedin_sales_profile__WEBPACK_IMPORTED_MODULE_8__.getExecutiveFromSalesProfilePage)(document, linkedinHelper["sales_profile"]).then((executiveFromSalesProfilePage) => {
            if (localStorage) {
                localStorage?.setItem("prevURL", window?.location?.href);
            }
            if (executiveFromSalesProfilePage) {
                sendExecutiveToBackground(executiveFromSalesProfilePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_PROFILE, false);
            }
        });
    }
    if (isLinkedInConnectionPage()) {
        setTimeout(() => {
            // chrome.runtime.sendMessage({
            //   page: LinkedInPages.CONNECTION_PAGE,
            //   type: Event.CONTENT_PAGE,
            //   executives: [],
            // });
            if (isLinkedInConnectionPage()) {
                (0,_linkedin_connection__WEBPACK_IMPORTED_MODULE_2__.getExecutiveFromConnectionPage)(document, linkedinHelper["connections"]).then((executives) => {
                    sendExecutiveToBackground(executives, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE, true);
                });
            }
        }, 1000);
        if (localStorage) {
            localStorage?.setItem("prevURL", window?.location?.href);
        }
        (0,_linkedin_connection__WEBPACK_IMPORTED_MODULE_2__.getExecutiveFromConnectionPage)(document, linkedinHelper["connections"]).then((executiveFromConnectionPage) => {
            if (executiveFromConnectionPage) {
                sendExecutiveToBackground(executiveFromConnectionPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE, true, true);
            }
        });
    }
    if (isLinkedInCompanyPage()) {
        chrome.runtime.sendMessage({
            page: _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.COMPANY_PAGE,
            type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
            executives: [],
        });
        if (localStorage) {
            localStorage?.setItem("prevURL", window?.location?.href);
        }
        setTimeout(() => {
            (0,_linkedin_company__WEBPACK_IMPORTED_MODULE_3__.getExecutiveFromCompanyPage)(document, linkedinHelper["company_people"]).then((executiveFromCompanyPage) => {
                if (executiveFromCompanyPage) {
                    sendExecutiveToBackground(executiveFromCompanyPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.COMPANY_PAGE, false, true);
                }
            });
        }, 3000);
    }
    if (isLinkedInFacetConnectPage()) {
        // chrome.runtime.sendMessage({
        //   page: LinkedInPages.FACET_CONNECTION,
        //   type: Event.CONTENT_PAGE,
        //   executives: [],
        // });
        if (localStorage) {
            localStorage?.setItem("prevURL", window?.location?.href);
        }
        (0,_linkedin_facetConnection__WEBPACK_IMPORTED_MODULE_5__.getExecutiveFromFacetConnectionPage)(document, linkedinHelper["facet_connections"]).then((executiveFromFacetPage) => {
            if (executiveFromFacetPage) {
                sendExecutiveToBackground(executiveFromFacetPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.FACET_CONNECTION, false);
            }
        });
    }
    if (isLinkedInPeoplePage()) {
        // chrome.runtime.sendMessage({
        //   page: LinkedInPages.PEOPLE,
        //   type: Event.CONTENT_PAGE,
        //   executives: [],
        // });
        if (localStorage) {
            localStorage?.setItem("prevURL", window?.location?.href);
        }
        setTimeout(() => {
            (0,_linkedin_people__WEBPACK_IMPORTED_MODULE_6__.getExecutiveFromPeoplePage)(document, linkedinHelper["people"]).then((executiveFromPeoplePage) => {
                if (executiveFromPeoplePage) {
                    sendExecutiveToBackground(executiveFromPeoplePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.PEOPLE, false);
                }
            });
        }, 1000);
    }
    if (isLinkedInSearchPage()) {
        // chrome.runtime.sendMessage({
        //   page: LinkedInPages.SEARCH,
        //   type: Event.CONTENT_PAGE,
        //   executives: [],
        // });
        if (localStorage) {
            localStorage?.setItem("prevURL", window?.location?.href);
        }
        (0,_linkedin_search__WEBPACK_IMPORTED_MODULE_7__.getExecutiveFromSearchPage)(document, linkedinHelper["search"]).then((executiveFromSearchPage) => {
            if (executiveFromSearchPage) {
                sendExecutiveToBackground(executiveFromSearchPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH, false);
            }
        });
    }
}
chrome.runtime.onMessage.addListener(getDataFromPopup());
function getDataFromPopup() {
    return (request, sender, respond) => {
        // addExecutiveFromProfilePage();
        // if (chrome.runtime.lastError) {
        if (request.linkedinParser) {
            linkedinHelper = JSON.parse(request.linkedinParser);
        }
        else {
            if (localStorage) {
                localStorage?.setItem("prevURL", window?.location?.href);
            }
            if (window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.HOME)) {
                const handler = new Promise((resolve, reject) => {
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CLEAR_ALL_EXECUTIVE &&
                        !request.type) {
                        executiveDetail.length = 0;
                        executives.length = 0;
                        resetScroll();
                        chrome.runtime.sendMessage({
                            fromPage: _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CLEAR_ALL_EXECUTIVE,
                        });
                    }
                    // if (request.fromPage === LinkedInPages.USER_PROFILE) {
                    //   if (WaitToLoad()) {
                    //     addExecutiveFromProfilePage();
                    //   }
                    // }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.COMPANY_PAGE &&
                        !request.type) {
                        if (WaitToLoad())
                            (0,_linkedin_company__WEBPACK_IMPORTED_MODULE_3__.getExecutiveFromCompanyPage)(document, linkedinHelper["company_people"]).then((executiveFromCompanyPage) => {
                                if (executiveFromCompanyPage) {
                                    sendExecutiveToBackground(executiveFromCompanyPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.COMPANY_PAGE, true);
                                }
                            });
                    }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.FACET_CONNECTION &&
                        !request.type) {
                        if (WaitToLoad())
                            (0,_linkedin_facetConnection__WEBPACK_IMPORTED_MODULE_5__.getExecutiveFromFacetConnectionPage)(document, linkedinHelper["facet_connections"]).then((executiveFromFacetPage) => {
                                if (executiveFromFacetPage) {
                                    sendExecutiveToBackground(executiveFromFacetPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.FACET_CONNECTION, true);
                                }
                            });
                    }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.PEOPLE && !request.type) {
                        if (WaitToLoad())
                            (0,_linkedin_people__WEBPACK_IMPORTED_MODULE_6__.getExecutiveFromPeoplePage)(document, linkedinHelper["people"]).then((executiveFromPeoplePage) => {
                                if (executiveFromPeoplePage) {
                                    sendExecutiveToBackground(executiveFromPeoplePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.PEOPLE, true);
                                }
                            });
                    }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE &&
                        !request.type) {
                        if (WaitToLoad())
                            (0,_linkedin_connection__WEBPACK_IMPORTED_MODULE_2__.getExecutiveFromConnectionPage)(document, linkedinHelper["connections"]).then((executiveFromConnectionPage) => {
                                if (executiveFromConnectionPage) {
                                    sendExecutiveToBackground(executiveFromConnectionPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE, true);
                                }
                            });
                    }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH && !request.type) {
                        if (WaitToLoad())
                            (0,_linkedin_search__WEBPACK_IMPORTED_MODULE_7__.getExecutiveFromSearchPage)(document, linkedinHelper["search"]).then((executiveFromSearchPage) => {
                                if (executiveFromSearchPage) {
                                    sendExecutiveToBackground(executiveFromSearchPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH, true);
                                }
                            });
                    }
                    if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_PROFILE &&
                        !request.type) {
                        clearInterval(scrollTimer);
                        (0,_linkedin_sales_profile__WEBPACK_IMPORTED_MODULE_8__.getExecutiveFromSalesProfilePage)(document, linkedinHelper["sales_profile"]).then((executivefromSalesProfilePage) => {
                            if (executivefromSalesProfilePage) {
                                sendExecutiveToBackground(executivefromSalesProfilePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_PROFILE, true);
                            }
                        });
                    }
                    if (request.type === _model__WEBPACK_IMPORTED_MODULE_0__.Event.GET_SALES_PROFILE) {
                        if (request.fromPage === _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_LIST) {
                            sendRequest(request.csrfToken, request.url, request.companyProfileCode, request.executive);
                        }
                        else {
                            chrome.runtime.sendMessage({
                                type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.GET_NORMAL_PROFILE,
                                executive: request.executive,
                            });
                        }
                    }
                    if (request && request.start && request.end) {
                        startpage = request.start;
                        endpage = request.end;
                        totalPage = endpage;
                        page = startpage;
                        if (window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.SALES_NAVIGATOR_LIST)) {
                            window.scrollTo(0, 0);
                            let lastPage = lastpageSelector && lastpageSelector.textContent !== null
                                ? lastpageSelector.textContent.replace("…", "").trim()
                                : "1";
                            if (lastPage !== null) {
                                if (endpage <= parseInt(lastPage)) {
                                    if (startpage <= endpage) {
                                        if (document.querySelector("nav > ol > li:nth-child(" + startpage + ") > button")) {
                                            document
                                                .querySelector("nav > ol > li:nth-child(" + startpage + ") > button")
                                                .click();
                                        }
                                        executiveDetail.length = 0;
                                        executives.length = 0;
                                        scrollTimer = setInterval(function () {
                                            window.scrollBy(0, 600);
                                        }, 1220);
                                    }
                                    else {
                                        chrome.runtime.sendMessage({
                                            err: "invalid input",
                                            collectData: "false",
                                        });
                                    }
                                }
                                else {
                                    chrome.runtime.sendMessage({
                                        err: "End page should be less than last page",
                                        collectData: "false",
                                    });
                                }
                            }
                            // Normal Account page scroll --code start
                        }
                        else if (window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.SEARCH_URL) ||
                            window?.location?.href?.includes(_model__WEBPACK_IMPORTED_MODULE_0__.LinkedInUrl.PEOPLE)) {
                            let intervalCount = 0;
                            window.scrollTo(0, document.body.scrollHeight - 300);
                            noScroll = false;
                            if (startpage <= endpage) {
                                let lastPageCheck = setInterval(() => {
                                    if (document.querySelector(".artdeco-pagination__pages > li:last-child")) {
                                        lastpageSelector = document.querySelector(".artdeco-pagination__pages > li:last-child");
                                        window.scrollTo(0, 0);
                                        clearInterval(lastPageCheck);
                                        let lastPage = lastpageSelector && lastpageSelector.textContent !== null
                                            ? lastpageSelector.textContent.replace("…", "").trim()
                                            : "1";
                                        lastPage = parseInt(lastPage, 10).toString();
                                        var filtersQueryParam = {};
                                        var queryParam = {};
                                        if (lastPage >= endpage) {
                                            lastPageButton = true;
                                            let currentUrl = decodeURI(window?.location?.href);
                                            var urlFilter = currentUrl
                                                .split("/?")
                                                .pop()
                                                .split("&origin")[0];
                                            // connectionOf=["ACoAACgkC-YBQGQEdEs48X0wFAw572Vmdq8LalQ"]&network=["F"%2C"S"]
                                            // List(connectionOf->ACoAACgkC-YBQGQEdEs48X0wFAw572Vmdq8LalQ,network->F|S,resultType->PEOPLE)
                                            var paramUrlFilter = urlFilter.split("&");
                                            for (let i = 0; i < paramUrlFilter.length; i++) {
                                                var filterKey = paramUrlFilter[i].split("=")[0];
                                                if (filterKey === "keywords") {
                                                    var keyword = paramUrlFilter[i].split("=")[1];
                                                }
                                                else {
                                                    if (filterKey === "network") {
                                                        var part = paramUrlFilter[i].substring(paramUrlFilter[i].lastIndexOf('["') + 1, paramUrlFilter[i].lastIndexOf('"]'));
                                                        var networkValue = part.split("%2C").join("|");
                                                        filtersQueryParam[filterKey] = networkValue.replace(/"/g, "");
                                                        // filtersQueryParam[filterKey] =
                                                        // paramUrlFilter[i].split("=")[1].split('"')[1] +
                                                        // "|" +
                                                        // paramUrlFilter[i].split("=")[1].split('"')[3];
                                                    }
                                                    else {
                                                        paramUrlFilter[i] = paramUrlFilter[i].replace(/"/g, "");
                                                        part = paramUrlFilter[i].substring(paramUrlFilter[i].lastIndexOf("[") + 1, paramUrlFilter[i].lastIndexOf("]"));
                                                        filtersQueryParam[filterKey] = part
                                                            .split("%2C")
                                                            .join("|");
                                                    }
                                                }
                                            }
                                            // List(connectionOf->ACoAACgkC-YBQGQEdEs48X0wFAw572Vmdq8LalQ,network->F|S,resultType->PEOPLE)
                                            var filters = serialize(filtersQueryParam);
                                            let delayApiCall = 1;
                                            for (let i = startpage; i <= endpage; i++) {
                                                delayApiCall++;
                                                setTimeout(function () {
                                                    let count = (i - 1) * 10;
                                                    noScroll = true;
                                                    if (keyword) {
                                                        queryParam = {
                                                            count: 10,
                                                            filters: "replace",
                                                            keywords: keyword,
                                                            origin: "FACETED_SEARCH",
                                                            q: "all",
                                                            queryContext: "List(spellCorrectionEnabled->true)",
                                                            start: count,
                                                        };
                                                    }
                                                    else {
                                                        queryParam = {
                                                            count: 10,
                                                            filters: "replace",
                                                            origin: "FACETED_SEARCH",
                                                            q: "all",
                                                            queryContext: "List(spellCorrectionEnabled->true)",
                                                            start: count,
                                                        };
                                                    }
                                                    var stringQueryParam = dictToURI(queryParam);
                                                    var stringQueryParam = stringQueryParam.replace("replace", filters);
                                                    if (i == endpage) {
                                                        chrome.runtime.sendMessage({
                                                            pageDataUrl: _linkedin_Api__WEBPACK_IMPORTED_MODULE_10__.LINKEDIN_VOYAGER_BLENDED,
                                                            queryParam: stringQueryParam,
                                                            type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
                                                            stopCollecting: true,
                                                        });
                                                    }
                                                    chrome.runtime.sendMessage({
                                                        pageDataUrl: _linkedin_Api__WEBPACK_IMPORTED_MODULE_10__.LINKEDIN_VOYAGER_BLENDED,
                                                        queryParam: stringQueryParam,
                                                        type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
                                                        stopCollecting: false,
                                                    });
                                                }, delayApiCall * 500);
                                            }
                                        }
                                        else {
                                            noScroll = true;
                                            chrome.runtime.sendMessage({
                                                err: "End Page Should be less than Last Page",
                                                collectData: "false",
                                            });
                                            lastPageButton = false;
                                            totalPage = 0;
                                            page = 1;
                                        }
                                    }
                                    intervalCount++;
                                    if (intervalCount > 50) {
                                        clearInterval(lastPageCheck);
                                        chrome.runtime.sendMessage({
                                            err: "This page don't have pagination",
                                            collectData: "false",
                                        });
                                        totalPage = 0;
                                        page = 1;
                                    }
                                }, 100);
                            }
                            else {
                                chrome.runtime.sendMessage({
                                    err: "invalid input",
                                    collectData: "false",
                                });
                                totalPage = 0;
                                page = 1;
                            }
                        }
                        // Normal Account page scroll --code end
                        else {
                            chrome.runtime.sendMessage({
                                err: "Please refresh the page",
                                collectData: "false",
                            });
                        }
                        if (request.isPopupClosed) {
                            resetScroll();
                        }
                        resolve(`Started Fetching data`);
                    }
                    else {
                        reject("request is empty.");
                    }
                    if (request.isPopupClosed) {
                        resetScroll();
                    }
                });
                handler
                    .then((message) => respond(message))
                    .catch((error) => {
                    return respond(error);
                });
            }
        }
        return true;
    };
}
// function addExecutiveFromProfilePage() {
//   getExecutiveFromProfilePage(document, linkedinHelper["profile"]).then(
//     (executiveFromProfilePage) => {
//       if (executiveFromProfilePage) {
//         sendExecutiveToBackground(
//           executiveFromProfilePage,
//           LinkedInPages.USER_PROFILE,
//           true
//         );
//       }
//     }
//   );
// }
function dictToURI(dict) {
    var str = [];
    for (var p in dict) {
        str.push(encodeURIComponent(p) + "=" + encodeURIComponent(dict[p]));
    }
    return str.join("&");
}
function serialize(obj) {
    var str = [];
    for (var p in obj)
        if (obj.hasOwnProperty(p)) {
            str.push(p + "->" + obj[p]);
        }
    var filterlist = str.join(",");
    if (filterlist) {
        return "List(" + filterlist + ",resultType->PEOPLE)";
    }
    return "List(resultType->PEOPLE)";
}
function resetScroll() {
    clearInterval(scrollTimer);
    lastPageButton = false;
    totalPage = 0;
    page = 1;
}
if (window.innerHeight + window.scrollY >= document.body.scrollHeight ||
    window.innerHeight + window.scrollY > 350) {
    window.scrollTo(0, 0);
}
window.onscroll = function (ev) {
    setTimeout(() => {
        // function onScroll() {
        if (Math.round(window.innerHeight + window.scrollY) >=
            document.body.scrollHeight &&
            noScroll) {
            if (isSalesNavigatorPage()) {
                if (document.readyState === "complete") {
                    if (page <= totalPage) {
                        const list = document.getElementsByClassName("search-results__result-container");
                        if (list.length > 0) {
                            (0,_linkedin_sales__WEBPACK_IMPORTED_MODULE_1__.getExecutiveFromSalesPage)(document, linkedinHelper["sales_people"]).then((executives) => {
                                sendExecutiveToBackground(executives, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_LIST);
                                executiveDetail = [...executiveDetail, ...executives];
                                if (page < totalPage) {
                                    clickNextButton();
                                }
                                else {
                                    chrome.runtime.sendMessage({
                                        stopCollecting: true,
                                    });
                                }
                            });
                        }
                    }
                }
            }
            else if (isLinkedInPeoplePage() || isLinkedInSearchPage()) {
                if (localStorage) {
                    localStorage?.setItem("prevURL", window?.location?.href);
                }
                if (document.readyState === "complete") {
                    if (page <= totalPage) {
                        const list = document.getElementsByClassName("reusable-search__result-container");
                        if (list.length > 0) {
                            (0,_linkedin_search__WEBPACK_IMPORTED_MODULE_7__.getExecutiveFromSearchPage)(document, linkedinHelper["search"]).then((executives) => {
                                sendExecutiveToBackground(executives, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH);
                                executiveDetail = [...executiveDetail, ...executives];
                                // if (page < totalPage) {
                                //   window.scrollTo(0, document.body.scrollHeight - 300);
                                //   let nextButtonCheck = setInterval(() => {
                                //     if (
                                //       document.querySelector<any>(
                                //         ".artdeco-pagination__button--next"
                                //       )
                                //     ) {
                                //       clearInterval(nextButtonCheck);
                                //     }
                                //   }, 100);
                                // } else {
                                //   setTimeout(function () {
                                //     resetScroll();
                                //   }, 2500);
                                //   chrome.runtime.sendMessage({
                                //     stopCollecting: true,
                                //   });
                                // }
                            });
                        }
                    }
                }
            }
        }
        var st = window.pageYOffset || document.documentElement.scrollTop;
        if (st > lastScrollTop && noScroll) {
            if (isSalesNavigatorPage()) {
                (0,_linkedin_sales__WEBPACK_IMPORTED_MODULE_1__.getExecutiveFromSalesPage)(document, linkedinHelper["sales_people"]).then((executives) => {
                    sendExecutiveToBackground(executives, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_LIST, Math.round(window.innerHeight + window.scrollY) >=
                        document.body.scrollHeight);
                });
            }
            if (isLinkedInConnectionPage()) {
                (0,_linkedin_connection__WEBPACK_IMPORTED_MODULE_2__.getExecutiveFromConnectionPage)(document, linkedinHelper["connections"]).then((executives) => {
                    sendExecutiveToBackground(executives, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.CONNECTION_PAGE, Math.round(window.innerHeight + window.scrollY) >=
                        document.body.scrollHeight);
                });
            }
            if (isLinkedInCompanyPage()) {
                (0,_linkedin_company__WEBPACK_IMPORTED_MODULE_3__.getExecutiveFromCompanyPage)(document, linkedinHelper["company_people"]).then((executiveFromCompanyPage) => {
                    if (executiveFromCompanyPage) {
                        sendExecutiveToBackground(executiveFromCompanyPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.COMPANY_PAGE, Math.round(window.innerHeight + window.scrollY) >=
                            document.body.scrollHeight);
                    }
                });
            }
            if (isLinkedInFacetConnectPage()) {
                (0,_linkedin_facetConnection__WEBPACK_IMPORTED_MODULE_5__.getExecutiveFromFacetConnectionPage)(document, linkedinHelper["facet_connections"]).then((getExecutiveFromFacetConnectionPage) => {
                    if (getExecutiveFromFacetConnectionPage) {
                        sendExecutiveToBackground(getExecutiveFromFacetConnectionPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.FACET_CONNECTION, Math.round(window.innerHeight + window.scrollY) >=
                            document.body.scrollHeight);
                    }
                });
            }
            if (isLinkedInPeoplePage()) {
                (0,_linkedin_people__WEBPACK_IMPORTED_MODULE_6__.getExecutiveFromPeoplePage)(document, linkedinHelper["people"]).then((executiveFromPeoplePage) => {
                    if (executiveFromPeoplePage) {
                        sendExecutiveToBackground(executiveFromPeoplePage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.PEOPLE, Math.round(window.innerHeight + window.scrollY) >=
                            document.body.scrollHeight);
                    }
                });
            }
            if (isLinkedInSearchPage()) {
                (0,_linkedin_search__WEBPACK_IMPORTED_MODULE_7__.getExecutiveFromSearchPage)(document, linkedinHelper["search"]).then((executiveFromSearchPage) => {
                    if (executiveFromSearchPage) {
                        sendExecutiveToBackground(executiveFromSearchPage, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH, Math.round(window.innerHeight + window.scrollY) >=
                            document.body.scrollHeight);
                    }
                });
            }
        }
        lastScrollTop = st <= 0 ? 0 : st;
    }, 1000);
};
function clickNextButton() {
    if (document.querySelector('[class="search-results__pagination-next-button"]')) {
        if (page !== totalPage) {
            document
                .querySelector('[class="search-results__pagination-next-button"]')
                .click();
            page++;
        }
        else {
            sendExecutiveToBackground(executiveDetail, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SALES_NAVIGATOR_LIST, true);
            resetScroll();
        }
    }
    else if (document.querySelector(".artdeco-pagination__button--next")) {
        if (page !== totalPage) {
            document.querySelector(".artdeco-pagination__button--next").click();
            page++;
        }
        else {
            sendExecutiveToBackground(executiveDetail, _model__WEBPACK_IMPORTED_MODULE_0__.LinkedInPages.SEARCH, true);
            resetScroll();
        }
    }
    else {
        resetScroll();
    }
}
function sendExecutiveToBackground(executiveForBackground, fromPage, isReset, isLast, lastPage) {
    if (executiveForBackground) {
        let connectionExecutives = [];
        connectionExecutives = executiveForBackground;
        connectionExecutives = Object.values(connectionExecutives.reduce((acc, cur) => Object.assign(acc, { [cur?.id]: cur }), {}));
        chrome.runtime.sendMessage({
            executiveLength: connectionExecutives.length,
            fromPage,
            isReset,
            type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.CONTENT_PAGE,
            executives: connectionExecutives,
            isLast,
            lastPage,
        });
    }
}
async function sendRequest(tks, url, companyProfileCode, executive) {
    const response = await fetch(url, {
        method: "GET",
        headers: {
            "csrf-token": tks,
            accept: "application/json",
            "x-li-page-instance": "urn:li:page:d_flagship3_profile_view_base_contact_details",
            "x-li-track": '{"clientVersion":"1.3.3430","osName":"web","timezoneOffset":1,"deviceFormFactor":"DESKTOP","mpName":"voyager-web"}',
            "x-requested-with": "XMLHttpRequest",
            "x-restli-protocol-version": "2.0.0",
        },
    });
    if (response.ok) {
        response.text().then((result) => {
            chrome.runtime.sendMessage({
                type: _model__WEBPACK_IMPORTED_MODULE_0__.Event.GET_SALES_PROFILE,
                json: JSON.parse(result),
                companyProfileCode: companyProfileCode,
                executive,
            });
        });
    }
}

})();

/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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