{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { __decorate, __metadata } from \"tslib\";\nimport { ChangeDetectorRef } from \"@angular/core\";\nimport { Select, Store } from \"@ngxs/store\";\nimport { PopupState } from \"../../../popup/store/state/popup.state\";\nimport { Observable } from \"rxjs\";\nimport { GetSavedExecutiveList } from \"../../../popup/store/action/company.action\"; // import { environment } from \"src/environments/environment\";\n\nimport { environment } from \"src/environments/environment.prod\";\nimport { LinkedInPages, LinkedInUrl } from \"src/app/constant/value\";\nimport { ShowDownloadConnectionButton, ShowLinkedPeoplePage, ShowLinkedSalesNavigator, ShowLinkedSearchPage } from \"../../../popup/store/action/popup.action\";\nimport { CompanyState } from \"../../../popup/store/state/company.state\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngxs/store\";\nimport * as i2 from \"@angular/material/icon\";\nimport * as i3 from \"@angular/common\";\n\nfunction SaveMenuComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"sync\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SaveMenuComponent_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelementStart(2, \"div\", 11);\n    i0.ɵɵelement(3, \"img\", 12);\n    i0.ɵɵelementStart(4, \"strong\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵelement(7, \"img\", 14);\n    i0.ɵɵelementStart(8, \"span\", 15);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 11);\n    i0.ɵɵelement(11, \"img\", 16);\n    i0.ɵɵelementStart(12, \"span\", 17);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"hr\", 18);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const executive_r2 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate(\"id\", executive_r2.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.full_name) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.full_name : executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.first_name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"title\", (executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title : \"No Job Title Available\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title : \"No Job Title Available\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.company_name, \" \");\n  }\n}\n\nexport class SaveMenuComponent {\n  constructor(cd, store) {\n    this.cd = cd;\n    this.store = store;\n    this.executives = [];\n    this.profiles = [];\n  }\n\n  ngOnInit() {\n    this.executives$.subscribe(executives => {\n      this.executives = executives;\n      this.cd.detectChanges();\n    }); // Dispatch the action to get the saved executive list\n\n    this.store.dispatch(new GetSavedExecutiveList()).subscribe({\n      next: result => {\n        this.processSavedExecutiveList(); // Process the list after loading\n      },\n      error: error => {}\n    });\n  } // New method to process the saved executive list\n\n\n  processSavedExecutiveList() {\n    // Subscribe to the saved executive list and set profiles\n    this.savedExecutiveList$ = this.store.select(state => state.company.savedExecutiveList);\n    this.savedExecutiveList$.subscribe(savedExecutiveList => {\n      // Set profiles when data is received\n      this.profiles = savedExecutiveList?.data || []; // Use empty array as fallback\n      // Trigger change detection to update the view\n\n      this.cd.detectChanges();\n    });\n  }\n\n  viewAllItems() {\n    // window.open(environment.SALEZCONNECT_BASE_API_URL + \"/contact/list\", '_blank');\n    window.open(environment.connectPlusUrl + \"contact/list\", '_blank');\n  }\n\n  checkPageUrl() {\n    chrome.tabs.query({\n      active: true,\n      currentWindow: true\n    }, tabs => {\n      chrome.storage.local.get(\"currentPage\", item => {\n        this.url = item.currentPage;\n\n        if (this.url && this.url.includes(LinkedInUrl.SALES_NAVIGATOR_LIST)) {\n          this.store.dispatch(new ShowDownloadConnectionButton(false));\n          this.sendMessageTobackground(LinkedInPages.SALES_NAVIGATOR_LIST);\n          this.store.dispatch(new ShowLinkedSalesNavigator(true));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.CONNECTION_URL)) {\n          this.sendMessageTobackground(LinkedInPages.CONNECTION_PAGE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.COMPANY_URL)) {\n          this.sendMessageTobackground(LinkedInPages.COMPANY_PAGE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.USER_PROFILE)) {\n          this.sendMessageTobackground(LinkedInPages.USER_PROFILE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.FACET_CONNECTION)) {\n          this.sendMessageTobackground(LinkedInPages.FACET_CONNECTION);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.PEOPLE)) {\n          this.sendMessageTobackground(LinkedInPages.PEOPLE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(true));\n        } else if (this.url && this.url.includes(LinkedInUrl.SEARCH_URL)) {\n          this.sendMessageTobackground(LinkedInPages.SEARCH);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(true));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else if (this.url && this.url.includes(LinkedInUrl.SALES_NAVIGATOR_PROFILE)) {\n          this.sendMessageTobackground(LinkedInPages.SALES_NAVIGATOR_PROFILE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        } else {\n          this.sendMessageTobackground(LinkedInPages.OTHER_PAGE);\n          this.store.dispatch(new ShowLinkedSalesNavigator(false));\n          this.store.dispatch(new ShowLinkedSearchPage(false));\n          this.store.dispatch(new ShowLinkedPeoplePage(false));\n        }\n      });\n    });\n  }\n\n  sendMessageTobackground(fromPage) {\n    return _asyncToGenerator(function* () {\n      chrome.storage.local.get(\"contentPageId\", item => {\n        chrome.tabs.sendMessage(parseInt(item.contentPageId), {\n          fromPage\n        });\n      });\n      /* await bindCallback<any>(\r\n      chrome.tabs.sendMessage(this.tabId, {\r\n        fromPage,\r\n      });\r\n      )().toPromise(); */\n    })();\n  }\n\n}\n\nSaveMenuComponent.ɵfac = function SaveMenuComponent_Factory(t) {\n  return new (t || SaveMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Store));\n};\n\nSaveMenuComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SaveMenuComponent,\n  selectors: [[\"app-save-menu\"]],\n  decls: 18,\n  vars: 4,\n  consts: [[1, \"bold-line\"], [1, \"header\"], [1, \"view-link\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"company-tab\"], [\"id\", \"executives\", 1, \"executivesList\"], [\"class\", \"underline\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [1, \"loading-icon\"], [1, \"underline\", 3, \"id\"], [2, \"width\", \"100%\", \"display\", \"grid\"], [2, \"display\", \"flex\"], [\"src\", \"assets\\\\img\\\\profile.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Contact Name\", 1, \"tooltipData\"], [1, \"primary-font-family\", 2, \"margin-left\", \"10px\"], [\"src\", \"assets\\\\img\\\\Designation.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Job Title\", 1, \"tooltipData\"], [1, \"designation\", \"secondary-font-family\", 2, \"margin-left\", \"10px\", 3, \"title\"], [\"src\", \"assets\\\\img\\\\Company.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Company Name\", 1, \"tooltipData\"], [1, \"company-name\", 2, \"margin-left\", \"10px\"], [1, \"divider\"]],\n  template: function SaveMenuComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"hr\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"h2\");\n      i0.ɵɵtext(3, \"Recently Saved\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"a\", 2);\n      i0.ɵɵlistener(\"click\", function SaveMenuComponent_Template_a_click_4_listener() {\n        return ctx.viewAllItems();\n      });\n      i0.ɵɵtext(5, \" View all \");\n      i0.ɵɵelementStart(6, \"mat-icon\");\n      i0.ɵɵtext(7, \"open_in_new\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, SaveMenuComponent_div_8_Template, 3, 0, \"div\", 3);\n      i0.ɵɵpipe(9, \"async\");\n      i0.ɵɵelementStart(10, \"div\", 4);\n      i0.ɵɵelementStart(11, \"ul\", 5);\n      i0.ɵɵtemplate(12, SaveMenuComponent_li_12_Template, 15, 5, \"li\", 6);\n      i0.ɵɵelement(13, \"br\");\n      i0.ɵɵelement(14, \"br\");\n      i0.ɵɵelement(15, \"br\");\n      i0.ɵɵelement(16, \"br\");\n      i0.ɵɵelement(17, \"br\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(9, 2, ctx.loading$));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.profiles);\n    }\n  },\n  directives: [i2.MatIcon, i3.NgIf, i3.NgForOf],\n  pipes: [i3.AsyncPipe],\n  styles: [\".bold-line[_ngcontent-%COMP%]{border:none;border-top:1px solid #cfc3c3;width:100%;margin:0}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #d4c7c7;padding-bottom:10px;width:100%}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;font-weight:bold;color:#000}.view-link[_ngcontent-%COMP%]{display:flex;color:#d83f87;font-weight:bold;font-size:16px;text-decoration:none;cursor:pointer}.view-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-left:5px;font-size:20px;text-decoration:none;cursor:pointer}ul.executivesList[_ngcontent-%COMP%]{list-style-type:none;margin:0;padding:0}.divider[_ngcontent-%COMP%]{border:0;height:1px;background-color:#ccc;width:100%}.company-tab[_ngcontent-%COMP%]{max-height:615px;overflow-y:auto;scrollbar-width:none;padding:20px}.company-tab[_ngcontent-%COMP%]::-webkit-scrollbar{width:0}.company-tab[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:transparent}.company-name[_ngcontent-%COMP%]{color:#d83f87}.loading-icon[_ngcontent-%COMP%]{animation:rotate 1s linear infinite;vertical-align:middle;color:#d83f87}.loading-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:#fffc;z-index:9999}@keyframes rotate{to{transform:rotate(360deg)}}.executivesList[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:16px;height:20px}.tooltipData[_ngcontent-%COMP%]{cursor:pointer}\"]\n});\n\n__decorate([Select(PopupState.getSelectedExecutives), __metadata(\"design:type\", Observable)], SaveMenuComponent.prototype, \"executives$\", void 0);\n\n__decorate([Select(CompanyState.isLoading), __metadata(\"design:type\", Observable)], SaveMenuComponent.prototype, \"loading$\", void 0);", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/angular/src/app/modules/popup/pages/action/save-menu/save-menu/save-menu.component.ts"], "names": ["__decorate", "__metadata", "ChangeDetectorRef", "Select", "Store", "PopupState", "Observable", "GetSavedExecutiveList", "environment", "LinkedInPages", "LinkedInUrl", "ShowDownloadConnectionButton", "ShowLinkedPeoplePage", "ShowLinkedSalesNavigator", "ShowLinkedSearchPage", "CompanyState", "i0", "i1", "i2", "i3", "SaveMenuComponent_div_8_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "SaveMenuComponent_li_12_Template", "ɵɵelement", "executive_r2", "$implicit", "ɵɵpropertyInterpolate", "id", "ɵɵadvance", "ɵɵtextInterpolate", "contactDetails", "full_name", "first_name", "job_title", "ɵɵtextInterpolate1", "company_name", "SaveMenuComponent", "constructor", "cd", "store", "executives", "profiles", "ngOnInit", "executives$", "subscribe", "detectChanges", "dispatch", "next", "result", "processSavedExecutiveList", "error", "savedExecutiveList$", "select", "state", "company", "savedExecutiveList", "data", "viewAllItems", "window", "open", "connectPlusUrl", "checkPageUrl", "chrome", "tabs", "query", "active", "currentWindow", "storage", "local", "get", "item", "url", "currentPage", "includes", "SALES_NAVIGATOR_LIST", "sendMessageTobackground", "CONNECTION_URL", "CONNECTION_PAGE", "COMPANY_URL", "COMPANY_PAGE", "USER_PROFILE", "FACET_CONNECTION", "PEOPLE", "SEARCH_URL", "SEARCH", "SALES_NAVIGATOR_PROFILE", "OTHER_PAGE", "fromPage", "sendMessage", "parseInt", "contentPageId", "ɵfac", "SaveMenuComponent_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "consts", "template", "SaveMenuComponent_Template", "ɵɵlistener", "SaveMenuComponent_Template_a_click_4_listener", "ɵɵtemplate", "ɵɵpipe", "ɵɵproperty", "ɵɵpipeBind1", "loading$", "directives", "MatIcon", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pipes", "AsyncPipe", "styles", "getSelectedExecutives", "prototype", "isLoading"], "mappings": ";AAAA,SAASA,UAAT,EAAqBC,UAArB,QAAuC,OAAvC;AACA,SAASC,iBAAT,QAAkC,eAAlC;AACA,SAASC,MAAT,EAAiBC,KAAjB,QAA8B,aAA9B;AACA,SAASC,UAAT,QAA2B,wCAA3B;AACA,SAASC,UAAT,QAA2B,MAA3B;AACA,SAASC,qBAAT,QAAsC,4CAAtC,C,CACA;;AACA,SAASC,WAAT,QAA4B,mCAA5B;AACA,SAASC,aAAT,EAAwBC,WAAxB,QAA2C,wBAA3C;AACA,SAASC,4BAAT,EAAuCC,oBAAvC,EAA6DC,wBAA7D,EAAuFC,oBAAvF,QAAoH,0CAApH;AACA,SAASC,YAAT,QAA6B,0CAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;;AACA,SAASC,gCAAT,CAA0CC,EAA1C,EAA8CC,GAA9C,EAAmD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7DL,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAP,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,CAAjC;AACAP,IAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV,EAAa,MAAb;AACAR,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACS,YAAH;AACH;AAAE;;AACH,SAASC,gCAAT,CAA0CL,EAA1C,EAA8CC,GAA9C,EAAmD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7DL,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,CAA3B;AACAP,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAP,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAP,IAAAA,EAAE,CAACW,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACAX,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAP,IAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV;AACAR,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAP,IAAAA,EAAE,CAACW,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACAX,IAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAP,IAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV;AACAR,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACO,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAP,IAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACAX,IAAAA,EAAE,CAACO,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAP,IAAAA,EAAE,CAACQ,MAAH,CAAU,EAAV;AACAR,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACS,YAAH;AACAT,IAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB,EAAuB,EAAvB;AACAX,IAAAA,EAAE,CAACS,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMO,YAAY,GAAGN,GAAG,CAACO,SAAzB;AACAb,IAAAA,EAAE,CAACc,qBAAH,CAAyB,IAAzB,EAA+BF,YAAY,CAACG,EAA5C;AACAf,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,IAAAA,EAAE,CAACiB,iBAAH,CAAqB,CAACL,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BC,SAAxG,IAAqHP,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BC,SAA5N,GAAwOP,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BE,UAApW;AACApB,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,IAAAA,EAAE,CAACc,qBAAH,CAAyB,OAAzB,EAAkC,CAACF,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BG,SAAxG,IAAqHT,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BG,SAA5N,GAAwO,wBAA1Q;AACArB,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,IAAAA,EAAE,CAACsB,kBAAH,CAAsB,GAAtB,EAA2B,CAACV,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BG,SAAxG,IAAqHT,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BG,SAA5N,GAAwO,wBAAnQ,EAA6R,GAA7R;AACArB,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,IAAAA,EAAE,CAACsB,kBAAH,CAAsB,GAAtB,EAA2BV,YAAY,IAAI,IAAhB,GAAuB,IAAvB,GAA8BA,YAAY,CAACM,cAAb,IAA+B,IAA/B,GAAsC,IAAtC,GAA6CN,YAAY,CAACM,cAAb,CAA4BK,YAAlI,EAAgJ,GAAhJ;AACH;AAAE;;AACH,OAAO,MAAMC,iBAAN,CAAwB;AAC3BC,EAAAA,WAAW,CAACC,EAAD,EAAKC,KAAL,EAAY;AACnB,SAAKD,EAAL,GAAUA,EAAV;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKC,WAAL,CAAiBC,SAAjB,CAA4BJ,UAAD,IAAgB;AACvC,WAAKA,UAAL,GAAkBA,UAAlB;AACA,WAAKF,EAAL,CAAQO,aAAR;AACH,KAHD,EADO,CAKP;;AACA,SAAKN,KAAL,CAAWO,QAAX,CAAoB,IAAI3C,qBAAJ,EAApB,EAAiDyC,SAAjD,CAA2D;AACvDG,MAAAA,IAAI,EAAGC,MAAD,IAAY;AACd,aAAKC,yBAAL,GADc,CACoB;AACrC,OAHsD;AAIvDC,MAAAA,KAAK,EAAGA,KAAD,IAAW,CACjB;AALsD,KAA3D;AAOH,GApB0B,CAqB3B;;;AACAD,EAAAA,yBAAyB,GAAG;AACxB;AACA,SAAKE,mBAAL,GAA2B,KAAKZ,KAAL,CAAWa,MAAX,CAAmBC,KAAD,IAAWA,KAAK,CAACC,OAAN,CAAcC,kBAA3C,CAA3B;AACA,SAAKJ,mBAAL,CAAyBP,SAAzB,CAAoCW,kBAAD,IAAwB;AACvD;AACA,WAAKd,QAAL,GAAgBc,kBAAkB,EAAEC,IAApB,IAA4B,EAA5C,CAFuD,CAEP;AAChD;;AACA,WAAKlB,EAAL,CAAQO,aAAR;AACH,KALD;AAMH;;AACDY,EAAAA,YAAY,GAAG;AACX;AACAC,IAAAA,MAAM,CAACC,IAAP,CAAYvD,WAAW,CAACwD,cAAZ,GAA6B,cAAzC,EAAyD,QAAzD;AACH;;AACDC,EAAAA,YAAY,GAAG;AACXC,IAAAA,MAAM,CAACC,IAAP,CAAYC,KAAZ,CAAkB;AAAEC,MAAAA,MAAM,EAAE,IAAV;AAAgBC,MAAAA,aAAa,EAAE;AAA/B,KAAlB,EAA0DH,IAAD,IAAU;AAC/DD,MAAAA,MAAM,CAACK,OAAP,CAAeC,KAAf,CAAqBC,GAArB,CAAyB,aAAzB,EAAyCC,IAAD,IAAU;AAC9C,aAAKC,GAAL,GAAWD,IAAI,CAACE,WAAhB;;AACA,YAAI,KAAKD,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAACoE,oBAA9B,CAAhB,EAAqE;AACjE,eAAKnC,KAAL,CAAWO,QAAX,CAAoB,IAAIvC,4BAAJ,CAAiC,KAAjC,CAApB;AACA,eAAKoE,uBAAL,CAA6BtE,aAAa,CAACqE,oBAA3C;AACA,eAAKnC,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,IAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SAND,MAOK,IAAI,KAAK+D,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAACsE,cAA9B,CAAhB,EAA+D;AAChE,eAAKD,uBAAL,CAA6BtE,aAAa,CAACwE,eAA3C;AACA,eAAKtC,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SALI,MAMA,IAAI,KAAK+D,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAACwE,WAA9B,CAAhB,EAA4D;AAC7D,eAAKH,uBAAL,CAA6BtE,aAAa,CAAC0E,YAA3C;AACA,eAAKxC,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SALI,MAMA,IAAI,KAAK+D,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAAC0E,YAA9B,CAAhB,EAA6D;AAC9D,eAAKL,uBAAL,CAA6BtE,aAAa,CAAC2E,YAA3C;AACA,eAAKzC,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SALI,MAMA,IAAI,KAAK+D,GAAL,IACL,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAAC2E,gBAA9B,CADC,EACgD;AACjD,eAAKN,uBAAL,CAA6BtE,aAAa,CAAC4E,gBAA3C;AACA,eAAK1C,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SANI,MAOA,IAAI,KAAK+D,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAAC4E,MAA9B,CAAhB,EAAuD;AACxD,eAAKP,uBAAL,CAA6BtE,aAAa,CAAC6E,MAA3C;AACA,eAAK3C,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,IAAzB,CAApB;AACH,SALI,MAMA,IAAI,KAAK+D,GAAL,IAAY,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAAC6E,UAA9B,CAAhB,EAA2D;AAC5D,eAAKR,uBAAL,CAA6BtE,aAAa,CAAC+E,MAA3C;AACA,eAAK7C,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,IAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SALI,MAMA,IAAI,KAAK+D,GAAL,IACL,KAAKA,GAAL,CAASE,QAAT,CAAkBnE,WAAW,CAAC+E,uBAA9B,CADC,EACuD;AACxD,eAAKV,uBAAL,CAA6BtE,aAAa,CAACgF,uBAA3C;AACA,eAAK9C,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH,SANI,MAOA;AACD,eAAKmE,uBAAL,CAA6BtE,aAAa,CAACiF,UAA3C;AACA,eAAK/C,KAAL,CAAWO,QAAX,CAAoB,IAAIrC,wBAAJ,CAA6B,KAA7B,CAApB;AACA,eAAK8B,KAAL,CAAWO,QAAX,CAAoB,IAAIpC,oBAAJ,CAAyB,KAAzB,CAApB;AACA,eAAK6B,KAAL,CAAWO,QAAX,CAAoB,IAAItC,oBAAJ,CAAyB,KAAzB,CAApB;AACH;AACJ,OA3DD;AA4DH,KA7DD;AA8DH;;AACKmE,EAAAA,uBAAuB,CAACY,QAAD,EAAW;AAAA;AACpCzB,MAAAA,MAAM,CAACK,OAAP,CAAeC,KAAf,CAAqBC,GAArB,CAAyB,eAAzB,EAA2CC,IAAD,IAAU;AAChDR,QAAAA,MAAM,CAACC,IAAP,CAAYyB,WAAZ,CAAwBC,QAAQ,CAACnB,IAAI,CAACoB,aAAN,CAAhC,EAAsD;AAClDH,UAAAA;AADkD,SAAtD;AAGH,OAJD;AAKA;AACR;AACA;AACA;AACA;AAV4C;AAWvC;;AA/G0B;;AAiH/BnD,iBAAiB,CAACuD,IAAlB,GAAyB,SAASC,yBAAT,CAAmCC,CAAnC,EAAsC;AAAE,SAAO,KAAKA,CAAC,IAAIzD,iBAAV,EAA6BxB,EAAE,CAACkF,iBAAH,CAAqBlF,EAAE,CAACd,iBAAxB,CAA7B,EAAyEc,EAAE,CAACkF,iBAAH,CAAqBjF,EAAE,CAACb,KAAxB,CAAzE,CAAP;AAAkH,CAAnL;;AACAoC,iBAAiB,CAAC2D,IAAlB,GAAyB,aAAcnF,EAAE,CAACoF,iBAAH,CAAqB;AAAEC,EAAAA,IAAI,EAAE7D,iBAAR;AAA2B8D,EAAAA,SAAS,EAAE,CAAC,CAAC,eAAD,CAAD,CAAtC;AAA2DC,EAAAA,KAAK,EAAE,EAAlE;AAAsEC,EAAAA,IAAI,EAAE,CAA5E;AAA+EC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,WAAJ,CAAD,EAAmB,CAAC,CAAD,EAAI,QAAJ,CAAnB,EAAkC,CAAC,CAAD,EAAI,WAAJ,EAAiB,CAAjB,EAAoB,OAApB,CAAlC,EAAgE,CAAC,OAAD,EAAU,mBAAV,EAA+B,CAA/B,EAAkC,MAAlC,CAAhE,EAA2G,CAAC,CAAD,EAAI,aAAJ,CAA3G,EAA+H,CAAC,IAAD,EAAO,YAAP,EAAqB,CAArB,EAAwB,gBAAxB,CAA/H,EAA0K,CAAC,OAAD,EAAU,WAAV,EAAuB,CAAvB,EAA0B,IAA1B,EAAgC,CAAhC,EAAmC,OAAnC,EAA4C,SAA5C,CAA1K,EAAkO,CAAC,CAAD,EAAI,mBAAJ,CAAlO,EAA4P,CAAC,CAAD,EAAI,cAAJ,CAA5P,EAAiR,CAAC,CAAD,EAAI,WAAJ,EAAiB,CAAjB,EAAoB,IAApB,CAAjR,EAA4S,CAAC,CAAD,EAAI,OAAJ,EAAa,MAAb,EAAqB,SAArB,EAAgC,MAAhC,CAA5S,EAAqV,CAAC,CAAD,EAAI,SAAJ,EAAe,MAAf,CAArV,EAA6W,CAAC,KAAD,EAAQ,0BAAR,EAAoC,KAApC,EAA2C,QAA3C,EAAqD,aAArD,EAAoE,SAApE,EAA+E,OAA/E,EAAwF,cAAxF,EAAwG,CAAxG,EAA2G,aAA3G,CAA7W,EAAwe,CAAC,CAAD,EAAI,qBAAJ,EAA2B,CAA3B,EAA8B,aAA9B,EAA6C,MAA7C,CAAxe,EAA8hB,CAAC,KAAD,EAAQ,8BAAR,EAAwC,KAAxC,EAA+C,QAA/C,EAAyD,aAAzD,EAAwE,SAAxE,EAAmF,OAAnF,EAA4F,WAA5F,EAAyG,CAAzG,EAA4G,aAA5G,CAA9hB,EAA0pB,CAAC,CAAD,EAAI,aAAJ,EAAmB,uBAAnB,EAA4C,CAA5C,EAA+C,aAA/C,EAA8D,MAA9D,EAAsE,CAAtE,EAAyE,OAAzE,CAA1pB,EAA6uB,CAAC,KAAD,EAAQ,0BAAR,EAAoC,KAApC,EAA2C,QAA3C,EAAqD,aAArD,EAAoE,SAApE,EAA+E,OAA/E,EAAwF,cAAxF,EAAwG,CAAxG,EAA2G,aAA3G,CAA7uB,EAAw2B,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,aAAvB,EAAsC,MAAtC,CAAx2B,EAAu5B,CAAC,CAAD,EAAI,SAAJ,CAAv5B,CAAvF;AAA+/BC,EAAAA,QAAQ,EAAE,SAASC,0BAAT,CAAoCtF,EAApC,EAAwCC,GAAxC,EAA6C;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACxnCL,MAAAA,EAAE,CAACW,SAAH,CAAa,CAAb,EAAgB,IAAhB,EAAsB,CAAtB;AACAX,MAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAP,MAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAP,MAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV,EAAa,gBAAb;AACAR,MAAAA,EAAE,CAACS,YAAH;AACAT,MAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,CAA1B;AACAP,MAAAA,EAAE,CAAC4F,UAAH,CAAc,OAAd,EAAuB,SAASC,6CAAT,GAAyD;AAAE,eAAOvF,GAAG,CAACuC,YAAJ,EAAP;AAA4B,OAA9G;AACA7C,MAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV,EAAa,YAAb;AACAR,MAAAA,EAAE,CAACO,cAAH,CAAkB,CAAlB,EAAqB,UAArB;AACAP,MAAAA,EAAE,CAACQ,MAAH,CAAU,CAAV,EAAa,aAAb;AACAR,MAAAA,EAAE,CAACS,YAAH;AACAT,MAAAA,EAAE,CAACS,YAAH;AACAT,MAAAA,EAAE,CAACS,YAAH;AACAT,MAAAA,EAAE,CAAC8F,UAAH,CAAc,CAAd,EAAiB1F,gCAAjB,EAAmD,CAAnD,EAAsD,CAAtD,EAAyD,KAAzD,EAAgE,CAAhE;AACAJ,MAAAA,EAAE,CAAC+F,MAAH,CAAU,CAAV,EAAa,OAAb;AACA/F,MAAAA,EAAE,CAACO,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAP,MAAAA,EAAE,CAACO,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,CAA5B;AACAP,MAAAA,EAAE,CAAC8F,UAAH,CAAc,EAAd,EAAkBpF,gCAAlB,EAAoD,EAApD,EAAwD,CAAxD,EAA2D,IAA3D,EAAiE,CAAjE;AACAV,MAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAX,MAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAX,MAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAX,MAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAX,MAAAA,EAAE,CAACW,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAX,MAAAA,EAAE,CAACS,YAAH;AACAT,MAAAA,EAAE,CAACS,YAAH;AACH;;AAAC,QAAIJ,EAAE,GAAG,CAAT,EAAY;AACVL,MAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,MAAAA,EAAE,CAACgG,UAAH,CAAc,MAAd,EAAsBhG,EAAE,CAACiG,WAAH,CAAe,CAAf,EAAkB,CAAlB,EAAqB3F,GAAG,CAAC4F,QAAzB,CAAtB;AACAlG,MAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb;AACAhB,MAAAA,EAAE,CAACgG,UAAH,CAAc,SAAd,EAAyB1F,GAAG,CAACuB,QAA7B;AACH;AAAE,GA/BqD;AA+BnDsE,EAAAA,UAAU,EAAE,CAACjG,EAAE,CAACkG,OAAJ,EAAajG,EAAE,CAACkG,IAAhB,EAAsBlG,EAAE,CAACmG,OAAzB,CA/BuC;AA+BJC,EAAAA,KAAK,EAAE,CAACpG,EAAE,CAACqG,SAAJ,CA/BH;AA+BmBC,EAAAA,MAAM,EAAE,CAAC,89CAAD;AA/B3B,CAArB,CAAvC;;AAgCAzH,UAAU,CAAC,CACPG,MAAM,CAACE,UAAU,CAACqH,qBAAZ,CADC,EAEPzH,UAAU,CAAC,aAAD,EAAgBK,UAAhB,CAFH,CAAD,EAGPkC,iBAAiB,CAACmF,SAHX,EAGsB,aAHtB,EAGqC,KAAK,CAH1C,CAAV;;AAIA3H,UAAU,CAAC,CACPG,MAAM,CAACY,YAAY,CAAC6G,SAAd,CADC,EAEP3H,UAAU,CAAC,aAAD,EAAgBK,UAAhB,CAFH,CAAD,EAGPkC,iBAAiB,CAACmF,SAHX,EAGsB,UAHtB,EAGkC,KAAK,CAHvC,CAAV", "sourcesContent": ["import { __decorate, __metadata } from \"tslib\";\r\nimport { ChangeDetectorRef } from \"@angular/core\";\r\nimport { Select, Store } from \"@ngxs/store\";\r\nimport { PopupState } from \"../../../popup/store/state/popup.state\";\r\nimport { Observable } from \"rxjs\";\r\nimport { GetSavedExecutiveList } from \"../../../popup/store/action/company.action\";\r\n// import { environment } from \"src/environments/environment\";\r\nimport { environment } from \"src/environments/environment.prod\";\r\nimport { LinkedInPages, LinkedInUrl } from \"src/app/constant/value\";\r\nimport { ShowDownloadConnectionButton, ShowLinkedPeoplePage, ShowLinkedSalesNavigator, ShowLinkedSearchPage, } from \"../../../popup/store/action/popup.action\";\r\nimport { CompanyState } from \"../../../popup/store/state/company.state\";\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@ngxs/store\";\r\nimport * as i2 from \"@angular/material/icon\";\r\nimport * as i3 from \"@angular/common\";\r\nfunction SaveMenuComponent_div_8_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"mat-icon\", 8);\r\n    i0.ɵɵtext(2, \"sync\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction SaveMenuComponent_li_12_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"li\", 9);\r\n    i0.ɵɵelementStart(1, \"div\", 10);\r\n    i0.ɵɵelementStart(2, \"div\", 11);\r\n    i0.ɵɵelement(3, \"img\", 12);\r\n    i0.ɵɵelementStart(4, \"strong\", 13);\r\n    i0.ɵɵtext(5);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(6, \"div\", 11);\r\n    i0.ɵɵelement(7, \"img\", 14);\r\n    i0.ɵɵelementStart(8, \"span\", 15);\r\n    i0.ɵɵtext(9);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(10, \"div\", 11);\r\n    i0.ɵɵelement(11, \"img\", 16);\r\n    i0.ɵɵelementStart(12, \"span\", 17);\r\n    i0.ɵɵtext(13);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(14, \"hr\", 18);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const executive_r2 = ctx.$implicit;\r\n    i0.ɵɵpropertyInterpolate(\"id\", executive_r2.id);\r\n    i0.ɵɵadvance(5);\r\n    i0.ɵɵtextInterpolate((executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.full_name) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.full_name : executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.first_name);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵpropertyInterpolate(\"title\", (executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title : \"No Job Title Available\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", (executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title) ? executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.job_title : \"No Job Title Available\", \" \");\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵtextInterpolate1(\" \", executive_r2 == null ? null : executive_r2.contactDetails == null ? null : executive_r2.contactDetails.company_name, \" \");\r\n} }\r\nexport class SaveMenuComponent {\r\n    constructor(cd, store) {\r\n        this.cd = cd;\r\n        this.store = store;\r\n        this.executives = [];\r\n        this.profiles = [];\r\n    }\r\n    ngOnInit() {\r\n        this.executives$.subscribe((executives) => {\r\n            this.executives = executives;\r\n            this.cd.detectChanges();\r\n        });\r\n        // Dispatch the action to get the saved executive list\r\n        this.store.dispatch(new GetSavedExecutiveList()).subscribe({\r\n            next: (result) => {\r\n                this.processSavedExecutiveList(); // Process the list after loading\r\n            },\r\n            error: (error) => {\r\n            }\r\n        });\r\n    }\r\n    // New method to process the saved executive list\r\n    processSavedExecutiveList() {\r\n        // Subscribe to the saved executive list and set profiles\r\n        this.savedExecutiveList$ = this.store.select((state) => state.company.savedExecutiveList);\r\n        this.savedExecutiveList$.subscribe((savedExecutiveList) => {\r\n            // Set profiles when data is received\r\n            this.profiles = savedExecutiveList?.data || []; // Use empty array as fallback\r\n            // Trigger change detection to update the view\r\n            this.cd.detectChanges();\r\n        });\r\n    }\r\n    viewAllItems() {\r\n        // window.open(environment.SALEZCONNECT_BASE_API_URL + \"/contact/list\", '_blank');\r\n        window.open(environment.connectPlusUrl + \"contact/list\", '_blank');\r\n    }\r\n    checkPageUrl() {\r\n        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {\r\n            chrome.storage.local.get(\"currentPage\", (item) => {\r\n                this.url = item.currentPage;\r\n                if (this.url && this.url.includes(LinkedInUrl.SALES_NAVIGATOR_LIST)) {\r\n                    this.store.dispatch(new ShowDownloadConnectionButton(false));\r\n                    this.sendMessageTobackground(LinkedInPages.SALES_NAVIGATOR_LIST);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(true));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url && this.url.includes(LinkedInUrl.CONNECTION_URL)) {\r\n                    this.sendMessageTobackground(LinkedInPages.CONNECTION_PAGE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url && this.url.includes(LinkedInUrl.COMPANY_URL)) {\r\n                    this.sendMessageTobackground(LinkedInPages.COMPANY_PAGE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url && this.url.includes(LinkedInUrl.USER_PROFILE)) {\r\n                    this.sendMessageTobackground(LinkedInPages.USER_PROFILE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url &&\r\n                    this.url.includes(LinkedInUrl.FACET_CONNECTION)) {\r\n                    this.sendMessageTobackground(LinkedInPages.FACET_CONNECTION);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url && this.url.includes(LinkedInUrl.PEOPLE)) {\r\n                    this.sendMessageTobackground(LinkedInPages.PEOPLE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(true));\r\n                }\r\n                else if (this.url && this.url.includes(LinkedInUrl.SEARCH_URL)) {\r\n                    this.sendMessageTobackground(LinkedInPages.SEARCH);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(true));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else if (this.url &&\r\n                    this.url.includes(LinkedInUrl.SALES_NAVIGATOR_PROFILE)) {\r\n                    this.sendMessageTobackground(LinkedInPages.SALES_NAVIGATOR_PROFILE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n                else {\r\n                    this.sendMessageTobackground(LinkedInPages.OTHER_PAGE);\r\n                    this.store.dispatch(new ShowLinkedSalesNavigator(false));\r\n                    this.store.dispatch(new ShowLinkedSearchPage(false));\r\n                    this.store.dispatch(new ShowLinkedPeoplePage(false));\r\n                }\r\n            });\r\n        });\r\n    }\r\n    async sendMessageTobackground(fromPage) {\r\n        chrome.storage.local.get(\"contentPageId\", (item) => {\r\n            chrome.tabs.sendMessage(parseInt(item.contentPageId), {\r\n                fromPage,\r\n            });\r\n        });\r\n        /* await bindCallback<any>(\r\n        chrome.tabs.sendMessage(this.tabId, {\r\n          fromPage,\r\n        });\r\n        )().toPromise(); */\r\n    }\r\n}\r\nSaveMenuComponent.ɵfac = function SaveMenuComponent_Factory(t) { return new (t || SaveMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Store)); };\r\nSaveMenuComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: SaveMenuComponent, selectors: [[\"app-save-menu\"]], decls: 18, vars: 4, consts: [[1, \"bold-line\"], [1, \"header\"], [1, \"view-link\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"company-tab\"], [\"id\", \"executives\", 1, \"executivesList\"], [\"class\", \"underline\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [1, \"loading-icon\"], [1, \"underline\", 3, \"id\"], [2, \"width\", \"100%\", \"display\", \"grid\"], [2, \"display\", \"flex\"], [\"src\", \"assets\\\\img\\\\profile.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Contact Name\", 1, \"tooltipData\"], [1, \"primary-font-family\", 2, \"margin-left\", \"10px\"], [\"src\", \"assets\\\\img\\\\Designation.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Job Title\", 1, \"tooltipData\"], [1, \"designation\", \"secondary-font-family\", 2, \"margin-left\", \"10px\", 3, \"title\"], [\"src\", \"assets\\\\img\\\\Company.svg\", \"alt\", \"Filter\", \"data-toggle\", \"tooltip\", \"title\", \"Company Name\", 1, \"tooltipData\"], [1, \"company-name\", 2, \"margin-left\", \"10px\"], [1, \"divider\"]], template: function SaveMenuComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelement(0, \"hr\", 0);\r\n        i0.ɵɵelementStart(1, \"div\", 1);\r\n        i0.ɵɵelementStart(2, \"h2\");\r\n        i0.ɵɵtext(3, \"Recently Saved\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(4, \"a\", 2);\r\n        i0.ɵɵlistener(\"click\", function SaveMenuComponent_Template_a_click_4_listener() { return ctx.viewAllItems(); });\r\n        i0.ɵɵtext(5, \" View all \");\r\n        i0.ɵɵelementStart(6, \"mat-icon\");\r\n        i0.ɵɵtext(7, \"open_in_new\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(8, SaveMenuComponent_div_8_Template, 3, 0, \"div\", 3);\r\n        i0.ɵɵpipe(9, \"async\");\r\n        i0.ɵɵelementStart(10, \"div\", 4);\r\n        i0.ɵɵelementStart(11, \"ul\", 5);\r\n        i0.ɵɵtemplate(12, SaveMenuComponent_li_12_Template, 15, 5, \"li\", 6);\r\n        i0.ɵɵelement(13, \"br\");\r\n        i0.ɵɵelement(14, \"br\");\r\n        i0.ɵɵelement(15, \"br\");\r\n        i0.ɵɵelement(16, \"br\");\r\n        i0.ɵɵelement(17, \"br\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(9, 2, ctx.loading$));\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.profiles);\r\n    } }, directives: [i2.MatIcon, i3.NgIf, i3.NgForOf], pipes: [i3.AsyncPipe], styles: [\".bold-line[_ngcontent-%COMP%]{border:none;border-top:1px solid #cfc3c3;width:100%;margin:0}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #d4c7c7;padding-bottom:10px;width:100%}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;font-weight:bold;color:#000}.view-link[_ngcontent-%COMP%]{display:flex;color:#d83f87;font-weight:bold;font-size:16px;text-decoration:none;cursor:pointer}.view-link[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-left:5px;font-size:20px;text-decoration:none;cursor:pointer}ul.executivesList[_ngcontent-%COMP%]{list-style-type:none;margin:0;padding:0}.divider[_ngcontent-%COMP%]{border:0;height:1px;background-color:#ccc;width:100%}.company-tab[_ngcontent-%COMP%]{max-height:615px;overflow-y:auto;scrollbar-width:none;padding:20px}.company-tab[_ngcontent-%COMP%]::-webkit-scrollbar{width:0}.company-tab[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:transparent}.company-name[_ngcontent-%COMP%]{color:#d83f87}.loading-icon[_ngcontent-%COMP%]{animation:rotate 1s linear infinite;vertical-align:middle;color:#d83f87}.loading-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:#fffc;z-index:9999}@keyframes rotate{to{transform:rotate(360deg)}}.executivesList[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:16px;height:20px}.tooltipData[_ngcontent-%COMP%]{cursor:pointer}\"] });\r\n__decorate([\r\n    Select(PopupState.getSelectedExecutives),\r\n    __metadata(\"design:type\", Observable)\r\n], SaveMenuComponent.prototype, \"executives$\", void 0);\r\n__decorate([\r\n    Select(CompanyState.isLoading),\r\n    __metadata(\"design:type\", Observable)\r\n], SaveMenuComponent.prototype, \"loading$\", void 0);\r\n"]}, "metadata": {}, "sourceType": "module"}