"use strict";
(self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["angular_src_app_modules_options_options_module_ts"],{

/***/ 4157:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "OptionsModule": function() { return /* binding */ OptionsModule; }
});

// EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/common.js
var common = __webpack_require__(4364);
// EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules
var router = __webpack_require__(24);
// EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js
var core = __webpack_require__(2316);
;// CONCATENATED MODULE: ./angular/src/app/modules/options/options-routing.module.ts



const routes = [
    {
        path: 'https://www.salezshark.com/pricing'
        // component: OptionsComponent
    }
];
class OptionsRoutingModule {
}
OptionsRoutingModule.ɵfac = function OptionsRoutingModule_Factory(t) { return new (t || OptionsRoutingModule)(); };
OptionsRoutingModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: OptionsRoutingModule });
OptionsRoutingModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[router/* RouterModule.forChild */.Bz.forChild(routes)], router/* RouterModule */.Bz] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(OptionsRoutingModule, { imports: [router/* RouterModule */.Bz], exports: [router/* RouterModule */.Bz] }); })();

;// CONCATENATED MODULE: ./angular/src/app/modules/options/pages/options/options.component.ts

class OptionsComponent {
}
OptionsComponent.ɵfac = function OptionsComponent_Factory(t) { return new (t || OptionsComponent)(); };
OptionsComponent.ɵcmp = /*@__PURE__*/ core/* ɵɵdefineComponent */.Xpm({ type: OptionsComponent, selectors: [["app-options"]], decls: 4, vars: 0, consts: [[2, "text-align", "center"]], template: function OptionsComponent_Template(rf, ctx) { if (rf & 1) {
        core/* ɵɵelementStart */.TgZ(0, "h1", 0);
        core/* ɵɵtext */._uU(1, "Options");
        core/* ɵɵelementEnd */.qZA();
        core/* ɵɵelementStart */.TgZ(2, "p", 0);
        core/* ɵɵtext */._uU(3, "You are now on the options page!");
        core/* ɵɵelementEnd */.qZA();
    } }, styles: [""] });

;// CONCATENATED MODULE: ./angular/src/app/modules/options/options.module.ts




class OptionsModule {
}
OptionsModule.ɵfac = function OptionsModule_Factory(t) { return new (t || OptionsModule)(); };
OptionsModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: OptionsModule });
OptionsModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[common/* CommonModule */.ez, OptionsRoutingModule]] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(OptionsModule, { declarations: [OptionsComponent], imports: [common/* CommonModule */.ez, OptionsRoutingModule] }); })();


/***/ })

}]);
//# sourceMappingURL=angular_src_app_modules_options_options_module_ts-esnext.js.map