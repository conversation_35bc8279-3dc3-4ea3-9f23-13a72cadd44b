(self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["main"],{

/***/ 403:
/***/ (function(module) {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(function() {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = function() { return []; };
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 403;
module.exports = webpackEmptyAsyncContext;

/***/ }),

/***/ 2101:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "s": function() { return /* binding */ ApiService; }
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3882);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);



class ApiService {
    constructor(httpClient) {
        this.httpClient = httpClient;
    }
    put(url, payload) {
        return this.httpClient.put(url, payload);
    }
    delete(url, payload) {
        const options = {
            body: payload
        };
        return this.httpClient.request('delete', url, options);
    }
    post(url, payload) {
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM().set('Content-Type', 'application/json');
        const options = {
            headers: httpHeaders
        };
        return this.httpClient.post(url, payload, options);
    }
    patch(url, payload) {
        return this.httpClient.patch(url, payload);
    }
    get(url, payload) {
        const options = {
            params: payload
        };
        return this.httpClient.get(url, options);
    }
    getUsingCsrfToken(url, payload, csrfToken) {
        const options = {
            params: payload,
            headers: {
                'csrf-token': csrfToken
            }
        };
        return this.httpClient.get(url, options);
    }
    sendGetFileRequest(url) {
        let headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM();
        headers = headers.append('Accept', 'text/csv; charset=utf-8');
        return this.httpClient.get(url, {
            headers,
            observe: 'response',
            responseType: 'text'
        });
    }
    putWithHeader(url, payload, dsmID) {
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM().set('dsmID', dsmID);
        const options = {
            headers: httpHeaders
        };
        return this.httpClient.put(url, payload, options);
    }
    postWithHeader(url, payload, dsmID) {
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM().set('dsmID', dsmID);
        const options = {
            headers: httpHeaders
        };
        return this.httpClient.post(url, payload, options);
    }
    getWithHeader(url, dsmID) {
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM({
            'dsmID': dsmID
        });
        const options = {
            headers: httpHeaders
        };
        return this.httpClient.get(url, options);
    }
    getWithHeaderPagination(url, refererUrl, pageNo) {
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpHeaders */ .WM({
            'accept': 'application/vnd.linkedin.normalized+json+2.1',
            // "referer": refererUrl+"&page="+pageNo
        });
        const options = {
            headers: httpHeaders
        };
        return this.httpClient.get(url, options);
    }
    sendHttpRequest(type, url, headers) {
        const options = {
            headers: headers
        };
        return this.httpClient.request(type, url, options);
    }
}
ApiService.ɵfac = function ApiService_Factory(t) { return new (t || ApiService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵinject"] */ .LFG(_angular_common_http__WEBPACK_IMPORTED_MODULE_0__/* .HttpClient */ .eN)); };
ApiService.ɵprov = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdefineInjectable"] */ .Yz7({ token: ApiService, factory: ApiService.ɵfac, providedIn: 'root' });


/***/ }),

/***/ 8598:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "E": function() { return /* binding */ SnackBarComponent; }
/* harmony export */ });
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1900);
/* harmony import */ var _constant_value__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9818);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4364);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2529);






function SnackBarComponent_div_1_Template(rf, ctx) { if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵgetCurrentView"] */ .EpF();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(0, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(1, "mat-icon", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵlistener"] */ .NdJ("click", function SnackBarComponent_div_1_Template_mat_icon_click_1_listener() { _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵrestoreView"] */ .CHM(_r4); const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵnextContext"] */ .oxw(); return ctx_r3.dismiss(); });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtext"] */ ._uU(2, "clear");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
} }
function SnackBarComponent_div_2_Template(rf, ctx) { if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵgetCurrentView"] */ .EpF();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(0, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(1, "mat-icon", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵlistener"] */ .NdJ("click", function SnackBarComponent_div_2_Template_mat_icon_click_1_listener() { _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵrestoreView"] */ .CHM(_r6); const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵnextContext"] */ .oxw(); return ctx_r5.dismiss(); });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtext"] */ ._uU(2, "done");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
} }
function SnackBarComponent_div_3_Template(rf, ctx) { if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵgetCurrentView"] */ .EpF();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(0, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(1, "mat-icon", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵlistener"] */ .NdJ("click", function SnackBarComponent_div_3_Template_mat_icon_click_1_listener() { _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵrestoreView"] */ .CHM(_r8); const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵnextContext"] */ .oxw(); return ctx_r7.dismiss(); });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtext"] */ ._uU(2, "warning");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
} }
class SnackBarComponent {
    constructor(data, snackbarRef) {
        this.data = data;
        this.snackbarRef = snackbarRef;
        this.type = _constant_value__WEBPACK_IMPORTED_MODULE_0__/* .SNACK_BAR_TYPE */ .cx;
    }
    ngOnInit() { }
    dismiss() {
        this.snackbarRef.dismiss();
    }
}
SnackBarComponent.ɵfac = function SnackBarComponent_Factory(t) { return new (t || SnackBarComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdirectiveInject"] */ .Y36(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__/* .MAT_SNACK_BAR_DATA */ .qD), _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdirectiveInject"] */ .Y36(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__/* .MatSnackBarRef */ .OX)); };
SnackBarComponent.ɵcmp = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdefineComponent"] */ .Xpm({ type: SnackBarComponent, selectors: [["app-snack-bar"]], decls: 6, vars: 5, consts: [[3, "ngSwitch"], ["class", "tick-background error", 4, "ngSwitchCase"], ["class", "tick-background success", 4, "ngSwitchCase"], ["class", "tick-background warn", 4, "ngSwitchCase"], [1, "message"], [1, "tick-background", "error"], [3, "click"], [1, "tick-background", "success"], [1, "tick-background", "warn"]], template: function SnackBarComponent_Template(rf, ctx) { if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementContainerStart"] */ .ynx(0, 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtemplate"] */ .YNc(1, SnackBarComponent_div_1_Template, 3, 0, "div", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtemplate"] */ .YNc(2, SnackBarComponent_div_2_Template, 3, 0, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtemplate"] */ .YNc(3, SnackBarComponent_div_3_Template, 3, 0, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementContainerEnd"] */ .BQk();
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementStart"] */ .TgZ(4, "p", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtext"] */ ._uU(5);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵelementEnd"] */ .qZA();
    } if (rf & 2) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵproperty"] */ .Q6J("ngSwitch", ctx.data.type);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵadvance"] */ .xp6(1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵproperty"] */ .Q6J("ngSwitchCase", ctx.type.ERROR);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵadvance"] */ .xp6(1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵproperty"] */ .Q6J("ngSwitchCase", ctx.type.SUCCESS);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵadvance"] */ .xp6(1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵproperty"] */ .Q6J("ngSwitchCase", ctx.type.WARN);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵadvance"] */ .xp6(2);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵtextInterpolate1"] */ .hij(" ", ctx.data.message, "\n");
    } }, directives: [_angular_common__WEBPACK_IMPORTED_MODULE_3__/* .NgSwitch */ .RF, _angular_common__WEBPACK_IMPORTED_MODULE_3__/* .NgSwitchCase */ .n9, _angular_material_icon__WEBPACK_IMPORTED_MODULE_4__/* .MatIcon */ .Hw], styles: [".tick-background[_ngcontent-%COMP%]{border-radius:4px 0 0 4px;overflow:hidden;text-overflow:ellipsis;margin-right:14px;float:left}.tick-background.success[_ngcontent-%COMP%]{background-color:#7ed321}.tick-background.error[_ngcontent-%COMP%]{background-color:#d31f19}.tick-background.warn[_ngcontent-%COMP%]{background-color:#ffc058}.tick-background[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{padding:10px;margin-bottom:0;width:45px;min-height:60px;color:#fff;cursor:pointer;height:auto}.message[_ngcontent-%COMP%]{color:#000;font-size:12px;padding:12px;word-break:break-all;margin-bottom:0;white-space:pre-wrap}"] });


/***/ }),

/***/ 4486:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Y": function() { return /* binding */ SnackBarModule; }
/* harmony export */ });
/* harmony import */ var _snack_bar_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8598);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2529);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4364);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1900);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);





class SnackBarModule {
}
SnackBarModule.ɵfac = function SnackBarModule_Factory(t) { return new (t || SnackBarModule)(); };
SnackBarModule.ɵmod = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdefineNgModule"] */ .oAB({ type: SnackBarModule });
SnackBarModule.ɵinj = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdefineInjector"] */ .cJS({ imports: [[
            _angular_material_icon__WEBPACK_IMPORTED_MODULE_2__/* .MatIconModule */ .Ps,
            _angular_common__WEBPACK_IMPORTED_MODULE_3__/* .CommonModule */ .ez,
            _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__/* .MatSnackBarModule */ .ZX
        ], _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__/* .MatSnackBarModule */ .ZX] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵsetNgModuleScope"] */ .kYT(SnackBarModule, { declarations: [_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__/* .SnackBarComponent */ .E], imports: [_angular_material_icon__WEBPACK_IMPORTED_MODULE_2__/* .MatIconModule */ .Ps,
        _angular_common__WEBPACK_IMPORTED_MODULE_3__/* .CommonModule */ .ez,
        _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__/* .MatSnackBarModule */ .ZX], exports: [_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__/* .SnackBarComponent */ .E, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__/* .MatSnackBarModule */ .ZX] }); })();


/***/ }),

/***/ 6718:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "o": function() { return /* binding */ SnackbarService; }
/* harmony export */ });
/* harmony import */ var _snack_bar_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8598);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);
/* harmony import */ var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1900);




class SnackbarService {
    constructor(snackBar) {
        this.snackBar = snackBar;
    }
    openSnackBar(message, time, type) {
        return this.snackBar.openFromComponent(_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__/* .SnackBarComponent */ .E, {
            duration: time,
            verticalPosition: "top",
            horizontalPosition: "center",
            panelClass: ["snackbar"],
            data: { message, type },
        });
    }
}
SnackbarService.ɵfac = function SnackbarService_Factory(t) { return new (t || SnackbarService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵinject"] */ .LFG(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__/* .MatSnackBar */ .ux)); };
SnackbarService.ɵprov = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_1__/* ["ɵɵdefineInjectable"] */ .Yz7({ token: SnackbarService, factory: SnackbarService.ɵfac, providedIn: "root" });


/***/ }),

/***/ 5502:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "C2": function() { return /* binding */ LOGIN_API; },
/* harmony export */   "WH": function() { return /* binding */ LOGOUT_API; },
/* harmony export */   "Rl": function() { return /* binding */ SIGNUP_API; },
/* harmony export */   "H5": function() { return /* binding */ IS_EMAIL_EXIST_API; },
/* harmony export */   "eB": function() { return /* binding */ VERIFY_EMAIL_API; },
/* harmony export */   "R5": function() { return /* binding */ USER_PROFILE_API; },
/* harmony export */   "cs": function() { return /* binding */ UPDATE_PASSWORD_API; },
/* harmony export */   "GF": function() { return /* binding */ FORGOT_PASSWORD_API; },
/* harmony export */   "TT": function() { return /* binding */ GET_USER_SETUP_DETAILS; },
/* harmony export */   "NB": function() { return /* binding */ GET_EMAIL_API; },
/* harmony export */   "_3": function() { return /* binding */ GET_EXECUTIVE_STATUS; },
/* harmony export */   "JE": function() { return /* binding */ GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API; },
/* harmony export */   "S4": function() { return /* binding */ LINKED_IN_URL; },
/* harmony export */   "xY": function() { return /* binding */ GMAIL_URL; },
/* harmony export */   "XT": function() { return /* binding */ PROFILE_VIEW_URL; },
/* harmony export */   "hT": function() { return /* binding */ COMPANY_URL; },
/* harmony export */   "xK": function() { return /* binding */ GET_COMPANY_DETAILS_API; },
/* harmony export */   "Z9": function() { return /* binding */ GET_DROPDOWN_DATA; },
/* harmony export */   "Nk": function() { return /* binding */ DEFAULT_COMPANY_LOGO; },
/* harmony export */   "_8": function() { return /* binding */ COMPANY_LOGO_URL; },
/* harmony export */   "gO": function() { return /* binding */ GET_BACK_TO_YOU; },
/* harmony export */   "Zf": function() { return /* binding */ GET_EXECUTIVE_LIST_OPTIONS; },
/* harmony export */   "U$": function() { return /* binding */ CREATE_EXECUTIVE_LIST; },
/* harmony export */   "vz": function() { return /* binding */ GET_SAVED_EXECUTIVE_LIST; },
/* harmony export */   "E3": function() { return /* binding */ GET_PROFILE_DATA_LIMIT; },
/* harmony export */   "rc": function() { return /* binding */ GET_SEARCH_RESULT_API; },
/* harmony export */   "_H": function() { return /* binding */ GET_FIND_PHONE_EMAIL; },
/* harmony export */   "oP": function() { return /* binding */ GET_COMPANY_KEYEMP; }
/* harmony export */ });
/* unused harmony exports ADMINSERVICEURL, SUBSCRIBEREURL, ADMINURL, VERIFY_UPDATE_MEMBER_DETAILS_API, GET_EXECUTIVE_API, PROFILE_CONTACT_INFO_URL, SALES_PROFILE */
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8135);

const ADMINSERVICEURL = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/admin-service";
const SUBSCRIBEREURL = ADMINSERVICEURL + "/subscriber";
const ADMINURL = ADMINSERVICEURL + "/admin";
const LOGIN_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/login";
const LOGOUT_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/logout";
const SIGNUP_API = SUBSCRIBEREURL + "/signup";
const IS_EMAIL_EXIST_API = SUBSCRIBEREURL + "/isEmailExist";
const VERIFY_EMAIL_API = SUBSCRIBEREURL + "/verifyEmail";
const USER_PROFILE_API = ADMINURL + "/getLoggedInUserProfile";
const UPDATE_PASSWORD_API = SUBSCRIBEREURL + "/updatePassword";
const FORGOT_PASSWORD_API = SUBSCRIBEREURL + "/forgotPassword";
const GET_USER_SETUP_DETAILS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/admin-service/onboard";
const VERIFY_UPDATE_MEMBER_DETAILS_API = ADMINURL + "/verifyAndUpdateMemberDetails";
const GET_EXECUTIVE_API = "getexecutive";
const GET_EMAIL_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.BASE_URL */ .N.BASE_URL + "email";
const GET_EXECUTIVE_STATUS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/email/status";
const GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/getAccessTokenByRefreshToken";
const LINKED_IN_URL = "https://www.linkedin.com/uas/oauth2/authorization?response_type=code&client_id";
const GMAIL_URL = "https://accounts.google.com/o/oauth2/v2/auth?response_type=code&access_type=offline&client_id";
const PROFILE_CONTACT_INFO_URL = "https://www.linkedin.com/voyager/api/identity/profiles/tariq-haq-********/profileContactInfo";
const PROFILE_VIEW_URL = "https://www.linkedin.com/voyager/api/identity/profiles/";
const COMPANY_URL = "https://www.linkedin.com/voyager/api/organization/companies?decorationId=com.linkedin.voyager.deco.organization.web.WebFullCompanyMain-12&q=universalName&universalName=";
const SALES_PROFILE = "https://www.linkedin.com/sales-api/salesApiProfiles/";
const GET_COMPANY_DETAILS_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/discover-intelligent/companyDetails";
const GET_DROPDOWN_DATA = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/discover-service/filter/executive";
const DEFAULT_COMPANY_LOGO = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.DEFULT_LOGO_URL */ .N.DEFULT_LOGO_URL;
const COMPANY_LOGO_URL = "https://logo.clearbit.com/";
const GET_BACK_TO_YOU = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/getBackToYou";
const GET_EXECUTIVE_LIST_OPTIONS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/list-service/list/getAllLists?name=&pageIndex=0&sortBy=&sortOrder=&query=&category=";
const CREATE_EXECUTIVE_LIST = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/discover-intelligent/createContactOrList";
const GET_SAVED_EXECUTIVE_LIST = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/contact-service/contact/getRecentlyCreatedContacts/fromLinkedIn";
const GET_PROFILE_DATA_LIMIT = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/admin-service/admin/checkLinkedinDataLimit";
const GET_SEARCH_RESULT_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/list-service/list/isListNameAvailable?name=";
const GET_FIND_PHONE_EMAIL = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL +
    "/discover-intelligent/email/findEmail";
const GET_COMPANY_KEYEMP = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__/* .environment.SALEZCONNECT_BASE_API_URL */ .N.SALEZCONNECT_BASE_API_URL + "/discover-service/company/executive";


/***/ }),

/***/ 7063:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "xT": function() { return /* binding */ ClientMessage; },
/* harmony export */   "n2": function() { return /* binding */ EMAIL_REQUIRED; },
/* harmony export */   "As": function() { return /* binding */ INVALID_EMAIL; }
/* harmony export */ });
/* unused harmony exports ServerMessage, NO_DATA_IMAGE_PATH, Message */
const ServerMessage = {
    SUCCESS: 'Success!',
    UNAUTHORIZED: 'Invalid Credentials!'
};
const ClientMessage = {
    INTERNAL_SERVER_ERROR: 'Internal server error',
    SESSION_EXPIRED: 'Session Expired',
    ERROR: 'Error Occured!',
    PARSE_ERROR_MESSAGE: 'Error Occur while parsing page,please refresh the page',
    EMAIL_ERROR_MESSAGE: 'Error occured while getting email',
    NO_EMAIL_FOUND: 'No email found',
    REFRESH_MESSAGE: 'Please Refresh the page ',
    SERVER_ERROR: 'Server is down we will get back to you soon!!!',
    SERVER_ERROR_404: 'Not Found (404)',
    CONTACT: 'Your contact has been added',
    CREDITLIMIT: "You don't have enough credits",
    EMAIL_API_ERROR: 'Sorry something wrong at our end, Please try'
};
const NO_DATA_IMAGE_PATH = 'assets/images/No_Data_Illustration.svg';
const Message = {
    SUCCESS: 'Success',
    FAILED: 'Failed',
    SESSION_EXPIRED: 'Session Expired',
};
const EMAIL_REQUIRED = 'Email is required';
const INVALID_EMAIL = 'Invalid email';


/***/ }),

/***/ 1888:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "G": function() { return /* binding */ StatusCode; }
/* harmony export */ });
const StatusCode = {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    NOTFOUND: 404,
    INTERNALSERVERERROR: 500,
    CONTENTFOUND: 302,
    CREATED: 201,
    VALIDATIONFAILED: 422,
    INVALIDREQUEST: 406,
    CONFLICT: 409,
    LINKEXPIRED: 410,
    EMAILSENT: 250,
    UNKNOWN_ERROR: 0,
    NOTMODIFIED: 304,
    CONTACT_CREATION_LIMIT: 426,
    CONTACT_IMPORT_LIMIT_EXCEED: 413,
    CAMPAIGN_LIMIT_EXCEED: 417,
    NO_CONTENT: 204,
    UNPROCESSABLE: 422,
    GATEWAY_TIME_OUT: 504,
    BAD_GATEWAY: 502,
};


/***/ }),

/***/ 9818:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "xE": function() { return /* binding */ DEBOUNCE_TIME; },
/* harmony export */   "_Q": function() { return /* binding */ SNACKBAR_TIME; },
/* harmony export */   "cx": function() { return /* binding */ SNACK_BAR_TYPE; },
/* harmony export */   "TK": function() { return /* binding */ CSRF_TOKEN; },
/* harmony export */   "ju": function() { return /* binding */ Event; },
/* harmony export */   "CS": function() { return /* binding */ SAMPLE_TEST; },
/* harmony export */   "rZ": function() { return /* binding */ SAMPLE_DATA; },
/* harmony export */   "$C": function() { return /* binding */ LinkedInPages; },
/* harmony export */   "hH": function() { return /* binding */ LinkedInUrl; },
/* harmony export */   "me": function() { return /* binding */ DEVICE_TYPE; },
/* harmony export */   "L$": function() { return /* binding */ ButtonType; },
/* harmony export */   "qE": function() { return /* binding */ ButtonSize; }
/* harmony export */ });
/* unused harmony export DIALOG */
const DEBOUNCE_TIME = 600;
const SNACKBAR_TIME = {
    ONE_SECOND: 1000,
    TWO_SECOND: 2000,
    THREE_SECOND: 3000,
    FOUR_SECOND: 4000,
    FIVE_SECOND: 5000,
    TEN_SECOND: 10000,
};
const DIALOG = {
    WIDTH_800: "800px",
    WIDTH_460: "460px",
    WIDTH_520: "520px",
    WIDTH_600: "600px",
    HEIGHT_500: "500px",
    WIDTH_950: "950px",
};
var SNACK_BAR_TYPE;
(function (SNACK_BAR_TYPE) {
    SNACK_BAR_TYPE[SNACK_BAR_TYPE["SUCCESS"] = 0] = "SUCCESS";
    SNACK_BAR_TYPE[SNACK_BAR_TYPE["ERROR"] = 1] = "ERROR";
    SNACK_BAR_TYPE[SNACK_BAR_TYPE["WARN"] = 2] = "WARN";
})(SNACK_BAR_TYPE || (SNACK_BAR_TYPE = {}));
const CSRF_TOKEN = "csrfToken";
const Event = {
    CONTENT_PAGE: "CONTENT_PAGE",
    POPUP: "POPUP",
    BACKGROUND: "BACKGROUND",
    GET_SALES_PROFILE: "GET_SALES_PROFILE",
    GET_NORMAL_PROFILE: "GET_NORMAL_PROFILE",
    SHOW_DOWNLOAD_CONNECTION: "SHOW_DOWNLOAD_CONNECTION",
};
const SAMPLE_TEST = "_qwert_12_90_32_blpy_qwerty_opq_";
const SAMPLE_DATA = "linkedinextension";
const LinkedInPages = {
    SALES_NAVIGATOR_LIST: "SALES_NAVIGATOR_PAGE",
    CONNECTION_PAGE: "CONNECTION_PAGE",
    USER_PROFILE: "USER_PROFILE",
    USER_FEED: "USER_FEED",
    COMPANY_PAGE: "COMPANY_PAGE",
    OTHER_PAGE: "OTHER_PAGE",
    CLEAR_ALL_EXECUTIVE: "CLEAR_ALL_EXECUTIVE",
    FACET_CONNECTION: "FACET_CONNECTION",
    PEOPLE: "PEOPLE",
    SEARCH: "SEARCH",
    SALES_NAVIGATOR_PROFILE: "SALES_NAVIGATOR_PROFILE",
};
const LinkedInUrl = {
    SALES_NAVIGATOR_LIST: "https://www.linkedin.com/sales/search/people",
    CONNECTION_URL: "https://www.linkedin.com/mynetwork/invite-connect/connections/",
    SEARCH_URL: "https://www.linkedin.com/search/results/all/",
    HOME: "https://www.linkedin.com/",
    FEED: "https://www.linkedin.com/feed/",
    USER_PROFILE: "https://www.linkedin.com/in/",
    COMPANY_URL: "https://www.linkedin.com/company",
    FACET_CONNECTION: "https://www.linkedin.com/search/results/people/?facetConnectionOf",
    PEOPLE: "https://www.linkedin.com/search/results/people/",
    SALES_NAVIGATOR_PROFILE: "https://www.linkedin.com/sales/people",
};
const DEVICE_TYPE = "extension";
const ButtonType = {
    PRIMARY: "primary",
    SECONDARY: "secondary",
    TERTIARY: "tertiary",
    DELETE: "delete",
};
const ButtonSize = {
    SMALL: "small",
    STANDARD: "standard",
    LARGE: "large",
    MEDIUM: "medium",
};


/***/ }),

/***/ 9198:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Fs": function() { return /* binding */ LoginWithEmailAndPassword; },
/* harmony export */   "Hw": function() { return /* binding */ LoginWithEmailAndPasswordSuccess; },
/* harmony export */   "SO": function() { return /* binding */ LoginWithEmailAndPasswordFailed; },
/* harmony export */   "af": function() { return /* binding */ SetLoggedIn; },
/* harmony export */   "RD": function() { return /* binding */ Logout; },
/* harmony export */   "ys": function() { return /* binding */ LogoutSuccess; },
/* harmony export */   "HR": function() { return /* binding */ SetAuthData; },
/* harmony export */   "yc": function() { return /* binding */ FetchProfileDetails; },
/* harmony export */   "G_": function() { return /* binding */ FetchProfileDetailsSuccess; },
/* harmony export */   "_S": function() { return /* binding */ FetchProfileDetailsFailed; },
/* harmony export */   "Wf": function() { return /* binding */ GetNewAccessToken; },
/* harmony export */   "xL": function() { return /* binding */ GetNewAccessTokenSuccess; },
/* harmony export */   "cO": function() { return /* binding */ GetNewAccessTokenFailed; },
/* harmony export */   "yT": function() { return /* binding */ GetUserSetupDetails; },
/* harmony export */   "WM": function() { return /* binding */ GetUserSetupDetailsSuccess; },
/* harmony export */   "Ju": function() { return /* binding */ GetUserSetupDetailsFailure; },
/* harmony export */   "$L": function() { return /* binding */ UpdateNewUserSetupDetails; }
/* harmony export */ });
/* unused harmony export ResetAuthResponse */
class LoginWithEmailAndPassword {
    constructor(payload, rememberMe) {
        this.payload = payload;
        this.rememberMe = rememberMe;
    }
}
LoginWithEmailAndPassword.type = '[Login] LoginWithEmailAndPassword';
class LoginWithEmailAndPasswordSuccess {
    constructor(user) {
        this.user = user;
    }
}
LoginWithEmailAndPasswordSuccess.type = '[Login] LoginWithEmailAndPasswordSuccess';
class LoginWithEmailAndPasswordFailed {
    constructor(error) {
        this.error = error;
    }
}
LoginWithEmailAndPasswordFailed.type = '[Login] LoginWithEmailAndPasswordFailed';
class SetLoggedIn {
    constructor(isLoggedIn) {
        this.isLoggedIn = isLoggedIn;
    }
}
SetLoggedIn.type = '[Login] SetLoggedIn';
class Logout {
    constructor(payload) {
        this.payload = payload;
    }
}
Logout.type = '[Auth] Logout';
class LogoutSuccess {
}
LogoutSuccess.type = '[Auth] LogoutSuccess';
class ResetAuthResponse {
}
ResetAuthResponse.type = '[Auth] ResetAuthResponse';
class SetAuthData {
    constructor(authResponse) {
        this.authResponse = authResponse;
    }
}
SetAuthData.type = '[Auth] SetAuthData';
class FetchProfileDetails {
}
FetchProfileDetails.type = '[UserProfile] FetchProfileDetails';
class FetchProfileDetailsSuccess {
    constructor(userProfile) {
        this.userProfile = userProfile;
    }
}
FetchProfileDetailsSuccess.type = '[UserProfile] FetchProfileDetailsSuccess';
class FetchProfileDetailsFailed {
}
FetchProfileDetailsFailed.type = '[UserProfile] FetchProfileDetailsFailed';
class GetNewAccessToken {
    constructor(payload) {
        this.payload = payload;
    }
}
GetNewAccessToken.type = '[Auth] GetNewAccessToken';
class GetNewAccessTokenSuccess {
    constructor(user) {
        this.user = user;
    }
}
GetNewAccessTokenSuccess.type = '[Auth] GetNewAccessTokenSuccess';
class GetNewAccessTokenFailed {
}
GetNewAccessTokenFailed.type = '[Auth] GetNewAccessTokenFailed';
class GetUserSetupDetails {
}
GetUserSetupDetails.type = "[DASHBOARD] GetUserSetupDetails";
class GetUserSetupDetailsSuccess {
    constructor(res) {
        this.res = res;
    }
}
GetUserSetupDetailsSuccess.type = "[DASHBOARD] GetUserSetupDetailsSuccess";
class GetUserSetupDetailsFailure {
}
GetUserSetupDetailsFailure.type = "[DASHBOARD] GetUserSetupDetailsFailure";
class UpdateNewUserSetupDetails {
    constructor(id) {
        this.id = id;
    }
}
UpdateNewUserSetupDetails.type = "[DASHBOARD] UpdateNewUserSetupDetails";


/***/ }),

/***/ 8903:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "r": function() { return /* binding */ LoginService; }
/* harmony export */ });
/* harmony import */ var _constant_api_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5502);
/* harmony import */ var _common_service_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2101);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2316);





class LoginService {
    constructor(apiService) {
        this.apiService = apiService;
    }
    login(payload) {
        return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .LOGIN_API */ .C2, payload);
    }
    logout(payload) {
        return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .LOGOUT_API */ .WH, payload);
    }
    fetchUserProfile() {
        return this.apiService.get(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .USER_PROFILE_API */ .R5, {});
    }
    getNewAccessToken(refreshToken) {
        return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API */ .JE, { refreshToken });
    }
    getUserSetupDetails() {
        return this.apiService.get(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .GET_USER_SETUP_DETAILS */ .TT, {});
    }
    updateNewUserSetupDetails(id) {
        return this.apiService.put(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__/* .GET_USER_SETUP_DETAILS */ .TT + '/' + id + '/true', {});
    }
}
LoginService.ɵfac = function LoginService_Factory(t) { return new (t || LoginService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__/* ["ɵɵinject"] */ .LFG(_common_service_api_service__WEBPACK_IMPORTED_MODULE_1__/* .ApiService */ .s)); };
LoginService.ɵprov = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_2__/* ["ɵɵdefineInjectable"] */ .Yz7({ token: LoginService, factory: LoginService.ɵfac, providedIn: 'root' });


/***/ }),

/***/ 8341:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "U": function() { return /* binding */ ScLoginState; }
});

// EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs
var tslib_es6 = __webpack_require__(2321);
// EXTERNAL MODULE: ./node_modules/@ngxs/store/fesm2015/ngxs-store.js + 3 modules
var ngxs_store = __webpack_require__(6090);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/action/login.action.ts
var login_action = __webpack_require__(9198);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/tap.js + 1 modules
var tap = __webpack_require__(4921);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/catchError.js
var catchError = __webpack_require__(8293);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/service/login.service.ts
var login_service = __webpack_require__(8903);
// EXTERNAL MODULE: ./angular/src/app/constant/status-code.ts
var status_code = __webpack_require__(1888);
// EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.service.ts
var snack_bar_service = __webpack_require__(6718);
// EXTERNAL MODULE: ./angular/src/app/constant/message.ts
var message = __webpack_require__(7063);
// EXTERNAL MODULE: ./angular/src/app/constant/value.ts
var value = __webpack_require__(9818);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/action/popup.action.ts
var popup_action = __webpack_require__(1950);
;// CONCATENATED MODULE: ./angular/src/app/app.state.model.ts
const AppState = {
    AUTH: 'auth',
};

// EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js
var core = __webpack_require__(2316);
;// CONCATENATED MODULE: ./angular/src/app/modules/popup/pages/login/store/state/login.state.ts















let ScLoginState = class ScLoginState {
    constructor(loginService, snackbarService) {
        this.loginService = loginService;
        this.snackbarService = snackbarService;
    }
    static getUserSetupDetailsResponse(state) {
        return state.getUserSetupDetailsResponse;
    }
    static getUserProfile(state) {
        return state.user;
    }
    static getLoginUserDetails(state) {
        return state.authData;
    }
    static isLoggedIn(state) {
        return state.isLoggedIn;
    }
    static isLoginLoading(state) {
        return state.isLoginLoading;
    }
    /**Commands */
    getUserSetupDetails(ctx) {
        return this.loginService.getUserSetupDetails().pipe((0,tap/* tap */.b)(response => {
            if (response.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS &&
                response.data) {
                return ctx.dispatch(new login_action/* GetUserSetupDetailsSuccess */.WM(response));
            }
            else {
                return ctx.dispatch(new login_action/* GetUserSetupDetailsFailure */.Ju());
            }
        }));
    }
    updateNewUserSetupDetails(ctx, action) {
        return this.loginService.updateNewUserSetupDetails(action.id).pipe((0,tap/* tap */.b)(response => {
            if (response.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS) {
            }
        }));
    }
    loginWithEmailPassword(ctx, action) {
        const payload = {
            email: action.payload.email,
            password: action.payload.password,
            deviceType: value/* DEVICE_TYPE */.me
        };
        if (action.rememberMe) {
            ctx.patchState({
                email: action.payload.email
            });
        }
        ctx.patchState({
            isLoginLoading: true
        });
        return this.loginService.login(payload).pipe((0,tap/* tap */.b)(res => {
            if (res.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS && res.data !== null) {
                return ctx.dispatch(new login_action/* LoginWithEmailAndPasswordSuccess */.Hw(res.data));
            }
            else {
                return ctx.dispatch(new login_action/* LoginWithEmailAndPasswordFailed */.SO(res));
            }
        }), (0,catchError/* catchError */.K)(err => {
            return ctx.dispatch(new login_action/* LoginWithEmailAndPasswordFailed */.SO({
                message: 'Error Occured'
            }));
        }));
    }
    logout(ctx, action) {
        return this.loginService.logout(action.payload).pipe((0,tap/* tap */.b)(res => {
            return ctx.dispatch(new login_action/* LogoutSuccess */.ys());
        }));
    }
    /**Events */
    setUserStateOnLoginWithEmailAndPasswordSuccess(ctx, event) {
        ctx.dispatch(new login_action/* FetchProfileDetails */.yc());
        ctx.patchState({
            authData: event.user,
            isLoggedIn: true,
            isLoginLoading: false
        });
        ctx.dispatch(new login_action/* GetUserSetupDetails */.yT());
    }
    setStateOnLogout(ctx) {
        ctx.patchState({
            isLoggedIn: false,
            authData: undefined,
            isLoginLoading: false
        });
        ctx.dispatch(new login_action/* SetLoggedIn */.af(false));
    }
    setStateOnGetNewAccessTokenSuccess(ctx, event) {
        ctx.patchState({
            authData: event.authResponse.data,
            isLoginLoading: false
        });
    }
    setUserStateOnLoginWithEmailAndPasswordFailed(ctx, event) {
        ctx.patchState({
            authData: event.error,
            isLoggedIn: false,
            isLoginLoading: false
        });
    }
    setLoggedIn(ctx, event) {
        if (!event.isLoggedIn) {
            ctx.patchState({
                authData: undefined,
                isLoggedIn: event.isLoggedIn
            });
            ctx.dispatch(new popup_action/* ResetExecutiveList */.x5());
        }
        else {
            ctx.patchState({
                isLoggedIn: event.isLoggedIn
            });
        }
    }
    fetchUserProfile(ctx) {
        return this.loginService.fetchUserProfile().pipe((0,tap/* tap */.b)((res) => {
            if (res.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS && res.data !== null) {
                return ctx.dispatch(new login_action/* FetchProfileDetailsSuccess */.G_(res.data));
            }
        }), (0,catchError/* catchError */.K)(err => {
            return ctx.dispatch(new login_action/* FetchProfileDetailsFailed */._S());
        }));
    }
    setStateOnUserProfileSuccess(ctx, event) {
        ctx.patchState({
            user: event.userProfile
        });
    }
    setStateOnFetchProfileDetailsFailed(ctx) {
        ctx.patchState({
            user: undefined
        });
    }
    getNewAccessToken(ctx, action) {
        return this.loginService.getNewAccessToken(action.payload).pipe((0,tap/* tap */.b)(res => {
            if (res.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS) {
                const successRes = {
                    accessToken: res.data.accessToken,
                    dsmID: res.data.dsmID,
                    refreshToken: res.data.refreshToken,
                    email: action.payload.email,
                    isRememberMe: action.payload.isRememberMe
                };
                return ctx.dispatch(new login_action/* GetNewAccessTokenSuccess */.xL(successRes));
            }
            else {
                return ctx.dispatch(new login_action/* GetNewAccessTokenFailed */.cO());
            }
        }), (0,catchError/* catchError */.K)(err => {
            return ctx.dispatch(new login_action/* GetNewAccessTokenFailed */.cO());
        }));
    }
    setUserStateOnGetNewAccessTokenSuccess(ctx, event) {
        ctx.patchState({
            authData: event.user
        });
    }
    setUserStateOnGetNewAccessTokenFailed(ctx) {
        this.snackbarService.openSnackBar(message/* ClientMessage.SESSION_EXPIRED */.xT.SESSION_EXPIRED, value/* SNACKBAR_TIME.THREE_SECOND */._Q.THREE_SECOND, value/* SNACK_BAR_TYPE.ERROR */.cx.ERROR);
        ctx.dispatch(new login_action/* SetLoggedIn */.af(false));
    }
    setStateOnGetUserSetupDetailsSuccess(ctx, event) {
        ctx.patchState({
            getUserSetupDetailsResponse: event.res,
        });
        if (event?.res !== null) {
            for (let i = 0; i < event?.res?.data?.length; i++) {
                if ('Install Chrome Extension' === event?.res?.data[i]?.name
                    && !event?.res?.data[i]?.status) {
                    ctx.dispatch(new login_action/* UpdateNewUserSetupDetails */.$L(event?.res?.data[i]?.id));
                    break;
                }
            }
        }
    }
    setStateOnGetUserSetupDetailsFailure(ctx) {
        ctx.patchState({
            getUserSetupDetailsResponse: undefined,
        });
    }
};
ScLoginState.ɵfac = function ScLoginState_Factory(t) { return new (t || ScLoginState)(core/* ɵɵinject */.LFG(login_service/* LoginService */.r), core/* ɵɵinject */.LFG(snack_bar_service/* SnackbarService */.o)); };
ScLoginState.ɵprov = /*@__PURE__*/ core/* ɵɵdefineInjectable */.Yz7({ token: ScLoginState, factory: ScLoginState.ɵfac });
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetUserSetupDetails */.yT),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "getUserSetupDetails", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* UpdateNewUserSetupDetails */.$L),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* UpdateNewUserSetupDetails */.$L]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "updateNewUserSetupDetails", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* LoginWithEmailAndPassword */.Fs),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* LoginWithEmailAndPassword */.Fs]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "loginWithEmailPassword", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* Logout */.RD),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* Logout */.RD]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "logout", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* LoginWithEmailAndPasswordSuccess */.Hw),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* LoginWithEmailAndPasswordSuccess */.Hw]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setUserStateOnLoginWithEmailAndPasswordSuccess", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* LogoutSuccess */.ys),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnLogout", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* SetAuthData */.HR),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* SetAuthData */.HR]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnGetNewAccessTokenSuccess", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* LoginWithEmailAndPasswordFailed */.SO),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* LoginWithEmailAndPasswordFailed */.SO]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setUserStateOnLoginWithEmailAndPasswordFailed", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* SetLoggedIn */.af),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* SetLoggedIn */.af]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setLoggedIn", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* FetchProfileDetails */.yc),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "fetchUserProfile", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* FetchProfileDetailsSuccess */.G_),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* FetchProfileDetailsSuccess */.G_]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnUserProfileSuccess", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* FetchProfileDetailsFailed */._S),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnFetchProfileDetailsFailed", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetNewAccessToken */.Wf),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* GetNewAccessToken */.Wf]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "getNewAccessToken", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetNewAccessTokenSuccess */.xL),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* GetNewAccessTokenSuccess */.xL]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setUserStateOnGetNewAccessTokenSuccess", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetNewAccessTokenFailed */.cO),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setUserStateOnGetNewAccessTokenFailed", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetUserSetupDetailsSuccess */.WM),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object, login_action/* GetUserSetupDetailsSuccess */.WM]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnGetUserSetupDetailsSuccess", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Action */.aU)(login_action/* GetUserSetupDetailsFailure */.Ju),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState.prototype, "setStateOnGetUserSetupDetailsFailure", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Selector */.Qf)(),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState, "getUserSetupDetailsResponse", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Selector */.Qf)(),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState, "getUserProfile", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Selector */.Qf)(),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState, "getLoginUserDetails", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Selector */.Qf)(),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState, "isLoggedIn", null);
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Selector */.Qf)(),
    (0,tslib_es6/* __metadata */.w6)("design:type", Function),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [Object]),
    (0,tslib_es6/* __metadata */.w6)("design:returntype", void 0)
], ScLoginState, "isLoginLoading", null);
ScLoginState = (0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* State */.ZM)({
        name: AppState.AUTH,
        defaults: {
            email: '',
            authData: undefined,
            isLoggedIn: false,
            isLoginLoading: false,
            user: undefined,
            getUserSetupDetailsResponse: undefined
        }
    }),
    (0,tslib_es6/* __metadata */.w6)("design:paramtypes", [login_service/* LoginService */.r,
        snack_bar_service/* SnackbarService */.o])
], ScLoginState);



/***/ }),

/***/ 8233:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "mA": function() { return /* binding */ GetCompanyDetails; },
/* harmony export */   "lQ": function() { return /* binding */ SetCompanyId; },
/* harmony export */   "tc": function() { return /* binding */ GetExecutiveFilterOptions; },
/* harmony export */   "ds": function() { return /* binding */ GetExecutiveListOptions; },
/* harmony export */   "uw": function() { return /* binding */ GetCompanyKeyEmp; },
/* harmony export */   "ct": function() { return /* binding */ FetchEmail; },
/* harmony export */   "Vx": function() { return /* binding */ FetchPhone; },
/* harmony export */   "L5": function() { return /* binding */ SetSourceId; },
/* harmony export */   "jQ": function() { return /* binding */ SetCompanyExecutives; },
/* harmony export */   "Vt": function() { return /* binding */ FetchLogo; },
/* harmony export */   "HD": function() { return /* binding */ GetBackToYou; },
/* harmony export */   "J0": function() { return /* binding */ SearchListName; },
/* harmony export */   "f2": function() { return /* binding */ CreateExecutiveList; },
/* harmony export */   "aR": function() { return /* binding */ GetSavedExecutiveList; },
/* harmony export */   "x6": function() { return /* binding */ GetProfileEmailLimit; },
/* harmony export */   "Z$": function() { return /* binding */ GetChromeStorageData; },
/* harmony export */   "nY": function() { return /* binding */ GetChromeCompanyStorageData; },
/* harmony export */   "sQ": function() { return /* binding */ IsGetBackToYou; },
/* harmony export */   "ve": function() { return /* binding */ ClearChromeCompanyStorageData; },
/* harmony export */   "c3": function() { return /* binding */ GetAllTheExecutiveId; },
/* harmony export */   "GE": function() { return /* binding */ ClearOldUrl; },
/* harmony export */   "PR": function() { return /* binding */ seniorityFilters; },
/* harmony export */   "cz": function() { return /* binding */ departmentFilters; },
/* harmony export */   "LS": function() { return /* binding */ searchFilters; }
/* harmony export */ });
class GetCompanyDetails {
    constructor(companySourceId, website, companyName, departmentId, executiveLevelId, searchTerm) {
        this.companySourceId = companySourceId;
        this.website = website;
        this.companyName = companyName;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
    }
}
GetCompanyDetails.type = "[Company] Get Details";
class SetCompanyId {
    constructor(companyId) {
        this.companyId = companyId;
    }
}
SetCompanyId.type = "[Company] Set Company ID";
class GetExecutiveFilterOptions {
    constructor() { }
}
GetExecutiveFilterOptions.type = "[Company] Get Executive Filter Options";
class GetExecutiveListOptions {
    constructor() { }
}
GetExecutiveListOptions.type = "[Company] Get Executive List Options";
class GetCompanyKeyEmp {
    constructor(companyId, departmentId, executiveLevelId, searchTerm = "") {
        this.companyId = companyId;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
    }
}
GetCompanyKeyEmp.type = "[Company] Get Executives";
class FetchEmail {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchEmail.type = "[Company] Fetch Email";
class FetchPhone {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchPhone.type = "[Company] Fetch Phone";
class SetSourceId {
    constructor(sourceId, source = [], sourceName = null, firstName = null, lastName = null, domain = null, staffCount = null) {
        this.sourceId = sourceId;
        this.source = source;
        this.sourceName = sourceName;
        this.firstName = firstName;
        this.lastName = lastName;
        this.domain = domain;
        this.staffCount = staffCount;
    }
}
SetSourceId.type = "[Company] Set Source ID";
class SetCompanyExecutives {
    constructor(executives) {
        this.executives = executives;
    }
}
SetCompanyExecutives.type = "[Company] Set Company Executives";
class FetchLogo {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchLogo.type = "[Company] Fetch Logo";
class GetBackToYou {
    constructor(request) {
        this.request = request;
    }
}
GetBackToYou.type = "[Company] Get Back To You";
class SearchListName {
    constructor(name) {
        this.name = name;
    }
}
SearchListName.type = "[Company] Search List Name";
class CreateExecutiveList {
    constructor(payload) {
        this.payload = payload;
    }
}
CreateExecutiveList.type = "[company] create executive list";
class GetSavedExecutiveList {
    constructor() { }
}
GetSavedExecutiveList.type = "[Company] Get Saved Executive List ";
class GetProfileEmailLimit {
    constructor() { }
}
GetProfileEmailLimit.type = "[Company] Get Profile Email Limit";
class GetChromeStorageData {
    constructor(data) {
        this.data = data;
    }
}
GetChromeStorageData.type = "[Company] GetChromeStorageData ";
class GetChromeCompanyStorageData {
    constructor(data) {
        this.data = data;
    }
}
GetChromeCompanyStorageData.type = "[Company] GetChromeCompanyStorageData ";
class IsGetBackToYou {
    constructor(request) {
        this.request = request;
    }
}
IsGetBackToYou.type = "[Company] IsGetBackToYou";
class ClearChromeCompanyStorageData {
}
ClearChromeCompanyStorageData.type = "[Company] Clear Chrome Company Storage Data";
class GetAllTheExecutiveId {
    constructor(request) {
        this.request = request;
    }
}
GetAllTheExecutiveId.type = "[company] GetAllTheExecutiveId";
class ClearOldUrl {
    constructor(request) {
        this.request = request;
    }
}
ClearOldUrl.type = "[company] ClearOldUrl";
class seniorityFilters {
    constructor(request) {
        this.request = request;
    }
}
seniorityFilters.type = "[company] seniorityFilters";
class departmentFilters {
    constructor(request) {
        this.request = request;
    }
}
departmentFilters.type = "[company] departmentFilters";
class searchFilters {
    constructor(request) {
        this.request = request;
    }
}
searchFilters.type = "[company] type";


/***/ }),

/***/ 6136:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "LE": function() { return /* binding */ ExtractCompanyDetails; },
/* harmony export */   "b6": function() { return /* binding */ GetCompanyKeyEmpInExtractAny; },
/* harmony export */   "ct": function() { return /* binding */ FetchEmail; },
/* harmony export */   "Vx": function() { return /* binding */ FetchPhone; },
/* harmony export */   "yX": function() { return /* binding */ SetExtractCompanyExecutives; },
/* harmony export */   "Vt": function() { return /* binding */ FetchLogo; },
/* harmony export */   "Sf": function() { return /* binding */ CallAPI; }
/* harmony export */ });
class ExtractCompanyDetails {
    constructor(request) {
        this.request = request;
    }
}
ExtractCompanyDetails.type = "[ExtractCompany] Extract Company Details";
class GetCompanyKeyEmpInExtractAny {
    constructor(companyId, departmentId, executiveLevelId, searchTerm = "") {
        this.companyId = companyId;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
    }
}
GetCompanyKeyEmpInExtractAny.type = "[ExtractCompany] Get Executives";
class FetchEmail {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchEmail.type = "[ExtractCompany] FetchEmail";
class FetchPhone {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchPhone.type = "[ExtractCompany] FetchPhone";
class SetExtractCompanyExecutives {
    constructor(executives) {
        this.executives = executives;
    }
}
SetExtractCompanyExecutives.type = "[ExtractCompany] Set Company Executives";
class FetchLogo {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchLogo.type = "[ExtractCompany] Fetch Logo";
class CallAPI {
    constructor(payload) {
        this.payload = payload;
    }
}
CallAPI.type = "[ExtractCompany] CallAPI";


/***/ }),

/***/ 1950:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "jq": function() { return /* binding */ StartCollectingData; },
/* harmony export */   "PQ": function() { return /* binding */ ShowExecutiveList; },
/* harmony export */   "Mr": function() { return /* binding */ ShowExecutiveListInBulk; },
/* harmony export */   "hi": function() { return /* binding */ CurrentPageUrl; },
/* harmony export */   "py": function() { return /* binding */ ShowExecutiveEmailId; },
/* harmony export */   "SI": function() { return /* binding */ ShowExecutiveEmailIdSuccess; },
/* harmony export */   "KQ": function() { return /* binding */ ShowExecutiveEmailIdFailed; },
/* harmony export */   "x5": function() { return /* binding */ ResetExecutiveList; },
/* harmony export */   "Ae": function() { return /* binding */ UpdateExecutiveList; },
/* harmony export */   "w6": function() { return /* binding */ ResetDailyLimit; },
/* harmony export */   "A7": function() { return /* binding */ ResetDailyLimitSuccess; },
/* harmony export */   "T2": function() { return /* binding */ ResetDailyLimitFailed; },
/* harmony export */   "xS": function() { return /* binding */ ShowLinkedSalesNavigator; },
/* harmony export */   "rz": function() { return /* binding */ ShowLinkedSearchPage; },
/* harmony export */   "OI": function() { return /* binding */ ShowLinkedPeoplePage; },
/* harmony export */   "pc": function() { return /* binding */ GetExecutiveList; },
/* harmony export */   "Sh": function() { return /* binding */ StoreExecutiveResponse; },
/* harmony export */   "HD": function() { return /* binding */ GetProfileView; },
/* harmony export */   "ZW": function() { return /* binding */ GetProfileViewSuccess; },
/* harmony export */   "iu": function() { return /* binding */ GetProfileViewFailed; },
/* harmony export */   "Me": function() { return /* binding */ GetCompanyDetail; },
/* harmony export */   "fA": function() { return /* binding */ ShowExecutiveEmailIdLoader; },
/* harmony export */   "hr": function() { return /* binding */ ShowExecutiveEmailIdLoaderClose; },
/* harmony export */   "kQ": function() { return /* binding */ ShowMessage; },
/* harmony export */   "Hk": function() { return /* binding */ ShowDownloadConnectionButton; },
/* harmony export */   "MV": function() { return /* binding */ AddExecutive; },
/* harmony export */   "JN": function() { return /* binding */ RemoveExecutive; },
/* harmony export */   "qP": function() { return /* binding */ ClearExecutives; },
/* harmony export */   "i_": function() { return /* binding */ FetchEmailExecutive; },
/* harmony export */   "uy": function() { return /* binding */ FetchPhoneExecutive; },
/* harmony export */   "rs": function() { return /* binding */ ExecutiveChecked; },
/* harmony export */   "_N": function() { return /* binding */ GetExecutivesFromCompany; },
/* harmony export */   "pT": function() { return /* binding */ UpdateExecutivesSource; },
/* harmony export */   "dc": function() { return /* binding */ ExecutiveCheckBox; },
/* harmony export */   "Vt": function() { return /* binding */ FetchLogo; }
/* harmony export */ });
/* unused harmony exports GetAlreadyAdded, GetCompanyDetailSuccess, GetCompanyDetailFailed, GetPaginationaData, GETLatestSelectedExecutives */
class StartCollectingData {
    constructor(isCollecting) {
        this.isCollecting = isCollecting;
    }
}
StartCollectingData.type = "[PopUpAction] StartCollectingData";
class ShowExecutiveList {
    constructor(executives, fromPage) {
        this.executives = executives;
        this.fromPage = fromPage;
    }
}
ShowExecutiveList.type = "[PopUpAction] ShowExecutiveList";
class ShowExecutiveListInBulk {
    constructor(executives, fromPage) {
        this.executives = executives;
        this.fromPage = fromPage;
    }
}
ShowExecutiveListInBulk.type = "[PopUpAction] ShowExecutiveListInBulk";
class CurrentPageUrl {
    constructor(url) {
        this.url = url;
    }
}
CurrentPageUrl.type = "[PopUpAction] CurrentPageUrl";
class GetAlreadyAdded {
    constructor(executiveIdList) {
        this.executiveIdList = executiveIdList;
    }
}
GetAlreadyAdded.type = "[PopUpAction] GetAlreadyAdded";
class ShowExecutiveEmailId {
    constructor(emailRequest) {
        this.emailRequest = emailRequest;
    }
}
ShowExecutiveEmailId.type = "[PopUpAction] ShowExecutiveEmailId";
class ShowExecutiveEmailIdSuccess {
    constructor(emailResponse) {
        this.emailResponse = emailResponse;
    }
}
ShowExecutiveEmailIdSuccess.type = "[PopUpAction] ShowExecutiveEmailIdSuccess";
class ShowExecutiveEmailIdFailed {
    constructor(emailResponse) {
        this.emailResponse = emailResponse;
    }
}
ShowExecutiveEmailIdFailed.type = "[PopUpAction] ShowExecutiveEmailIdFailed";
class ResetExecutiveList {
}
ResetExecutiveList.type = "[PopUpAction] ResetExecutiveList";
class UpdateExecutiveList {
}
UpdateExecutiveList.type = "[PopUpAction] UpdateExecutiveList";
class ResetDailyLimit {
    constructor(from) {
        this.from = from;
    }
}
ResetDailyLimit.type = "[PopUpAction] ResetDailyLimit";
class ResetDailyLimitSuccess {
    constructor(response) {
        this.response = response;
    }
}
ResetDailyLimitSuccess.type = "[PopUpAction] ResetDailyLimitSuccess";
class ResetDailyLimitFailed {
    constructor(response) {
        this.response = response;
    }
}
ResetDailyLimitFailed.type = "[PopUpAction] ResetDailyLimitFailed";
class ShowLinkedSalesNavigator {
    constructor(isShown) {
        this.isShown = isShown;
    }
}
ShowLinkedSalesNavigator.type = "[PopUpAction] ShowLinkedSalesNavigator";
class ShowLinkedSearchPage {
    constructor(isShown) {
        this.isShown = isShown;
    }
}
ShowLinkedSearchPage.type = "[PopUpAction] ShowLinkedSearchPage";
class ShowLinkedPeoplePage {
    constructor(isShown) {
        this.isShown = isShown;
    }
}
ShowLinkedPeoplePage.type = "[PopUpAction] ShowLinkedPeoplePage";
class GetExecutiveList {
    constructor(executiveIdList, isFilterByPhone, isFilterByEmail, isMissingInfoRequested, companyName) {
        this.executiveIdList = executiveIdList;
        this.isFilterByPhone = isFilterByPhone;
        this.isFilterByEmail = isFilterByEmail;
        this.isMissingInfoRequested = isMissingInfoRequested;
        this.companyName = companyName;
    }
}
GetExecutiveList.type = "[PopUpAction] GetExecutiveList";
class StoreExecutiveResponse {
    constructor(response) {
        this.response = response;
    }
}
StoreExecutiveResponse.type = "[PopUpAction] StoreExecutiveResponse";
class GetProfileView {
    constructor(userID, salesResponse, companyProfileCode, executive, csrfToken, filters) {
        this.userID = userID;
        this.salesResponse = salesResponse;
        this.companyProfileCode = companyProfileCode;
        this.executive = executive;
        this.csrfToken = csrfToken;
        this.filters = filters;
    }
}
GetProfileView.type = "[PopUpAction] GetProfileView";
class GetProfileViewSuccess {
    constructor(response) {
        this.response = response;
    }
}
GetProfileViewSuccess.type = "[PopUpAction] GetProfileViewSuccess";
class GetProfileViewFailed {
}
GetProfileViewFailed.type = "[PopUpAction] GetProfileViewFailed";
class GetCompanyDetail {
    constructor(universalName, executiveData, executive, contactInfo, salesProfileResponse, filters, exeutiveSkill, csrfToken) {
        this.universalName = universalName;
        this.executiveData = executiveData;
        this.executive = executive;
        this.contactInfo = contactInfo;
        this.salesProfileResponse = salesProfileResponse;
        this.filters = filters;
        this.exeutiveSkill = exeutiveSkill;
        this.csrfToken = csrfToken;
    }
}
GetCompanyDetail.type = "[PopUpAction] GetCompanyDetail";
class GetCompanyDetailSuccess {
    constructor(res) {
        this.res = res;
    }
}
GetCompanyDetailSuccess.type = "[PopUpAction] GetCompanyDetailSuccess";
class GetCompanyDetailFailed {
}
GetCompanyDetailFailed.type = "[PopUpAction] GetCompanyDetailFailed";
class ShowExecutiveEmailIdLoader {
    constructor(executiveId) {
        this.executiveId = executiveId;
    }
}
ShowExecutiveEmailIdLoader.type = "[PopUpAction] ShowExecutiveEmailIdLoader";
class ShowExecutiveEmailIdLoaderClose {
    constructor(executiveId, message) {
        this.executiveId = executiveId;
        this.message = message;
    }
}
ShowExecutiveEmailIdLoaderClose.type = "[PopUpAction] ShowExecutiveEmailIdLoaderClose";
class ShowMessage {
    constructor(error) {
        this.error = error;
    }
}
ShowMessage.type = "[PopUpAction] ShowMessage";
class ShowDownloadConnectionButton {
    constructor(isShown) {
        this.isShown = isShown;
    }
}
ShowDownloadConnectionButton.type = "[PopUpAction] ShowDownloadConnectionButton";
class GetPaginationaData {
    constructor(count, refererUrl, pageNo, keyword) {
        this.count = count;
        this.refererUrl = refererUrl;
        this.pageNo = pageNo;
        this.keyword = keyword;
    }
}
GetPaginationaData.type = "[PopUpAction] GetPaginationaData";
class AddExecutive {
    constructor(payload) {
        this.payload = payload;
    }
}
AddExecutive.type = "[Executive] Add";
class RemoveExecutive {
    constructor(payload) {
        this.payload = payload;
    }
}
RemoveExecutive.type = "[Executive] Remove";
class ClearExecutives {
}
ClearExecutives.type = "[Executive] Clear";
class FetchEmailExecutive {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchEmailExecutive.type = "[PopUpAction] Fetch Email";
class FetchPhoneExecutive {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchPhoneExecutive.type = "[PopUpAction] Fetch Phone";
class ExecutiveChecked {
    constructor(payload, checked) {
        this.payload = payload;
        this.checked = checked;
    }
}
ExecutiveChecked.type = "[Executive] ExecutiveChecked";
class GetExecutivesFromCompany {
    constructor(payload) {
        this.payload = payload;
    }
}
GetExecutivesFromCompany.type = "[PopUpAction] GetExecutivesFromCompany";
class GETLatestSelectedExecutives {
    constructor(paylaod) {
        this.paylaod = paylaod;
    }
}
GETLatestSelectedExecutives.type = "[PopUpAction] GETLatestSelectedExecutives";
class UpdateExecutivesSource {
    constructor(paylaod) {
        this.paylaod = paylaod;
    }
}
UpdateExecutivesSource.type = "[PopUpAction] UpdateExecutivesSource";
class ExecutiveCheckBox {
    constructor(payload) {
        this.payload = payload;
    }
}
ExecutiveCheckBox.type = "[PopUpAction] ExecutiveCheckBox";
class FetchLogo {
    constructor(payload) {
        this.payload = payload;
    }
}
FetchLogo.type = "[PopUpAction] Fetch Logo";


/***/ }),

/***/ 6549:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "J": function() { return /* binding */ CompanyService; }
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(3882);
/* harmony import */ var _ngxs_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6090);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8117);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1134);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(5871);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(4921);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(8293);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(3927);
/* harmony import */ var _login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8341);
/* harmony import */ var _action_company_action__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8233);
/* harmony import */ var src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5502);
/* harmony import */ var _action_popup_action__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1950);
/* harmony import */ var _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6136);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(2316);












class CompanyService {
    constructor(http, store) {
        this.http = http;
        this.store = store;
    }
    getCompanyDetails(companySourceId, website, companyName) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const requestBody = {
            companySourceId: companySourceId,
            // website: website,
            // companyName: companyName,
        };
        return this.http
            .post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_COMPANY_DETAILS_API */ .xK, requestBody, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const companyId = result?.data?.data?.companyId;
            if (companyId) {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(companyId));
            }
            else {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(null));
            }
        }));
    }
    getExecutiveFilterOptions() {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken) {
            return;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            Authorization: `Bearer ${authData.accessToken}`,
        });
        return this.http
            .get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_DROPDOWN_DATA */ .Z9, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((response) => { }));
    }
    extractCompanyDetails(request) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const requestBody = {
            website: request?.website,
            companyName: request?.companyName,
        };
        return this.http
            .post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_COMPANY_DETAILS_API */ .xK, requestBody, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const companyId = result?.data?.data?.companyId;
            const websiteLogo = request?.data?.data?.website;
            if (companyId) {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(companyId));
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchLogo */ .Vt(websiteLogo));
            }
            else {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(null));
            }
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }));
    }
    findEmailOrPhone(payload) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
            "Content-Type": "application/json",
        });
        return this.http
            .post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_FIND_PHONE_EMAIL */ ._H, payload, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((response) => { }));
    }
    fetchLogo(website) {
        if (!website) {
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .DEFAULT_COMPANY_LOGO */ .Nk);
        }
        const logoUrl = `${src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .COMPANY_LOGO_URL */ ._8}${website}`;
        return this.http.get(logoUrl, { responseType: "blob" }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__/* .map */ .U)(() => logoUrl), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            // If the status is 404, return the default logo
            if (error.status === 404) {
                return (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .DEFAULT_COMPANY_LOGO */ .Nk);
            }
            // Handle other errors or rethrow them
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .DEFAULT_COMPANY_LOGO */ .Nk);
        }));
    }
    getCompanyExecutives(companyId, departmentId, executiveLevelId, searchTerm = "") {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const url = `${src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_COMPANY_KEYEMP */ .oP}/${companyId}/${departmentId}/${executiveLevelId}?searchTerm=${encodeURIComponent(searchTerm)}`;
        return this.http.get(url, { headers: httpHeaders }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const executives = result?.data?.emailPhoneResponses || [];
            this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyExecutives */ .jQ(executives));
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyExecutives */ .jQ([])); // Clear the list on error
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_12__/* .throwError */ ._)(error);
        }));
    }
    getCompanyExecutivesLeyEmp(companyId, departmentId, executiveLevelId, searchTerm = "") {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        // Append the searchTerm to the URL as a query parameter
        const url = `${src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_COMPANY_KEYEMP */ .oP}/${companyId}/${departmentId}/${executiveLevelId}?searchTerm=${encodeURIComponent(searchTerm)}`;
        return this.http.get(url, { headers: httpHeaders }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const executives = result?.data?.emailPhoneResponses || [];
            this.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__/* .SetExtractCompanyExecutives */ .yX(executives));
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            this.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__/* .SetExtractCompanyExecutives */ .yX([])); // Clear the list on error
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_12__/* .throwError */ ._)(error);
        }));
    }
    getBackToYouAPI(request) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const requestBody = {
            firstName: request?.firstName,
            lastName: request?.lastName,
            designation: request?.designation,
            linkedInId: request?.linkedInId,
            companyLinkedInId: request?.companyLinkedInId,
            companyName: request?.companyName,
        };
        return this.http
            .post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_BACK_TO_YOU */ .gO, requestBody, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const companyId = result?.data?.data?.companyId;
            if (companyId) {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(companyId));
            }
            else {
            }
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }));
    }
    fetchExecutiveListOptions() {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        return this.http.get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_EXECUTIVE_LIST_OPTIONS */ .Zf, {
            headers: httpHeaders,
        });
    }
    getSavedEvecutiveList() {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        return this.http.get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_SAVED_EXECUTIVE_LIST */ .vz, {
            headers: httpHeaders,
        });
    }
    searchListName(request) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const url = `${src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_SEARCH_RESULT_API */ .rc}${request?.name}`;
        return this.http.get(url, { headers: httpHeaders }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const companyId = result?.data?.data?.companyId;
            if (companyId) {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(companyId));
            }
            else {
            }
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }));
    }
    createExecutiveList(request) {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        const requestBody = {
            myContacts: request?.myContacts,
            listName: request?.listName,
            listId: request?.listId,
            isListExist: request?.isListExist,
            campaignList: request?.campaignList,
            isBulkView: request?.isBulkView,
        };
        return this.http
            .post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .CREATE_EXECUTIVE_LIST */ .U$, requestBody, { headers: httpHeaders })
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__/* .tap */ .b)((result) => {
            const companyId = result?.data?.data?.companyId;
            if (companyId) {
                this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetCompanyId */ .lQ(companyId));
                this.store.dispatch(new _action_popup_action__WEBPACK_IMPORTED_MODULE_4__/* .ClearExecutives */ .qP());
            }
            else {
            }
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__/* .catchError */ .K)((error) => {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }));
    }
    getProfileDataLimit() {
        const authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__/* .ScLoginState.getLoginUserDetails */ .U.getLoginUserDetails);
        if (!authData?.accessToken || !authData?.dsmID) {
            return rxjs__WEBPACK_IMPORTED_MODULE_6__/* .EMPTY */ .E;
        }
        const httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpHeaders */ .WM({
            dsmID: authData.dsmID,
            Authorization: `Bearer ${authData.accessToken}`,
        });
        return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__/* .GET_PROFILE_DATA_LIMIT */ .E3, null, {
            headers: httpHeaders,
        });
    }
}
CompanyService.ɵfac = function CompanyService_Factory(t) { return new (t || CompanyService)(_angular_core__WEBPACK_IMPORTED_MODULE_13__/* ["ɵɵinject"] */ .LFG(_angular_common_http__WEBPACK_IMPORTED_MODULE_7__/* .HttpClient */ .eN), _angular_core__WEBPACK_IMPORTED_MODULE_13__/* ["ɵɵinject"] */ .LFG(_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Store */ .yh)); };
CompanyService.ɵprov = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_13__/* ["ɵɵdefineInjectable"] */ .Yz7({ token: CompanyService, factory: CompanyService.ɵfac, providedIn: "root" });


/***/ }),

/***/ 1333:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "L": function() { return /* binding */ ExtractCompanyState; }
/* harmony export */ });
/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(2321);
/* harmony import */ var _ngxs_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6090);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4921);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8293);
/* harmony import */ var _service_company_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6549);
/* harmony import */ var _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6136);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5871);
/* harmony import */ var src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6718);
/* harmony import */ var src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9818);
/* harmony import */ var _action_company_action__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(8233);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2316);














let ExtractCompanyState = class ExtractCompanyState {
    constructor(companyService, store, snackbarService) {
        this.companyService = companyService;
        this.store = store;
        this.snackbarService = snackbarService;
    }
    static getExtractedCompanyDetails(state) {
        return state.extractedCompanyDetails;
    }
    static getExtractCompanyKeyemp(state) {
        return state.extractKeyEmp;
    }
    static getLogoUrl(state) {
        return state.logoUrl;
    }
    static isLoading(state) {
        return state.loading;
    }
    static extractEmPLoading(state) {
        return state.extractEmPLoading;
    }
    CallAPI(ctx, action) {
        ctx.patchState({
            extractedUrl: null,
        });
    }
    getCompanyKeyEmp(ctx, action) {
        ctx.patchState({ loading: true, extractEmPLoading: true });
        const state = ctx.getState();
        return this.companyService
            .getCompanyExecutivesLeyEmp(action.companyId, action.departmentId, action.executiveLevelId, action.searchTerm)
            .pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__/* .tap */ .b)((response) => {
            const executives = response.data.emailPhoneResponses || [];
            if (executives.length === 0) {
                ctx.patchState({
                    extractKeyEmp: [],
                    loading: false,
                    extractEmPLoading: false,
                });
            }
            else {
                ctx.patchState({
                    extractKeyEmp: executives,
                    loading: false,
                    extractEmPLoading: false,
                });
            }
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__/* .catchError */ .K)((error) => {
            ctx.patchState({
                extractKeyEmp: [],
                loading: false,
                extractEmPLoading: false,
            });
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__/* .throwError */ ._)(error);
        }));
    }
    setCompanyExecutives(ctx, action) {
        ctx.patchState({ extractKeyEmp: action.executives });
    }
    extractCompanyDetails(ctx, action) {
        const state = ctx.getState();
        const newUrl = action.request.website;
        if (state.extractedUrl === newUrl) {
            return;
        }
        ctx.patchState({ loading: true, extractedUrl: newUrl });
        return this.companyService.extractCompanyDetails(action.request).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__/* .tap */ .b)((result) => {
            ctx.patchState({
                extractedCompanyDetails: result?.data?.data || null, // Adjust based on your API response
            });
            const companyid = result?.data?.data?.companyId;
            this.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .GetCompanyKeyEmpInExtractAny */ .b6(companyid, action.request.departmentId || 0, action.request.executiveLevelId || 0, ""));
            // this.store.dispatch(new FetchLogo(result?.data?.data?.website));
            ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__/* .ClearOldUrl */ .GE("clearold "));
            ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__/* .searchFilters */ .LS(null));
            ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__/* .departmentFilters */ .cz(null));
            ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__/* .seniorityFilters */ .PR(null));
            // this.store.dispatch(new searchFilters(this.searchTerm));
            if (result?.message === "company details not found!") {
                ctx.patchState({
                    extractKeyEmp: [],
                    logoUrl: null,
                    loading: false,
                });
            }
            this.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchLogo */ .Vt(result?.data?.data?.website));
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__/* .catchError */ .K)((error) => {
            ctx.patchState({
                extractedCompanyDetails: null,
                logoUrl: null,
                loading: false,
            });
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__/* .throwError */ ._)(error);
        }));
    }
    fetchEmail(ctx, action) {
        const payload = {
            ...action.payload,
            sourceName: "LINKEDIN",
            staffCount: 0,
            source: Array.isArray(action.payload.source)
                ? action.payload.source
                : [action.payload.source],
        };
        ctx.patchState({ isFetchingEmail: true });
        return this.companyService.findEmailOrPhone(payload).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__/* .tap */ .b)((response) => {
            if (response.message === "Email Limit is exhausted") {
                this.snackbarService.openSnackBar(response.message, src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__/* .SNACKBAR_TIME.THREE_SECOND */ ._Q.THREE_SECOND, response);
            }
            let extractcompanyKeyEmp = [...ctx.getState().extractKeyEmp];
            const updatedextractCompanyKeyEmp = extractcompanyKeyEmp.map((emp) => {
                if (emp.sourceId === response?.data?.sourceId) {
                    return {
                        ...emp,
                        email: response?.data?.email,
                        isFetchingEmail: false,
                        error: response?.data?.email === "Not available" ? true : false,
                        // error: !!response.error
                    };
                }
                else {
                    return {
                        ...emp,
                        isFetchingEmail: false,
                        // error: !!response.error
                        error: response?.data?.email === "Not available" ? true : false,
                    };
                }
            });
            ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingEmail: false,
            });
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__/* .catchError */ .K)((error) => {
            let extractKeyEmp = ctx.getState().extractKeyEmp.length > 0
                ? [...ctx.getState().extractKeyEmp]
                : [];
            const updatedextractCompanyKeyEmp = extractKeyEmp.map((emp) => {
                if (emp.executiveId === action.payload.sourceId) {
                    return {
                        ...emp,
                    };
                }
                return emp;
            });
            ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingEmail: false,
            });
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__/* .throwError */ ._)(error);
        }));
    }
    fetchPhone(ctx, action) {
        const payload = {
            ...action.payload,
            sourceName: "LINKEDIN",
            staffCount: 0,
            source: Array.isArray(action.payload.source)
                ? action.payload.source
                : [action.payload.source],
        };
        // Start fetching state update
        ctx.patchState({ isFetchingPhone: true });
        return this.companyService.findEmailOrPhone(payload).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__/* .tap */ .b)((response) => {
            let extractcompanyKeyEmp = [...ctx.getState().extractKeyEmp];
            if (response.message === "Phone Limit is exhausted") {
                this.snackbarService.openSnackBar(response.message, src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__/* .SNACKBAR_TIME.THREE_SECOND */ ._Q.THREE_SECOND, response);
            }
            // Update state with the fetched phone data
            const updatedextractCompanyKeyEmp = extractcompanyKeyEmp.map((emp) => {
                if (emp.sourceId === action.payload.sourceId) {
                    return {
                        ...emp,
                        mobileNumber: response.data.mobileNumber,
                        isFetchingPhone: false, // Stop fetching
                        // error: !!response.error, // Set error flag if needed
                    };
                }
                else {
                    return {
                        ...emp,
                        // phone: response.phone || emp.phone, // Update with fetched phone
                        isFetchingPhone: false, // Stop fetching
                        // error: !!response.error, // Set error flag if needed
                    };
                }
            });
            ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingPhone: false,
            });
        }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__/* .catchError */ .K)((error) => {
            // Handle error and stop loading state
            const updatedextractCompanyKeyEmp = ctx
                .getState()
                .extractKeyEmp.map((emp) => {
                if (emp.executiveId === action.payload.sourceId) {
                    return {
                        ...emp,
                        isFetchingPhone: false,
                        error: true, // Mark error state
                    };
                }
                return emp;
            });
            ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingPhone: false,
            });
            return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__/* .throwError */ ._)(error);
        }));
    }
    fetchLogo(ctx, action) {
        ctx.patchState({ loading: true });
        return this.companyService.fetchLogo(action.payload).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__/* .tap */ .b)((logoUrl) => {
            ctx.patchState({ logoUrl, loading: false });
        }));
    }
};
ExtractCompanyState.ɵfac = function ExtractCompanyState_Factory(t) { return new (t || ExtractCompanyState)(_angular_core__WEBPACK_IMPORTED_MODULE_9__/* ["ɵɵinject"] */ .LFG(_service_company_service__WEBPACK_IMPORTED_MODULE_1__/* .CompanyService */ .J), _angular_core__WEBPACK_IMPORTED_MODULE_9__/* ["ɵɵinject"] */ .LFG(_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Store */ .yh), _angular_core__WEBPACK_IMPORTED_MODULE_9__/* ["ɵɵinject"] */ .LFG(src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__/* .SnackbarService */ .o)); };
ExtractCompanyState.ɵprov = /*@__PURE__*/ _angular_core__WEBPACK_IMPORTED_MODULE_9__/* ["ɵɵdefineInjectable"] */ .Yz7({ token: ExtractCompanyState, factory: ExtractCompanyState.ɵfac });
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .CallAPI */ .Sf),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .CallAPI */ .Sf]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "CallAPI", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .GetCompanyKeyEmpInExtractAny */ .b6),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .GetCompanyKeyEmpInExtractAny */ .b6]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "getCompanyKeyEmp", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetExtractCompanyExecutives */ .yX),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .SetExtractCompanyExecutives */ .yX]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "setCompanyExecutives", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .ExtractCompanyDetails */ .LE),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .ExtractCompanyDetails */ .LE]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "extractCompanyDetails", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchEmail */ .ct),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchEmail */ .ct]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "fetchEmail", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchPhone */ .Vx),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchPhone */ .Vx]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "fetchPhone", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Action */ .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchLogo */ .Vt),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__/* .FetchLogo */ .Vt]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState.prototype, "fetchLogo", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Selector */ .Qf)(),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState, "getExtractedCompanyDetails", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Selector */ .Qf)(),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", void 0)
], ExtractCompanyState, "getExtractCompanyKeyemp", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Selector */ .Qf)(),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", String)
], ExtractCompanyState, "getLogoUrl", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Selector */ .Qf)(),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", Boolean)
], ExtractCompanyState, "isLoading", null);
(0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Selector */ .Qf)(),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:type", Function),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [Object]),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:returntype", Boolean)
], ExtractCompanyState, "extractEmPLoading", null);
ExtractCompanyState = (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__decorate */ .gn)([
    (0,_ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .State */ .ZM)({
        name: "extractCompany",
        defaults: {
            extractedCompanyDetails: null,
            extractKeyEmp: [],
            companyKeyEmp: null,
            loading: false,
            logoUrl: null,
            extractedUrl: null,
            extractEmPLoading: false,
        },
    }),
    (0,tslib__WEBPACK_IMPORTED_MODULE_10__/* .__metadata */ .w6)("design:paramtypes", [_service_company_service__WEBPACK_IMPORTED_MODULE_1__/* .CompanyService */ .J,
        _ngxs_store__WEBPACK_IMPORTED_MODULE_0__/* .Store */ .yh,
        src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__/* .SnackbarService */ .o])
], ExtractCompanyState);



/***/ }),

/***/ 3154:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "$": function() { return /* binding */ TAB_ID; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2316);

/**
 * provides the currently opened tab id
 */
const TAB_ID = new _angular_core__WEBPACK_IMPORTED_MODULE_0__/* .InjectionToken */ .OlP('CHROME_TAB_ID');


/***/ }),

/***/ 8135:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "N": function() { return /* binding */ environment; }
/* harmony export */ });
const environment = {
    production: false,
    devToolsEnabled: false,
    BASE_URL: "https://app.salezshark.com/linkedinextensions/",
    SALEZCONNECT_BASE_API_URL: "https://www.salezshark.com/connect/app/gateway",
    // SALEZCONNECT_BASE_API_URL: "https://qa.salezshark.io/connect/app/gateway",
    APP_NAME: "Saleshark Connect +",
    linkedinClientId: "86i65bsc5clxfq",
    googleClientId: "266877872832-1ibf2cmgb91r2hhm7s9n17tcn31gojb4.apps.googleusercontent.com",
    linkedinSignIn: "https://www.salezshark.com/connect/app/auth/linkedinsignin",
    linkedinSignup: "https://www.salezshark.com/connect/app/auth/linkedinsignup",
    gmailSignIn: "https://www.salezshark.com/connect/app/auth/gmailsignin",
    gmailSignup: "https://www.salezshark.com/connect/app/auth/gmailsignup",
    connectPlusUrl: "https://qa.salezshark.io/wa/app/app",
    DEFULT_LOGO_URL: "https://qa.salezshark.io/wa/app/assets/images/company_default.svg",
};


/***/ }),

/***/ 3182:
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/@angular/platform-browser/__ivy_ngcc__/fesm2015/platform-browser.js
var platform_browser = __webpack_require__(1570);
// EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js
var core = __webpack_require__(2316);
// EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules
var router = __webpack_require__(24);
;// CONCATENATED MODULE: ./angular/src/app/app-routing.module.ts



const routes = [
    {
        path: "popup",
        pathMatch: "full",
        loadChildren: () => Promise.all(/* import() */[__webpack_require__.e("default-angular_src_app_modules_popup_common_module_ts"), __webpack_require__.e("angular_src_app_modules_popup_popup_module_ts")]).then(__webpack_require__.bind(__webpack_require__, 1224)).then((m) => m.PopupModule),
    },
    {
        path: "tab",
        pathMatch: "full",
        loadChildren: () => __webpack_require__.e(/* import() */ "angular_src_app_modules_tab_tab_module_ts").then(__webpack_require__.bind(__webpack_require__, 9651)).then((m) => m.TabModule),
    },
    {
        path: "options",
        pathMatch: "full",
        loadChildren: () => __webpack_require__.e(/* import() */ "angular_src_app_modules_options_options_module_ts").then(__webpack_require__.bind(__webpack_require__, 4157)).then((m) => m.OptionsModule),
    },
    {
        path: "company",
        pathMatch: "full",
        loadChildren: () => Promise.all(/* import() */[__webpack_require__.e("default-angular_src_app_modules_popup_common_module_ts"), __webpack_require__.e("angular_src_app_modules_popup_popup1_module_ts")]).then(__webpack_require__.bind(__webpack_require__, 4971)).then((m) => m.Popup1Module),
    },
];
class AppRoutingModule {
}
AppRoutingModule.ɵfac = function AppRoutingModule_Factory(t) { return new (t || AppRoutingModule)(); };
AppRoutingModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: AppRoutingModule });
AppRoutingModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[router/* RouterModule.forRoot */.Bz.forRoot(routes, { useHash: true })], router/* RouterModule */.Bz] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(AppRoutingModule, { imports: [router/* RouterModule */.Bz], exports: [router/* RouterModule */.Bz] }); })();

// EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs
var tslib_es6 = __webpack_require__(2321);
// EXTERNAL MODULE: ./node_modules/@ngxs/store/fesm2015/ngxs-store.js + 3 modules
var ngxs_store = __webpack_require__(6090);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/state/login.state.ts + 1 modules
var login_state = __webpack_require__(8341);
;// CONCATENATED MODULE: ./angular/src/app/app.component.ts





class AppComponent {
    constructor() {
    }
}
AppComponent.ɵfac = function AppComponent_Factory(t) { return new (t || AppComponent)(); };
AppComponent.ɵcmp = /*@__PURE__*/ core/* ɵɵdefineComponent */.Xpm({ type: AppComponent, selectors: [["app-root"]], decls: 1, vars: 0, template: function AppComponent_Template(rf, ctx) { if (rf & 1) {
        core/* ɵɵelement */._UZ(0, "router-outlet");
    } }, directives: [router/* RouterOutlet */.lC], styles: [""] });
(0,tslib_es6/* __decorate */.gn)([
    (0,ngxs_store/* Select */.Ph)(login_state/* ScLoginState.isLoggedIn */.U.isLoggedIn),
    (0,tslib_es6/* __metadata */.w6)("design:type", Object)
], AppComponent.prototype, "isLoggedIn$", void 0);

// EXTERNAL MODULE: ./node_modules/@ngxs/router-plugin/fesm2015/ngxs-router-plugin.js
var ngxs_router_plugin = __webpack_require__(3942);
// EXTERNAL MODULE: ./node_modules/@ngxs/storage-plugin/fesm2015/ngxs-storage-plugin.js
var ngxs_storage_plugin = __webpack_require__(3552);
// EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/http.js
var http = __webpack_require__(3882);
// EXTERNAL MODULE: ./node_modules/@angular/platform-browser/__ivy_ngcc__/fesm2015/animations.js + 1 modules
var animations = __webpack_require__(1918);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/BehaviorSubject.js
var BehaviorSubject = __webpack_require__(6491);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/observable/throwError.js
var throwError = __webpack_require__(5871);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/catchError.js
var catchError = __webpack_require__(8293);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/switchMap.js
var switchMap = __webpack_require__(9902);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/filter.js
var filter = __webpack_require__(9170);
// EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/take.js
var take = __webpack_require__(3466);
// EXTERNAL MODULE: ./angular/src/app/constant/api.url.ts
var api_url = __webpack_require__(5502);
// EXTERNAL MODULE: ./angular/src/app/constant/status-code.ts
var status_code = __webpack_require__(1888);
// EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.service.ts
var snack_bar_service = __webpack_require__(6718);
// EXTERNAL MODULE: ./angular/src/app/constant/message.ts
var message = __webpack_require__(7063);
// EXTERNAL MODULE: ./angular/src/app/constant/value.ts
var constant_value = __webpack_require__(9818);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/action/login.action.ts
var login_action = __webpack_require__(9198);
// EXTERNAL MODULE: ./node_modules/tweetnacl/nacl-fast.js
var nacl_fast = __webpack_require__(7097);
// EXTERNAL MODULE: ./node_modules/tweetnacl-util/nacl-util.js
var nacl_util = __webpack_require__(7163);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/service/login.service.ts
var login_service = __webpack_require__(8903);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/action/popup.action.ts
var popup_action = __webpack_require__(1950);
;// CONCATENATED MODULE: ./angular/src/app/interceptor/request.interceptor.ts



















window.global = window;
// @ts-ignore
window.Buffer = window.Buffer || __webpack_require__(2574)/* .Buffer */ .lW;
class RequestInterceptor {
    constructor(store, snackbarService, authService) {
        this.store = store;
        this.snackbarService = snackbarService;
        this.authService = authService;
        this.refreshTokenInProgress = false;
        this.refreshTokenSubject = new BehaviorSubject/* BehaviorSubject */.X(null);
    }
    intercept(request, next) {
        return next.handle(this.addAuthenticationToken(request)).pipe((0,catchError/* catchError */.K)((error) => {
            switch (error.status) {
                case status_code/* StatusCode.UNAUTHORIZED */.G.UNAUTHORIZED:
                    return this.handle401Error(next, request);
                case status_code/* StatusCode.UNKNOWN_ERROR */.G.UNKNOWN_ERROR:
                    if (request.url.includes("email")) {
                        var excutiveJsion = JSON.parse(request.body);
                        this.store.dispatch(new popup_action/* ShowExecutiveEmailIdLoaderClose */.hr(excutiveJsion.id, message/* ClientMessage.NO_EMAIL_FOUND */.xT.NO_EMAIL_FOUND));
                    }
                    else {
                        this.snackbarService.openSnackBar(message/* ClientMessage.SERVER_ERROR */.xT.SERVER_ERROR, constant_value/* SNACKBAR_TIME.THREE_SECOND */._Q.THREE_SECOND, constant_value/* SNACK_BAR_TYPE.ERROR */.cx.ERROR);
                    }
                    break;
                case status_code/* StatusCode.INTERNALSERVERERROR */.G.INTERNALSERVERERROR:
                    this.snackbarService.openSnackBar(error.message, constant_value/* SNACKBAR_TIME.THREE_SECOND */._Q.THREE_SECOND, constant_value/* SNACK_BAR_TYPE.ERROR */.cx.ERROR);
                    break;
                case status_code/* StatusCode.NOTFOUND */.G.NOTFOUND:
                    // this.snackbarService.openSnackBar(
                    //   ClientMessage.SERVER_ERROR_404,
                    //   SNACKBAR_TIME.THREE_SECOND,
                    //   SNACK_BAR_TYPE.ERROR
                    // );
                    break;
                case status_code/* StatusCode.BAD_GATEWAY */.G.BAD_GATEWAY:
                    this.snackbarService.openSnackBar(message/* ClientMessage.SERVER_ERROR */.xT.SERVER_ERROR, constant_value/* SNACKBAR_TIME.THREE_SECOND */._Q.THREE_SECOND, constant_value/* SNACK_BAR_TYPE.ERROR */.cx.ERROR);
                    break;
            }
            return (0,throwError/* throwError */._)(error);
        }));
    }
    handle401Error(next, request) {
        if (!this.refreshTokenInProgress) {
            this.refreshTokenInProgress = true;
            this.refreshTokenSubject.next(null);
            return this.authService
                .getNewAccessToken(this.getRefreshToken()).pipe((0,switchMap/* switchMap */.w)((response) => {
                if (response.statusCode === status_code/* StatusCode.SUCCESS */.G.SUCCESS && response?.data) {
                    this.store.dispatch(new login_action/* SetAuthData */.HR(response));
                    this.refreshTokenSubject.next(response?.data?.accessToken);
                    this.refreshTokenInProgress = false;
                    return next.handle(this.addAuthenticationToken(request));
                }
                else {
                    return this.refreshTokenExpired();
                }
            }), (0,catchError/* catchError */.K)(error => {
                return this.refreshTokenExpired();
            }));
        }
        else {
            return this.refreshTokenSubject.pipe((0,filter/* filter */.h)(token => token !== null), (0,take/* take */.q)(1), (0,switchMap/* switchMap */.w)(() => {
                return next.handle(this.addAuthenticationToken(request));
            }));
        }
    }
    refreshTokenExpired() {
        this.refreshTokenInProgress = false;
        this.snackbarService.openSnackBar(message/* ClientMessage.SESSION_EXPIRED */.xT.SESSION_EXPIRED, constant_value/* SNACKBAR_TIME.THREE_SECOND */._Q.THREE_SECOND, constant_value/* SNACK_BAR_TYPE.ERROR */.cx.ERROR);
        return this.store.dispatch(new login_action/* LogoutSuccess */.ys());
    }
    encrypt(value) {
        const encodeBase64 = nacl_util.encodeBase64;
        const nonce = nacl_fast.randomBytes(24);
        const sampleValue = Buffer.from(constant_value/* SAMPLE_TEST */.CS, 'utf8');
        const sampleData = Buffer.from(value, 'utf8');
        const sampleValueE = nacl_fast.secretbox(sampleData, nonce, sampleValue);
        const result = `${encodeBase64(nonce)}:${encodeBase64(sampleValueE)}`;
        // const wordArray = crypto.enc.Utf8.parse(value);
        // return crypto.enc.Base64.stringify(wordArray);
        return result;
    }
    addAuthenticationToken(request) {
        if (!(request.url.includes(api_url/* LOGIN_API */.C2) ||
            request.url.includes(api_url/* VERIFY_EMAIL_API */.eB) ||
            request.url.includes(api_url/* SIGNUP_API */.Rl) ||
            request.url.includes(api_url/* IS_EMAIL_EXIST_API */.H5) ||
            request.url.includes(api_url/* UPDATE_PASSWORD_API */.cs) ||
            request.url.includes(api_url/* FORGOT_PASSWORD_API */.GF) ||
            request.url.includes(api_url/* GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API */.JE))) {
            // if (request.url.includes('voyager') || request.url.includes('sales-api')) {
            //   return request.clone({
            //     setHeaders: {
            //       'csrf-token': getCsrfToken()
            //     }
            //   });
            // } else 
            if (request.url.includes('/linkedinextension')) {
                return request.clone({
                    setHeaders: {
                        auth: this.encrypt(constant_value/* SAMPLE_DATA */.rZ),
                        Authorization: 'bearer ' + this.getAccessToken(),
                        dsmID: this.getDsmId(),
                    }
                });
            }
            else if (request.url.includes('/linkedinParser')) {
                return request.clone({
                    setHeaders: {
                        auth: this.encrypt(constant_value/* SAMPLE_DATA */.rZ),
                    }
                });
            }
            else {
                return request.clone({
                    setHeaders: {
                        Authorization: 'bearer ' + this.getAccessToken(),
                        dsmID: this.getDsmId(),
                        timeZone: this.getTimeZone(),
                    }
                });
            }
        }
        else {
            return request;
        }
    }
    getAccessToken() {
        return this.store.selectSnapshot((state) => state?.auth?.authData?.accessToken);
    }
    getDsmId() {
        return this.store.selectSnapshot((state) => state?.auth?.authData?.dsmID);
    }
    getRefreshToken() {
        return this.store.selectSnapshot((state) => state?.auth?.authData?.refreshToken);
    }
    getTimeZone() {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    }
}
RequestInterceptor.ɵfac = function RequestInterceptor_Factory(t) { return new (t || RequestInterceptor)(core/* ɵɵinject */.LFG(ngxs_store/* Store */.yh), core/* ɵɵinject */.LFG(snack_bar_service/* SnackbarService */.o), core/* ɵɵinject */.LFG(login_service/* LoginService */.r)); };
RequestInterceptor.ɵprov = /*@__PURE__*/ core/* ɵɵdefineInjectable */.Yz7({ token: RequestInterceptor, factory: RequestInterceptor.ɵfac });

// EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.module.ts
var snack_bar_module = __webpack_require__(4486);
// EXTERNAL MODULE: ./node_modules/@angular/material/__ivy_ngcc__/fesm2015/button.js
var fesm2015_button = __webpack_require__(781);
// EXTERNAL MODULE: ./node_modules/@angular/material/__ivy_ngcc__/fesm2015/menu.js + 2 modules
var menu = __webpack_require__(1303);
// EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/common.js
var common = __webpack_require__(4364);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/state/extract-company.state.ts
var extract_company_state = __webpack_require__(1333);
;// CONCATENATED MODULE: ./angular/src/app/app.module.ts






//import { NgxsLoggerPluginModule } from '@ngxs/logger-plugin';












class AppModule {
}
AppModule.ɵfac = function AppModule_Factory(t) { return new (t || AppModule)(); };
AppModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: AppModule, bootstrap: [AppComponent] });
AppModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ providers: [
        {
            provide: http/* HTTP_INTERCEPTORS */.TP,
            useClass: RequestInterceptor,
            multi: true
        }
    ], imports: [[platform_browser/* BrowserModule */.b2,
            fesm2015_button/* MatButtonModule */.ot,
            menu/* MatMenuModule */.Tx,
            common/* CommonModule */.ez,
            AppRoutingModule,
            ngxs_store/* NgxsModule.forRoot */.$l.forRoot([extract_company_state/* ExtractCompanyState */.L], {
                compatibility: {
                    strictContentSecurityPolicy: true
                }
            }),
            ngxs_storage_plugin/* NgxsStoragePluginModule.forRoot */.x.forRoot({
                key: [
                    'auth.authData',
                    'executives',
                    "popup.dailyLimit",
                    "popup.dailyTime",
                    "auth"
                ],
            }),
            //NgxsLoggerPluginModule.forRoot({ disabled: environment.production }),
            ngxs_router_plugin/* NgxsRouterPluginModule.forRoot */.G1.forRoot(),
            http/* HttpClientModule */.JF,
            animations/* BrowserAnimationsModule */.PW,
            snack_bar_module/* SnackBarModule */.Y,
        ]] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(AppModule, { declarations: [AppComponent], imports: [platform_browser/* BrowserModule */.b2,
        fesm2015_button/* MatButtonModule */.ot,
        menu/* MatMenuModule */.Tx,
        common/* CommonModule */.ez,
        AppRoutingModule, ngxs_store/* ɵNgxsRootModule */.gr, ngxs_storage_plugin/* NgxsStoragePluginModule */.x, ngxs_router_plugin/* NgxsRouterPluginModule */.G1, http/* HttpClientModule */.JF,
        animations/* BrowserAnimationsModule */.PW,
        snack_bar_module/* SnackBarModule */.Y] }); })();

// EXTERNAL MODULE: ./angular/src/environments/environment.ts
var environment = __webpack_require__(8135);
// EXTERNAL MODULE: ./angular/src/app/providers/tab-id.provider.ts
var tab_id_provider = __webpack_require__(3154);
;// CONCATENATED MODULE: ./package.json
var package_namespaceObject = {"i8":"1.0.1"};
;// CONCATENATED MODULE: ./angular/src/main.ts






chrome.tabs.query({ active: true, currentWindow: true }, tabs => {
    if (environment/* environment.production */.N.production) {
        (0,core/* enableProdMode */.G48)();
    }
    const tab = [...tabs].pop();
    const { id: tabId } = tab;
    const appVersion = package_namespaceObject.i8 || '0.0.0';
    const metaTag = document.createElement('meta');
    metaTag.name = 'app-version';
    metaTag.content = appVersion;
    document.head.prepend(metaTag);
    // provides the current Tab ID so you can send messages to the content page
    platform_browser/* platformBrowser */.q6([{ provide: tab_id_provider/* TAB_ID */.$, useValue: tabId }])
        .bootstrapModule(AppModule)
        .catch(error => console.error(error));
});


/***/ }),

/***/ 5024:
/***/ (function() {

/* (ignored) */

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["vendor"], function() { return __webpack_exec__(3182); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main-esnext.js.map