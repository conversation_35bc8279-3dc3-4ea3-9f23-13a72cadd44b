{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, NgZone, Injector, Injectable, NgModule } from '@angular/core';\nimport * as i1 from '@ngxs/store';\nimport { Action, Selector, State, Store, NgxsModule } from '@ngxs/store';\nimport { __decorate, __metadata } from 'tslib';\nimport * as i2 from '@angular/router';\nimport { NavigationStart, RoutesRecognized, ResolveEnd, NavigationCancel, NavigationError, NavigationEnd, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n/**\n * Public event api of the router\n */\n\nclass Navigate {\n  constructor(path, queryParams, extras) {\n    this.path = path;\n    this.queryParams = queryParams;\n    this.extras = extras;\n  }\n\n}\n\nNavigate.type = '[Router] Navigate';\n/**\n *\n * Angular Routers internal state events\n *\n */\n\n/**\n * An action dispatched when the router starts the navigation.\n */\n\nclass RouterRequest {\n  constructor(routerState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterRequest.type = '[Router] RouterRequest';\n/**\n * An action dispatched when the router navigates.\n */\n\nclass RouterNavigation {\n  constructor(routerState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterNavigation.type = '[Router] RouterNavigation';\n/**\n * An action dispatched when the router cancel navigation.\n */\n\nclass RouterCancel {\n  constructor(routerState, storeState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.storeState = storeState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterCancel.type = '[Router] RouterCancel';\n/**\n * An action dispatched when the router errors.\n */\n\nclass RouterError {\n  constructor(routerState, storeState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.storeState = storeState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterError.type = '[Router] RouterError';\n/**\n * An action dispatched when the `ResolveEnd` event is triggered.\n */\n\nclass RouterDataResolved {\n  constructor(routerState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterDataResolved.type = '[Router] RouterDataResolved';\n/**\n * An action dispatched when the router navigation has been finished successfully.\n */\n\nclass RouterNavigated {\n  constructor(routerState, event, trigger = 'none') {\n    this.routerState = routerState;\n    this.event = event;\n    this.trigger = trigger;\n  }\n\n}\n\nRouterNavigated.type = '[Router] RouterNavigated';\n\nclass RouterStateSerializer {}\n\nclass DefaultRouterStateSerializer {\n  serialize(routerState) {\n    return {\n      root: this.serializeRoute(routerState.root),\n      url: routerState.url\n    };\n  }\n\n  serializeRoute(route) {\n    const children = route.children.map(c => this.serializeRoute(c));\n    return {\n      url: route.url,\n      params: route.params,\n      queryParams: route.queryParams,\n      fragment: route.fragment,\n      data: route.data,\n      outlet: route.outlet,\n      component: null,\n      routeConfig: null,\n      root: null,\n      parent: null,\n      firstChild: children[0],\n      children: children,\n      pathFromRoot: null,\n      paramMap: route.paramMap,\n      queryParamMap: route.queryParamMap,\n      toString: route.toString\n    };\n  }\n\n}\n\nconst USER_OPTIONS = new InjectionToken('USER_OPTIONS', {\n  providedIn: 'root',\n  factory: () => undefined\n});\nconst NGXS_ROUTER_PLUGIN_OPTIONS = new InjectionToken('NGXS_ROUTER_PLUGIN_OPTIONS', {\n  providedIn: 'root',\n  factory: () => ({})\n});\n\nfunction createRouterPluginOptions(options) {\n  return {\n    navigationActionTiming: options && options.navigationActionTiming || 1\n    /* PreActivation */\n\n  };\n}\n\nvar RouterState_1;\nlet RouterState = RouterState_1 = class RouterState {\n  constructor(_store, _router, _serializer, _ngZone, injector) {\n    this._store = _store;\n    this._router = _router;\n    this._serializer = _serializer;\n    this._ngZone = _ngZone;\n    /**\n     * Determines how navigation was performed by the `RouterState` itself\n     * or outside via `new Navigate(...)`\n     */\n\n    this._trigger = 'none';\n    /**\n     * That's the serialized state from the `Router` class\n     */\n\n    this._routerState = null;\n    /**\n     * That's the value of the `RouterState` state\n     */\n\n    this._storeState = null;\n    this._lastEvent = null;\n    this._subscription = new Subscription();\n    this._options = null; // Note: do not use `@Inject` since it fails on lower versions of Angular with Jest\n    // integration, it cannot resolve the token provider.\n\n    this._options = injector.get(NGXS_ROUTER_PLUGIN_OPTIONS, null);\n\n    this._setUpStoreListener();\n\n    this._setUpRouterEventsListener();\n  }\n\n  static state(state) {\n    return state && state.state;\n  }\n\n  static url(state) {\n    return state && state.state && state.state.url;\n  }\n\n  ngOnDestroy() {\n    this._subscription.unsubscribe();\n  }\n\n  navigate(_, action) {\n    return this._ngZone.run(() => this._router.navigate(action.path, Object.assign({\n      queryParams: action.queryParams\n    }, action.extras)));\n  }\n\n  angularRouterAction(ctx, action) {\n    ctx.setState({\n      trigger: action.trigger,\n      state: action.routerState,\n      navigationId: action.event.id\n    });\n  }\n\n  _setUpStoreListener() {\n    const subscription = this._store.select(RouterState_1).subscribe(state => {\n      this._navigateIfNeeded(state);\n    });\n\n    this._subscription.add(subscription);\n  }\n\n  _navigateIfNeeded(routerState) {\n    if (routerState && routerState.trigger === 'devtools') {\n      this._storeState = this._store.selectSnapshot(RouterState_1);\n    }\n\n    const canSkipNavigation = !this._storeState || !this._storeState.state || !routerState || routerState.trigger === 'router' || this._router.url === this._storeState.state.url || this._lastEvent instanceof NavigationStart;\n\n    if (canSkipNavigation) {\n      return;\n    }\n\n    this._storeState = this._store.selectSnapshot(RouterState_1);\n    this._trigger = 'store';\n\n    this._ngZone.run(() => this._router.navigateByUrl(this._storeState.state.url));\n  }\n\n  _setUpRouterEventsListener() {\n    const dispatchRouterNavigationLate = this._options != null && this._options.navigationActionTiming === 2\n    /* PostActivation */\n    ;\n    let lastRoutesRecognized;\n\n    const subscription = this._router.events.subscribe(event => {\n      this._lastEvent = event;\n\n      if (event instanceof NavigationStart) {\n        this._navigationStart(event);\n      } else if (event instanceof RoutesRecognized) {\n        lastRoutesRecognized = event;\n\n        if (!dispatchRouterNavigationLate && this._trigger !== 'store') {\n          this._dispatchRouterNavigation(lastRoutesRecognized);\n        }\n      } else if (event instanceof ResolveEnd) {\n        this._dispatchRouterDataResolved(event);\n      } else if (event instanceof NavigationCancel) {\n        this._dispatchRouterCancel(event);\n\n        this._reset();\n      } else if (event instanceof NavigationError) {\n        this._dispatchRouterError(event);\n\n        this._reset();\n      } else if (event instanceof NavigationEnd) {\n        if (this._trigger !== 'store') {\n          if (dispatchRouterNavigationLate) {\n            this._dispatchRouterNavigation(lastRoutesRecognized);\n          }\n\n          this._dispatchRouterNavigated(event);\n        }\n\n        this._reset();\n      }\n    });\n\n    this._subscription.add(subscription);\n  }\n  /** Reacts to `NavigationStart`. */\n\n\n  _navigationStart(event) {\n    this._routerState = this._serializer.serialize(this._router.routerState.snapshot);\n\n    if (this._trigger !== 'none') {\n      this._storeState = this._store.selectSnapshot(RouterState_1);\n\n      this._dispatchRouterAction(new RouterRequest(this._routerState, event, this._trigger));\n    }\n  }\n  /** Reacts to `ResolveEnd`. */\n\n\n  _dispatchRouterDataResolved(event) {\n    const routerState = this._serializer.serialize(event.state);\n\n    this._dispatchRouterAction(new RouterDataResolved(routerState, event, this._trigger));\n  }\n  /** Reacts to `RoutesRecognized` or `NavigationEnd`, depends on the `navigationActionTiming`. */\n\n\n  _dispatchRouterNavigation(lastRoutesRecognized) {\n    const nextRouterState = this._serializer.serialize(lastRoutesRecognized.state);\n\n    this._dispatchRouterAction(new RouterNavigation(nextRouterState, new RoutesRecognized(lastRoutesRecognized.id, lastRoutesRecognized.url, lastRoutesRecognized.urlAfterRedirects, nextRouterState), this._trigger));\n  }\n  /** Reacts to `NavigationCancel`. */\n\n\n  _dispatchRouterCancel(event) {\n    this._dispatchRouterAction(new RouterCancel(this._routerState, this._storeState, event, this._trigger));\n  }\n  /** Reacts to `NavigationEnd`. */\n\n\n  _dispatchRouterError(event) {\n    this._dispatchRouterAction(new RouterError(this._routerState, this._storeState, new NavigationError(event.id, event.url, `${event}`), this._trigger));\n  }\n  /** Reacts to `NavigationEnd`. */\n\n\n  _dispatchRouterNavigated(event) {\n    const routerState = this._serializer.serialize(this._router.routerState.snapshot);\n\n    this._dispatchRouterAction(new RouterNavigated(routerState, event, this._trigger));\n  }\n\n  _dispatchRouterAction(action) {\n    this._trigger = 'router';\n\n    try {\n      this._store.dispatch(action);\n    } finally {\n      this._trigger = 'none';\n    }\n  }\n\n  _reset() {\n    this._trigger = 'none';\n    this._storeState = null;\n    this._routerState = null;\n  }\n\n};\n/** @nocollapse */\n\nRouterState.ɵfac = function RouterState_Factory(t) {\n  return new (t || RouterState)(i0.ɵɵinject(i1.Store), i0.ɵɵinject(i2.Router), i0.ɵɵinject(RouterStateSerializer), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.Injector));\n};\n/** @nocollapse */\n\n\nRouterState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: RouterState,\n  factory: RouterState.ɵfac\n});\n\n__decorate([Action(Navigate), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [Object, Navigate]), __metadata(\"design:returntype\", void 0)], RouterState.prototype, \"navigate\", null);\n\n__decorate([Action([RouterRequest, RouterNavigation, RouterError, RouterCancel, RouterDataResolved, RouterNavigated]), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [Object, Object]), __metadata(\"design:returntype\", void 0)], RouterState.prototype, \"angularRouterAction\", null);\n\n__decorate([Selector(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [Object]), __metadata(\"design:returntype\", void 0)], RouterState, \"state\", null);\n\n__decorate([Selector(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [Object]), __metadata(\"design:returntype\", Object)], RouterState, \"url\", null);\n\nRouterState = RouterState_1 = __decorate([State({\n  name: 'router',\n  defaults: {\n    state: undefined,\n    navigationId: undefined,\n    trigger: 'none'\n  }\n}), __metadata(\"design:paramtypes\", [Store, Router, RouterStateSerializer, NgZone, Injector])], RouterState);\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterState, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.Store\n    }, {\n      type: i2.Router\n    }, {\n      type: RouterStateSerializer\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Injector\n    }];\n  }, {\n    navigate: [],\n    angularRouterAction: []\n  });\n})();\n\nclass NgxsRouterPluginModule {\n  static forRoot(options) {\n    return {\n      ngModule: NgxsRouterPluginModule,\n      providers: [{\n        provide: USER_OPTIONS,\n        useValue: options\n      }, {\n        provide: NGXS_ROUTER_PLUGIN_OPTIONS,\n        useFactory: createRouterPluginOptions,\n        deps: [USER_OPTIONS]\n      }, {\n        provide: RouterStateSerializer,\n        useClass: DefaultRouterStateSerializer\n      }]\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsRouterPluginModule.ɵfac = function NgxsRouterPluginModule_Factory(t) {\n  return new (t || NgxsRouterPluginModule)();\n};\n/** @nocollapse */\n\n\nNgxsRouterPluginModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsRouterPluginModule\n});\n/** @nocollapse */\n\nNgxsRouterPluginModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[NgxsModule.forFeature([RouterState])]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsRouterPluginModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NgxsModule.forFeature([RouterState])]\n    }]\n  }], null, null);\n})();\n/**\n * The public api for consumers of @ngxs/router-plugin\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DefaultRouterStateSerializer, Navigate, NgxsRouterPluginModule, RouterCancel, RouterDataResolved, RouterError, RouterNavigated, RouterNavigation, RouterRequest, RouterState, RouterStateSerializer };", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@ngxs/router-plugin/fesm2015/ngxs-router-plugin.js"], "names": ["i0", "InjectionToken", "NgZone", "Injector", "Injectable", "NgModule", "i1", "Action", "Selector", "State", "Store", "NgxsModule", "__decorate", "__metadata", "i2", "NavigationStart", "RoutesRecognized", "ResolveEnd", "NavigationCancel", "NavigationError", "NavigationEnd", "Router", "Subscription", "Navigate", "constructor", "path", "queryParams", "extras", "type", "RouterRequest", "routerState", "event", "trigger", "RouterNavigation", "RouterCancel", "storeState", "RouterError", "RouterDataResolved", "RouterNavigated", "RouterStateSerializer", "DefaultRouterStateSerializer", "serialize", "root", "serializeRoute", "url", "route", "children", "map", "c", "params", "fragment", "data", "outlet", "component", "routeConfig", "parent", "<PERSON><PERSON><PERSON><PERSON>", "pathFromRoot", "paramMap", "queryParamMap", "toString", "USER_OPTIONS", "providedIn", "factory", "undefined", "NGXS_ROUTER_PLUGIN_OPTIONS", "createRouterPluginOptions", "options", "navigationActionTiming", "RouterState_1", "RouterState", "_store", "_router", "_serializer", "_ngZone", "injector", "_trigger", "_routerState", "_storeState", "_lastEvent", "_subscription", "_options", "get", "_setUpStoreListener", "_setUpRouterEventsListener", "state", "ngOnDestroy", "unsubscribe", "navigate", "_", "action", "run", "Object", "assign", "angularRouterAction", "ctx", "setState", "navigationId", "id", "subscription", "select", "subscribe", "_navigateIfNeeded", "add", "selectSnapshot", "canSkipNavigation", "navigateByUrl", "dispatchRouterNavigationLate", "lastRoutesRecognized", "events", "_navigationStart", "_dispatchRouterNavigation", "_dispatchRouterDataResolved", "_dispatchRouterCancel", "_reset", "_dispatchRouterError", "_dispatchRouterNavigated", "snapshot", "_dispatchRouterAction", "nextRouterState", "urlAfterRedirects", "dispatch", "ɵfac", "ɵprov", "Function", "prototype", "name", "defaults", "NgxsRouterPluginModule", "forRoot", "ngModule", "providers", "provide", "useValue", "useFactory", "deps", "useClass", "ɵmod", "ɵinj", "forFeature", "args", "imports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,MAAzB,EAAiCC,QAAjC,EAA2CC,UAA3C,EAAuDC,QAAvD,QAAuE,eAAvE;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,MAAT,EAAiBC,QAAjB,EAA2BC,KAA3B,EAAkCC,KAAlC,EAAyCC,UAAzC,QAA2D,aAA3D;AACA,SAASC,UAAT,EAAqBC,UAArB,QAAuC,OAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,eAAT,EAA0BC,gBAA1B,EAA4CC,UAA5C,EAAwDC,gBAAxD,EAA0EC,eAA1E,EAA2FC,aAA3F,EAA0GC,MAA1G,QAAwH,iBAAxH;AACA,SAASC,YAAT,QAA6B,MAA7B;AAEA;AACA;AACA;;AACA,MAAMC,QAAN,CAAe;AACXC,EAAAA,WAAW,CAACC,IAAD,EAAOC,WAAP,EAAoBC,MAApB,EAA4B;AACnC,SAAKF,IAAL,GAAYA,IAAZ;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACH;;AALU;;AAOfJ,QAAQ,CAACK,IAAT,GAAgB,mBAAhB;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACA,MAAMC,aAAN,CAAoB;AAChBL,EAAAA,WAAW,CAACM,WAAD,EAAcC,KAAd,EAAqBC,OAAO,GAAG,MAA/B,EAAuC;AAC9C,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AALe;;AAOpBH,aAAa,CAACD,IAAd,GAAqB,wBAArB;AACA;AACA;AACA;;AACA,MAAMK,gBAAN,CAAuB;AACnBT,EAAAA,WAAW,CAACM,WAAD,EAAcC,KAAd,EAAqBC,OAAO,GAAG,MAA/B,EAAuC;AAC9C,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AALkB;;AAOvBC,gBAAgB,CAACL,IAAjB,GAAwB,2BAAxB;AACA;AACA;AACA;;AACA,MAAMM,YAAN,CAAmB;AACfV,EAAAA,WAAW,CAACM,WAAD,EAAcK,UAAd,EAA0BJ,KAA1B,EAAiCC,OAAO,GAAG,MAA3C,EAAmD;AAC1D,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKK,UAAL,GAAkBA,UAAlB;AACA,SAAKJ,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AANc;;AAQnBE,YAAY,CAACN,IAAb,GAAoB,uBAApB;AACA;AACA;AACA;;AACA,MAAMQ,WAAN,CAAkB;AACdZ,EAAAA,WAAW,CAACM,WAAD,EAAcK,UAAd,EAA0BJ,KAA1B,EAAiCC,OAAO,GAAG,MAA3C,EAAmD;AAC1D,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKK,UAAL,GAAkBA,UAAlB;AACA,SAAKJ,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AANa;;AAQlBI,WAAW,CAACR,IAAZ,GAAmB,sBAAnB;AACA;AACA;AACA;;AACA,MAAMS,kBAAN,CAAyB;AACrBb,EAAAA,WAAW,CAACM,WAAD,EAAcC,KAAd,EAAqBC,OAAO,GAAG,MAA/B,EAAuC;AAC9C,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AALoB;;AAOzBK,kBAAkB,CAACT,IAAnB,GAA0B,6BAA1B;AACA;AACA;AACA;;AACA,MAAMU,eAAN,CAAsB;AAClBd,EAAAA,WAAW,CAACM,WAAD,EAAcC,KAAd,EAAqBC,OAAO,GAAG,MAA/B,EAAuC;AAC9C,SAAKF,WAAL,GAAmBA,WAAnB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;;AALiB;;AAOtBM,eAAe,CAACV,IAAhB,GAAuB,0BAAvB;;AAEA,MAAMW,qBAAN,CAA4B;;AAE5B,MAAMC,4BAAN,CAAmC;AAC/BC,EAAAA,SAAS,CAACX,WAAD,EAAc;AACnB,WAAO;AACHY,MAAAA,IAAI,EAAE,KAAKC,cAAL,CAAoBb,WAAW,CAACY,IAAhC,CADH;AAEHE,MAAAA,GAAG,EAAEd,WAAW,CAACc;AAFd,KAAP;AAIH;;AACDD,EAAAA,cAAc,CAACE,KAAD,EAAQ;AAClB,UAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAN,CAAeC,GAAf,CAAmBC,CAAC,IAAI,KAAKL,cAAL,CAAoBK,CAApB,CAAxB,CAAjB;AACA,WAAO;AACHJ,MAAAA,GAAG,EAAEC,KAAK,CAACD,GADR;AAEHK,MAAAA,MAAM,EAAEJ,KAAK,CAACI,MAFX;AAGHvB,MAAAA,WAAW,EAAEmB,KAAK,CAACnB,WAHhB;AAIHwB,MAAAA,QAAQ,EAAEL,KAAK,CAACK,QAJb;AAKHC,MAAAA,IAAI,EAAEN,KAAK,CAACM,IALT;AAMHC,MAAAA,MAAM,EAAEP,KAAK,CAACO,MANX;AAOHC,MAAAA,SAAS,EAAE,IAPR;AAQHC,MAAAA,WAAW,EAAE,IARV;AASHZ,MAAAA,IAAI,EAAE,IATH;AAUHa,MAAAA,MAAM,EAAE,IAVL;AAWHC,MAAAA,UAAU,EAAEV,QAAQ,CAAC,CAAD,CAXjB;AAYHA,MAAAA,QAAQ,EAAEA,QAZP;AAaHW,MAAAA,YAAY,EAAE,IAbX;AAcHC,MAAAA,QAAQ,EAAEb,KAAK,CAACa,QAdb;AAeHC,MAAAA,aAAa,EAAEd,KAAK,CAACc,aAflB;AAgBHC,MAAAA,QAAQ,EAAEf,KAAK,CAACe;AAhBb,KAAP;AAkBH;;AA3B8B;;AA8BnC,MAAMC,YAAY,GAAG,IAAI5D,cAAJ,CAAmB,cAAnB,EAAmC;AAAE6D,EAAAA,UAAU,EAAE,MAAd;AAAsBC,EAAAA,OAAO,EAAE,MAAMC;AAArC,CAAnC,CAArB;AACA,MAAMC,0BAA0B,GAAG,IAAIhE,cAAJ,CAAmB,4BAAnB,EAAiD;AAAE6D,EAAAA,UAAU,EAAE,MAAd;AAAsBC,EAAAA,OAAO,EAAE,OAAO,EAAP;AAA/B,CAAjD,CAAnC;;AACA,SAASG,yBAAT,CAAmCC,OAAnC,EAA4C;AACxC,SAAO;AACHC,IAAAA,sBAAsB,EAAGD,OAAO,IAAIA,OAAO,CAACC,sBAApB,IAA+C;AAAE;;AADtE,GAAP;AAGH;;AAED,IAAIC,aAAJ;AACA,IAAIC,WAAW,GAAGD,aAAa,GAAG,MAAMC,WAAN,CAAkB;AAChD9C,EAAAA,WAAW,CAAC+C,MAAD,EAASC,OAAT,EAAkBC,WAAlB,EAA+BC,OAA/B,EAAwCC,QAAxC,EAAkD;AACzD,SAAKJ,MAAL,GAAcA,MAAd;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA;AACR;AACA;AACA;;AACQ,SAAKE,QAAL,GAAgB,MAAhB;AACA;AACR;AACA;;AACQ,SAAKC,YAAL,GAAoB,IAApB;AACA;AACR;AACA;;AACQ,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,aAAL,GAAqB,IAAI1D,YAAJ,EAArB;AACA,SAAK2D,QAAL,GAAgB,IAAhB,CApByD,CAqBzD;AACA;;AACA,SAAKA,QAAL,GAAgBN,QAAQ,CAACO,GAAT,CAAajB,0BAAb,EAAyC,IAAzC,CAAhB;;AACA,SAAKkB,mBAAL;;AACA,SAAKC,0BAAL;AACH;;AACW,SAALC,KAAK,CAACA,KAAD,EAAQ;AAChB,WAAOA,KAAK,IAAIA,KAAK,CAACA,KAAtB;AACH;;AACS,SAAHzC,GAAG,CAACyC,KAAD,EAAQ;AACd,WAAOA,KAAK,IAAIA,KAAK,CAACA,KAAf,IAAwBA,KAAK,CAACA,KAAN,CAAYzC,GAA3C;AACH;;AACD0C,EAAAA,WAAW,GAAG;AACV,SAAKN,aAAL,CAAmBO,WAAnB;AACH;;AACDC,EAAAA,QAAQ,CAACC,CAAD,EAAIC,MAAJ,EAAY;AAChB,WAAO,KAAKhB,OAAL,CAAaiB,GAAb,CAAiB,MAAM,KAAKnB,OAAL,CAAagB,QAAb,CAAsBE,MAAM,CAACjE,IAA7B,EAAmCmE,MAAM,CAACC,MAAP,CAAc;AAAEnE,MAAAA,WAAW,EAAEgE,MAAM,CAAChE;AAAtB,KAAd,EAAmDgE,MAAM,CAAC/D,MAA1D,CAAnC,CAAvB,CAAP;AACH;;AACDmE,EAAAA,mBAAmB,CAACC,GAAD,EAAML,MAAN,EAAc;AAC7BK,IAAAA,GAAG,CAACC,QAAJ,CAAa;AACThE,MAAAA,OAAO,EAAE0D,MAAM,CAAC1D,OADP;AAETqD,MAAAA,KAAK,EAAEK,MAAM,CAAC5D,WAFL;AAGTmE,MAAAA,YAAY,EAAEP,MAAM,CAAC3D,KAAP,CAAamE;AAHlB,KAAb;AAKH;;AACDf,EAAAA,mBAAmB,GAAG;AAClB,UAAMgB,YAAY,GAAG,KAAK5B,MAAL,CAChB6B,MADgB,CACT/B,aADS,EAEhBgC,SAFgB,CAELhB,KAAD,IAAW;AACtB,WAAKiB,iBAAL,CAAuBjB,KAAvB;AACH,KAJoB,CAArB;;AAKA,SAAKL,aAAL,CAAmBuB,GAAnB,CAAuBJ,YAAvB;AACH;;AACDG,EAAAA,iBAAiB,CAACxE,WAAD,EAAc;AAC3B,QAAIA,WAAW,IAAIA,WAAW,CAACE,OAAZ,KAAwB,UAA3C,EAAuD;AACnD,WAAK8C,WAAL,GAAmB,KAAKP,MAAL,CAAYiC,cAAZ,CAA2BnC,aAA3B,CAAnB;AACH;;AACD,UAAMoC,iBAAiB,GAAG,CAAC,KAAK3B,WAAN,IACtB,CAAC,KAAKA,WAAL,CAAiBO,KADI,IAEtB,CAACvD,WAFqB,IAGtBA,WAAW,CAACE,OAAZ,KAAwB,QAHF,IAItB,KAAKwC,OAAL,CAAa5B,GAAb,KAAqB,KAAKkC,WAAL,CAAiBO,KAAjB,CAAuBzC,GAJtB,IAKtB,KAAKmC,UAAL,YAA2BhE,eAL/B;;AAMA,QAAI0F,iBAAJ,EAAuB;AACnB;AACH;;AACD,SAAK3B,WAAL,GAAmB,KAAKP,MAAL,CAAYiC,cAAZ,CAA2BnC,aAA3B,CAAnB;AACA,SAAKO,QAAL,GAAgB,OAAhB;;AACA,SAAKF,OAAL,CAAaiB,GAAb,CAAiB,MAAM,KAAKnB,OAAL,CAAakC,aAAb,CAA2B,KAAK5B,WAAL,CAAiBO,KAAjB,CAAuBzC,GAAlD,CAAvB;AACH;;AACDwC,EAAAA,0BAA0B,GAAG;AACzB,UAAMuB,4BAA4B,GAAG,KAAK1B,QAAL,IAAiB,IAAjB,IACjC,KAAKA,QAAL,CAAcb,sBAAd,KAAyC;AAAE;AAD/C;AAEA,QAAIwC,oBAAJ;;AACA,UAAMT,YAAY,GAAG,KAAK3B,OAAL,CAAaqC,MAAb,CAAoBR,SAApB,CAA8BtE,KAAK,IAAI;AACxD,WAAKgD,UAAL,GAAkBhD,KAAlB;;AACA,UAAIA,KAAK,YAAYhB,eAArB,EAAsC;AAClC,aAAK+F,gBAAL,CAAsB/E,KAAtB;AACH,OAFD,MAGK,IAAIA,KAAK,YAAYf,gBAArB,EAAuC;AACxC4F,QAAAA,oBAAoB,GAAG7E,KAAvB;;AACA,YAAI,CAAC4E,4BAAD,IAAiC,KAAK/B,QAAL,KAAkB,OAAvD,EAAgE;AAC5D,eAAKmC,yBAAL,CAA+BH,oBAA/B;AACH;AACJ,OALI,MAMA,IAAI7E,KAAK,YAAYd,UAArB,EAAiC;AAClC,aAAK+F,2BAAL,CAAiCjF,KAAjC;AACH,OAFI,MAGA,IAAIA,KAAK,YAAYb,gBAArB,EAAuC;AACxC,aAAK+F,qBAAL,CAA2BlF,KAA3B;;AACA,aAAKmF,MAAL;AACH,OAHI,MAIA,IAAInF,KAAK,YAAYZ,eAArB,EAAsC;AACvC,aAAKgG,oBAAL,CAA0BpF,KAA1B;;AACA,aAAKmF,MAAL;AACH,OAHI,MAIA,IAAInF,KAAK,YAAYX,aAArB,EAAoC;AACrC,YAAI,KAAKwD,QAAL,KAAkB,OAAtB,EAA+B;AAC3B,cAAI+B,4BAAJ,EAAkC;AAC9B,iBAAKI,yBAAL,CAA+BH,oBAA/B;AACH;;AACD,eAAKQ,wBAAL,CAA8BrF,KAA9B;AACH;;AACD,aAAKmF,MAAL;AACH;AACJ,KA/BoB,CAArB;;AAgCA,SAAKlC,aAAL,CAAmBuB,GAAnB,CAAuBJ,YAAvB;AACH;AACD;;;AACAW,EAAAA,gBAAgB,CAAC/E,KAAD,EAAQ;AACpB,SAAK8C,YAAL,GAAoB,KAAKJ,WAAL,CAAiBhC,SAAjB,CAA2B,KAAK+B,OAAL,CAAa1C,WAAb,CAAyBuF,QAApD,CAApB;;AACA,QAAI,KAAKzC,QAAL,KAAkB,MAAtB,EAA8B;AAC1B,WAAKE,WAAL,GAAmB,KAAKP,MAAL,CAAYiC,cAAZ,CAA2BnC,aAA3B,CAAnB;;AACA,WAAKiD,qBAAL,CAA2B,IAAIzF,aAAJ,CAAkB,KAAKgD,YAAvB,EAAqC9C,KAArC,EAA4C,KAAK6C,QAAjD,CAA3B;AACH;AACJ;AACD;;;AACAoC,EAAAA,2BAA2B,CAACjF,KAAD,EAAQ;AAC/B,UAAMD,WAAW,GAAG,KAAK2C,WAAL,CAAiBhC,SAAjB,CAA2BV,KAAK,CAACsD,KAAjC,CAApB;;AACA,SAAKiC,qBAAL,CAA2B,IAAIjF,kBAAJ,CAAuBP,WAAvB,EAAoCC,KAApC,EAA2C,KAAK6C,QAAhD,CAA3B;AACH;AACD;;;AACAmC,EAAAA,yBAAyB,CAACH,oBAAD,EAAuB;AAC5C,UAAMW,eAAe,GAAG,KAAK9C,WAAL,CAAiBhC,SAAjB,CAA2BmE,oBAAoB,CAACvB,KAAhD,CAAxB;;AACA,SAAKiC,qBAAL,CAA2B,IAAIrF,gBAAJ,CAAqBsF,eAArB,EAAsC,IAAIvG,gBAAJ,CAAqB4F,oBAAoB,CAACV,EAA1C,EAA8CU,oBAAoB,CAAChE,GAAnE,EAAwEgE,oBAAoB,CAACY,iBAA7F,EAAgHD,eAAhH,CAAtC,EAAwK,KAAK3C,QAA7K,CAA3B;AACH;AACD;;;AACAqC,EAAAA,qBAAqB,CAAClF,KAAD,EAAQ;AACzB,SAAKuF,qBAAL,CAA2B,IAAIpF,YAAJ,CAAiB,KAAK2C,YAAtB,EAAoC,KAAKC,WAAzC,EAAsD/C,KAAtD,EAA6D,KAAK6C,QAAlE,CAA3B;AACH;AACD;;;AACAuC,EAAAA,oBAAoB,CAACpF,KAAD,EAAQ;AACxB,SAAKuF,qBAAL,CAA2B,IAAIlF,WAAJ,CAAgB,KAAKyC,YAArB,EAAmC,KAAKC,WAAxC,EAAqD,IAAI3D,eAAJ,CAAoBY,KAAK,CAACmE,EAA1B,EAA8BnE,KAAK,CAACa,GAApC,EAA0C,GAAEb,KAAM,EAAlD,CAArD,EAA2G,KAAK6C,QAAhH,CAA3B;AACH;AACD;;;AACAwC,EAAAA,wBAAwB,CAACrF,KAAD,EAAQ;AAC5B,UAAMD,WAAW,GAAG,KAAK2C,WAAL,CAAiBhC,SAAjB,CAA2B,KAAK+B,OAAL,CAAa1C,WAAb,CAAyBuF,QAApD,CAApB;;AACA,SAAKC,qBAAL,CAA2B,IAAIhF,eAAJ,CAAoBR,WAApB,EAAiCC,KAAjC,EAAwC,KAAK6C,QAA7C,CAA3B;AACH;;AACD0C,EAAAA,qBAAqB,CAAC5B,MAAD,EAAS;AAC1B,SAAKd,QAAL,GAAgB,QAAhB;;AACA,QAAI;AACA,WAAKL,MAAL,CAAYkD,QAAZ,CAAqB/B,MAArB;AACH,KAFD,SAGQ;AACJ,WAAKd,QAAL,GAAgB,MAAhB;AACH;AACJ;;AACDsC,EAAAA,MAAM,GAAG;AACL,SAAKtC,QAAL,GAAgB,MAAhB;AACA,SAAKE,WAAL,GAAmB,IAAnB;AACA,SAAKD,YAAL,GAAoB,IAApB;AACH;;AA1J+C,CAApD;AA4JA;;AAAmBP,WAAW,CAACoD,IAAZ;AAAA,mBAAyGpD,WAAzG,EAA+FtE,EAA/F,UAAsIM,EAAE,CAACI,KAAzI,GAA+FV,EAA/F,UAA2Jc,EAAE,CAACO,MAA9J,GAA+FrB,EAA/F,UAAiLuC,qBAAjL,GAA+FvC,EAA/F,UAAmNA,EAAE,CAACE,MAAtN,GAA+FF,EAA/F,UAAyOA,EAAE,CAACG,QAA5O;AAAA;AACnB;;;AAAmBmE,WAAW,CAACqD,KAAZ,kBAD+F3H,EAC/F;AAAA,SAA6GsE,WAA7G;AAAA,WAA6GA,WAA7G;AAAA;;AACnB1D,UAAU,CAAC,CACPL,MAAM,CAACgB,QAAD,CADC,EAEPV,UAAU,CAAC,aAAD,EAAgB+G,QAAhB,CAFH,EAGP/G,UAAU,CAAC,mBAAD,EAAsB,CAAC+E,MAAD,EAASrE,QAAT,CAAtB,CAHH,EAIPV,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKPyD,WAAW,CAACuD,SALL,EAKgB,UALhB,EAK4B,IAL5B,CAAV;;AAMAjH,UAAU,CAAC,CACPL,MAAM,CAAC,CACHsB,aADG,EAEHI,gBAFG,EAGHG,WAHG,EAIHF,YAJG,EAKHG,kBALG,EAMHC,eANG,CAAD,CADC,EASPzB,UAAU,CAAC,aAAD,EAAgB+G,QAAhB,CATH,EAUP/G,UAAU,CAAC,mBAAD,EAAsB,CAAC+E,MAAD,EAASA,MAAT,CAAtB,CAVH,EAWP/E,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAXH,CAAD,EAYPyD,WAAW,CAACuD,SAZL,EAYgB,qBAZhB,EAYuC,IAZvC,CAAV;;AAaAjH,UAAU,CAAC,CACPJ,QAAQ,EADD,EAEPK,UAAU,CAAC,aAAD,EAAgB+G,QAAhB,CAFH,EAGP/G,UAAU,CAAC,mBAAD,EAAsB,CAAC+E,MAAD,CAAtB,CAHH,EAIP/E,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKPyD,WALO,EAKM,OALN,EAKe,IALf,CAAV;;AAMA1D,UAAU,CAAC,CACPJ,QAAQ,EADD,EAEPK,UAAU,CAAC,aAAD,EAAgB+G,QAAhB,CAFH,EAGP/G,UAAU,CAAC,mBAAD,EAAsB,CAAC+E,MAAD,CAAtB,CAHH,EAIP/E,UAAU,CAAC,mBAAD,EAAsB+E,MAAtB,CAJH,CAAD,EAKPtB,WALO,EAKM,KALN,EAKa,IALb,CAAV;;AAMAA,WAAW,GAAGD,aAAa,GAAGzD,UAAU,CAAC,CACrCH,KAAK,CAAC;AACFqH,EAAAA,IAAI,EAAE,QADJ;AAEFC,EAAAA,QAAQ,EAAE;AACN1C,IAAAA,KAAK,EAAErB,SADD;AAENiC,IAAAA,YAAY,EAAEjC,SAFR;AAGNhC,IAAAA,OAAO,EAAE;AAHH;AAFR,CAAD,CADgC,EASrCnB,UAAU,CAAC,mBAAD,EAAsB,CAACH,KAAD,EAC5BW,MAD4B,EAE5BkB,qBAF4B,EAG5BrC,MAH4B,EAI5BC,QAJ4B,CAAtB,CAT2B,CAAD,EAcrCmE,WAdqC,CAAxC;;AAeA;AAAA,qDAhDkHtE,EAgDlH,mBAA4FsE,WAA5F,EAAqH,CAAC;AAC1G1C,IAAAA,IAAI,EAAExB;AADoG,GAAD,CAArH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEwB,MAAAA,IAAI,EAAEtB,EAAE,CAACI;AAAX,KAAD,EAAqB;AAAEkB,MAAAA,IAAI,EAAEd,EAAE,CAACO;AAAX,KAArB,EAA0C;AAAEO,MAAAA,IAAI,EAAEW;AAAR,KAA1C,EAA2E;AAAEX,MAAAA,IAAI,EAAE5B,EAAE,CAACE;AAAX,KAA3E,EAAgG;AAAE0B,MAAAA,IAAI,EAAE5B,EAAE,CAACG;AAAX,KAAhG,CAAP;AAAgI,GAF1K,EAE4L;AAAEqF,IAAAA,QAAQ,EAAE,EAAZ;AAAgBM,IAAAA,mBAAmB,EAAE;AAArC,GAF5L;AAAA;;AAIA,MAAMkC,sBAAN,CAA6B;AACX,SAAPC,OAAO,CAAC9D,OAAD,EAAU;AACpB,WAAO;AACH+D,MAAAA,QAAQ,EAAEF,sBADP;AAEHG,MAAAA,SAAS,EAAE,CACP;AAAEC,QAAAA,OAAO,EAAEvE,YAAX;AAAyBwE,QAAAA,QAAQ,EAAElE;AAAnC,OADO,EAEP;AACIiE,QAAAA,OAAO,EAAEnE,0BADb;AAEIqE,QAAAA,UAAU,EAAEpE,yBAFhB;AAGIqE,QAAAA,IAAI,EAAE,CAAC1E,YAAD;AAHV,OAFO,EAOP;AAAEuE,QAAAA,OAAO,EAAE7F,qBAAX;AAAkCiG,QAAAA,QAAQ,EAAEhG;AAA5C,OAPO;AAFR,KAAP;AAYH;;AAdwB;AAgB7B;;;AAAmBwF,sBAAsB,CAACN,IAAvB;AAAA,mBAAoHM,sBAApH;AAAA;AACnB;;;AAAmBA,sBAAsB,CAACS,IAAvB,kBArE+FzI,EAqE/F;AAAA,QAAqHgI;AAArH;AACnB;;AAAmBA,sBAAsB,CAACU,IAAvB,kBAtE+F1I,EAsE/F;AAAA,YAAuJ,CAACW,UAAU,CAACgI,UAAX,CAAsB,CAACrE,WAAD,CAAtB,CAAD,CAAvJ;AAAA;;AACnB;AAAA,qDAvEkHtE,EAuElH,mBAA4FgI,sBAA5F,EAAgI,CAAC;AACrHpG,IAAAA,IAAI,EAAEvB,QAD+G;AAErHuI,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAAClI,UAAU,CAACgI,UAAX,CAAsB,CAACrE,WAAD,CAAtB,CAAD;AADV,KAAD;AAF+G,GAAD,CAAhI;AAAA;AAOA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS9B,4BAAT,EAAuCjB,QAAvC,EAAiDyG,sBAAjD,EAAyE9F,YAAzE,EAAuFG,kBAAvF,EAA2GD,WAA3G,EAAwHE,eAAxH,EAAyIL,gBAAzI,EAA2JJ,aAA3J,EAA0KyC,WAA1K,EAAuL/B,qBAAvL", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, NgZone, Injector, Injectable, NgModule } from '@angular/core';\nimport * as i1 from '@ngxs/store';\nimport { Action, Selector, State, Store, NgxsModule } from '@ngxs/store';\nimport { __decorate, __metadata } from 'tslib';\nimport * as i2 from '@angular/router';\nimport { NavigationStart, RoutesRecognized, ResolveEnd, NavigationCancel, NavigationError, NavigationEnd, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\n/**\n * Public event api of the router\n */\nclass Navigate {\n    constructor(path, queryParams, extras) {\n        this.path = path;\n        this.queryParams = queryParams;\n        this.extras = extras;\n    }\n}\nNavigate.type = '[Router] Navigate';\n/**\n *\n * Angular Routers internal state events\n *\n */\n/**\n * An action dispatched when the router starts the navigation.\n */\nclass RouterRequest {\n    constructor(routerState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterRequest.type = '[Router] RouterRequest';\n/**\n * An action dispatched when the router navigates.\n */\nclass RouterNavigation {\n    constructor(routerState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterNavigation.type = '[Router] RouterNavigation';\n/**\n * An action dispatched when the router cancel navigation.\n */\nclass RouterCancel {\n    constructor(routerState, storeState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.storeState = storeState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterCancel.type = '[Router] RouterCancel';\n/**\n * An action dispatched when the router errors.\n */\nclass RouterError {\n    constructor(routerState, storeState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.storeState = storeState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterError.type = '[Router] RouterError';\n/**\n * An action dispatched when the `ResolveEnd` event is triggered.\n */\nclass RouterDataResolved {\n    constructor(routerState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterDataResolved.type = '[Router] RouterDataResolved';\n/**\n * An action dispatched when the router navigation has been finished successfully.\n */\nclass RouterNavigated {\n    constructor(routerState, event, trigger = 'none') {\n        this.routerState = routerState;\n        this.event = event;\n        this.trigger = trigger;\n    }\n}\nRouterNavigated.type = '[Router] RouterNavigated';\n\nclass RouterStateSerializer {\n}\nclass DefaultRouterStateSerializer {\n    serialize(routerState) {\n        return {\n            root: this.serializeRoute(routerState.root),\n            url: routerState.url\n        };\n    }\n    serializeRoute(route) {\n        const children = route.children.map(c => this.serializeRoute(c));\n        return {\n            url: route.url,\n            params: route.params,\n            queryParams: route.queryParams,\n            fragment: route.fragment,\n            data: route.data,\n            outlet: route.outlet,\n            component: null,\n            routeConfig: null,\n            root: null,\n            parent: null,\n            firstChild: children[0],\n            children: children,\n            pathFromRoot: null,\n            paramMap: route.paramMap,\n            queryParamMap: route.queryParamMap,\n            toString: route.toString\n        };\n    }\n}\n\nconst USER_OPTIONS = new InjectionToken('USER_OPTIONS', { providedIn: 'root', factory: () => undefined });\nconst NGXS_ROUTER_PLUGIN_OPTIONS = new InjectionToken('NGXS_ROUTER_PLUGIN_OPTIONS', { providedIn: 'root', factory: () => ({}) });\nfunction createRouterPluginOptions(options) {\n    return {\n        navigationActionTiming: (options && options.navigationActionTiming) || 1 /* PreActivation */\n    };\n}\n\nvar RouterState_1;\nlet RouterState = RouterState_1 = class RouterState {\n    constructor(_store, _router, _serializer, _ngZone, injector) {\n        this._store = _store;\n        this._router = _router;\n        this._serializer = _serializer;\n        this._ngZone = _ngZone;\n        /**\n         * Determines how navigation was performed by the `RouterState` itself\n         * or outside via `new Navigate(...)`\n         */\n        this._trigger = 'none';\n        /**\n         * That's the serialized state from the `Router` class\n         */\n        this._routerState = null;\n        /**\n         * That's the value of the `RouterState` state\n         */\n        this._storeState = null;\n        this._lastEvent = null;\n        this._subscription = new Subscription();\n        this._options = null;\n        // Note: do not use `@Inject` since it fails on lower versions of Angular with Jest\n        // integration, it cannot resolve the token provider.\n        this._options = injector.get(NGXS_ROUTER_PLUGIN_OPTIONS, null);\n        this._setUpStoreListener();\n        this._setUpRouterEventsListener();\n    }\n    static state(state) {\n        return state && state.state;\n    }\n    static url(state) {\n        return state && state.state && state.state.url;\n    }\n    ngOnDestroy() {\n        this._subscription.unsubscribe();\n    }\n    navigate(_, action) {\n        return this._ngZone.run(() => this._router.navigate(action.path, Object.assign({ queryParams: action.queryParams }, action.extras)));\n    }\n    angularRouterAction(ctx, action) {\n        ctx.setState({\n            trigger: action.trigger,\n            state: action.routerState,\n            navigationId: action.event.id\n        });\n    }\n    _setUpStoreListener() {\n        const subscription = this._store\n            .select(RouterState_1)\n            .subscribe((state) => {\n            this._navigateIfNeeded(state);\n        });\n        this._subscription.add(subscription);\n    }\n    _navigateIfNeeded(routerState) {\n        if (routerState && routerState.trigger === 'devtools') {\n            this._storeState = this._store.selectSnapshot(RouterState_1);\n        }\n        const canSkipNavigation = !this._storeState ||\n            !this._storeState.state ||\n            !routerState ||\n            routerState.trigger === 'router' ||\n            this._router.url === this._storeState.state.url ||\n            this._lastEvent instanceof NavigationStart;\n        if (canSkipNavigation) {\n            return;\n        }\n        this._storeState = this._store.selectSnapshot(RouterState_1);\n        this._trigger = 'store';\n        this._ngZone.run(() => this._router.navigateByUrl(this._storeState.state.url));\n    }\n    _setUpRouterEventsListener() {\n        const dispatchRouterNavigationLate = this._options != null &&\n            this._options.navigationActionTiming === 2 /* PostActivation */;\n        let lastRoutesRecognized;\n        const subscription = this._router.events.subscribe(event => {\n            this._lastEvent = event;\n            if (event instanceof NavigationStart) {\n                this._navigationStart(event);\n            }\n            else if (event instanceof RoutesRecognized) {\n                lastRoutesRecognized = event;\n                if (!dispatchRouterNavigationLate && this._trigger !== 'store') {\n                    this._dispatchRouterNavigation(lastRoutesRecognized);\n                }\n            }\n            else if (event instanceof ResolveEnd) {\n                this._dispatchRouterDataResolved(event);\n            }\n            else if (event instanceof NavigationCancel) {\n                this._dispatchRouterCancel(event);\n                this._reset();\n            }\n            else if (event instanceof NavigationError) {\n                this._dispatchRouterError(event);\n                this._reset();\n            }\n            else if (event instanceof NavigationEnd) {\n                if (this._trigger !== 'store') {\n                    if (dispatchRouterNavigationLate) {\n                        this._dispatchRouterNavigation(lastRoutesRecognized);\n                    }\n                    this._dispatchRouterNavigated(event);\n                }\n                this._reset();\n            }\n        });\n        this._subscription.add(subscription);\n    }\n    /** Reacts to `NavigationStart`. */\n    _navigationStart(event) {\n        this._routerState = this._serializer.serialize(this._router.routerState.snapshot);\n        if (this._trigger !== 'none') {\n            this._storeState = this._store.selectSnapshot(RouterState_1);\n            this._dispatchRouterAction(new RouterRequest(this._routerState, event, this._trigger));\n        }\n    }\n    /** Reacts to `ResolveEnd`. */\n    _dispatchRouterDataResolved(event) {\n        const routerState = this._serializer.serialize(event.state);\n        this._dispatchRouterAction(new RouterDataResolved(routerState, event, this._trigger));\n    }\n    /** Reacts to `RoutesRecognized` or `NavigationEnd`, depends on the `navigationActionTiming`. */\n    _dispatchRouterNavigation(lastRoutesRecognized) {\n        const nextRouterState = this._serializer.serialize(lastRoutesRecognized.state);\n        this._dispatchRouterAction(new RouterNavigation(nextRouterState, new RoutesRecognized(lastRoutesRecognized.id, lastRoutesRecognized.url, lastRoutesRecognized.urlAfterRedirects, nextRouterState), this._trigger));\n    }\n    /** Reacts to `NavigationCancel`. */\n    _dispatchRouterCancel(event) {\n        this._dispatchRouterAction(new RouterCancel(this._routerState, this._storeState, event, this._trigger));\n    }\n    /** Reacts to `NavigationEnd`. */\n    _dispatchRouterError(event) {\n        this._dispatchRouterAction(new RouterError(this._routerState, this._storeState, new NavigationError(event.id, event.url, `${event}`), this._trigger));\n    }\n    /** Reacts to `NavigationEnd`. */\n    _dispatchRouterNavigated(event) {\n        const routerState = this._serializer.serialize(this._router.routerState.snapshot);\n        this._dispatchRouterAction(new RouterNavigated(routerState, event, this._trigger));\n    }\n    _dispatchRouterAction(action) {\n        this._trigger = 'router';\n        try {\n            this._store.dispatch(action);\n        }\n        finally {\n            this._trigger = 'none';\n        }\n    }\n    _reset() {\n        this._trigger = 'none';\n        this._storeState = null;\n        this._routerState = null;\n    }\n};\n/** @nocollapse */ RouterState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: RouterState, deps: [{ token: i1.Store }, { token: i2.Router }, { token: RouterStateSerializer }, { token: i0.NgZone }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ RouterState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: RouterState });\n__decorate([\n    Action(Navigate),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", [Object, Navigate]),\n    __metadata(\"design:returntype\", void 0)\n], RouterState.prototype, \"navigate\", null);\n__decorate([\n    Action([\n        RouterRequest,\n        RouterNavigation,\n        RouterError,\n        RouterCancel,\n        RouterDataResolved,\n        RouterNavigated\n    ]),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", [Object, Object]),\n    __metadata(\"design:returntype\", void 0)\n], RouterState.prototype, \"angularRouterAction\", null);\n__decorate([\n    Selector(),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", [Object]),\n    __metadata(\"design:returntype\", void 0)\n], RouterState, \"state\", null);\n__decorate([\n    Selector(),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", [Object]),\n    __metadata(\"design:returntype\", Object)\n], RouterState, \"url\", null);\nRouterState = RouterState_1 = __decorate([\n    State({\n        name: 'router',\n        defaults: {\n            state: undefined,\n            navigationId: undefined,\n            trigger: 'none'\n        }\n    }),\n    __metadata(\"design:paramtypes\", [Store,\n        Router,\n        RouterStateSerializer,\n        NgZone,\n        Injector])\n], RouterState);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: RouterState, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.Store }, { type: i2.Router }, { type: RouterStateSerializer }, { type: i0.NgZone }, { type: i0.Injector }]; }, propDecorators: { navigate: [], angularRouterAction: [] } });\n\nclass NgxsRouterPluginModule {\n    static forRoot(options) {\n        return {\n            ngModule: NgxsRouterPluginModule,\n            providers: [\n                { provide: USER_OPTIONS, useValue: options },\n                {\n                    provide: NGXS_ROUTER_PLUGIN_OPTIONS,\n                    useFactory: createRouterPluginOptions,\n                    deps: [USER_OPTIONS]\n                },\n                { provide: RouterStateSerializer, useClass: DefaultRouterStateSerializer }\n            ]\n        };\n    }\n}\n/** @nocollapse */ NgxsRouterPluginModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRouterPluginModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsRouterPluginModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRouterPluginModule, imports: [i1.ɵNgxsFeatureModule] });\n/** @nocollapse */ NgxsRouterPluginModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRouterPluginModule, imports: [[NgxsModule.forFeature([RouterState])]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRouterPluginModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NgxsModule.forFeature([RouterState])]\n                }]\n        }] });\n\n/**\n * The public api for consumers of @ngxs/router-plugin\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultRouterStateSerializer, Navigate, NgxsRouterPluginModule, RouterCancel, RouterDataResolved, RouterError, RouterNavigated, RouterNavigation, RouterRequest, RouterState, RouterStateSerializer };\n"]}, "metadata": {}, "sourceType": "module"}