(function () {
  "use strict";

  function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }

  function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }

  function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }

  function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

  function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }

  (self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["angular_src_app_modules_popup_popup1_module_ts"], {
    /***/
    4971:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      // ESM COMPAT FLAG
      __webpack_require__.r(__webpack_exports__); // EXPORTS


      __webpack_require__.d(__webpack_exports__, {
        "Popup1Module": function Popup1Module() {
          return (
            /* binding */
            _Popup1Module
          );
        }
      }); // EXTERNAL MODULE: ./angular/src/app/providers/tab-id.provider.ts


      var tab_id_provider = __webpack_require__(3154); // EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules


      var router = __webpack_require__(24); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/action/extrtact-anywebsite/extrtact-anywebsite.component.ts


      var extrtact_anywebsite_component = __webpack_require__(7435); // EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js


      var core = __webpack_require__(2316);

      ; // CONCATENATED MODULE: ./angular/src/app/modules/popup/extract-routing.module.ts

      var routes = [{
        path: '',
        component: extrtact_anywebsite_component
        /* ExtrtactAnywebsiteComponent */
        .c
      }];

      var extractRoutingModule = /*#__PURE__*/_createClass(function extractRoutingModule() {
        _classCallCheck(this, extractRoutingModule);
      });

      extractRoutingModule.ɵfac = function extractRoutingModule_Factory(t) {
        return new (t || extractRoutingModule)();
      };

      extractRoutingModule.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: extractRoutingModule
      });
      extractRoutingModule.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        imports: [[router
        /* RouterModule.forChild */
        .Bz.forChild(routes)], router
        /* RouterModule */
        .Bz]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(extractRoutingModule, {
          imports: [router
          /* RouterModule */
          .Bz],
          exports: [router
          /* RouterModule */
          .Bz]
        });
      })(); // EXTERNAL MODULE: ./angular/src/app/modules/popup/common.module.ts + 12 modules


      var common_module = __webpack_require__(8451);

      ; // CONCATENATED MODULE: ./angular/src/app/modules/popup/popup1.module.ts

      var _Popup1Module = /*#__PURE__*/_createClass(function _Popup1Module() {
        _classCallCheck(this, _Popup1Module);
      });

      _Popup1Module.ɵfac = function Popup1Module_Factory(t) {
        return new (t || _Popup1Module)();
      };

      _Popup1Module.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: _Popup1Module
      });
      _Popup1Module.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        providers: [{
          provide: tab_id_provider
          /* TAB_ID */
          .$,
          useValue: 0
        }],
        imports: [[common_module
        /* CommonPopupModule */
        .A, extractRoutingModule]]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(_Popup1Module, {
          imports: [common_module
          /* CommonPopupModule */
          .A, extractRoutingModule]
        });
      })();
      /***/

    }
  }]);
})();
//# sourceMappingURL=angular_src_app_modules_popup_popup1_module_ts-es5.js.map