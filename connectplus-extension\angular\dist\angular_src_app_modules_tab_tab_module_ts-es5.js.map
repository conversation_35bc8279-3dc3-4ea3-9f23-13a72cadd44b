{"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAOaA;;;;;yBAAAA;AAAY;;;;;cAAZA;AAAYC;AAAAC;AAAAC;AAAAC;AAAAC;AAAA;ACPzBC;AAAA;AAAA;;AAA8BA;AAAA;AAAA;;AAAGA;AAAA;AAAA;AACjCA;AAAA;AAAA;;AAA6BA;AAAA;AAAA;;AAAqBA;AAAA;AAAA;;;;;;;;;;ACGlD,UAAMC,MAAM,GAAW,CACrB;AACEC,YAAI,EAAE,EADR;AAEEC,iBAAS,EAAET;AAFb,OADqB,CAAvB;;UAWaU;;;;;yBAAAA;AAAgB;;;;;cAAhBA;;;;;kBAHF,CAACC;AAAA;AAAA,qBAAsBJ,MAAtB,CAAD,GACCI;AAAA;AAAA;;;;;;aAECD,kBAAgB;AAAAE;AAAA;AAAA;AAAAC,oBAFjBF;AAAA;AAAA,aAEiB;AAAA;AAFL;;;;UCJXG;;;;;yBAAAA;AAAS;;;;;cAATA;;;;;kBAFF,CAACC;AAAA;AAAA,WAAD,EAAeL,gBAAf;;;;;;aAEEI,YAAS;AAAAE,yBAHLhB,YAGK;AAHOY,oBACjBG;AAAA;AAAA,aADiB,EACHL,gBADG;AAGP;AAFoB;;;;", "names": ["TabComponent", "selectors", "decls", "vars", "consts", "template", "core", "routes", "path", "component", "TabRoutingModule", "router", "imports", "exports", "TabModule", "common", "declarations"], "sources": ["webpack:///angular/src/app/modules/tab/pages/tab/tab.component.ts", "webpack:///angular/src/app/modules/tab/pages/tab/tab.component.html", "webpack:///angular/src/app/modules/tab/tab-routing.module.ts", "webpack:///angular/src/app/modules/tab/tab.module.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-tab',\r\n  templateUrl: 'tab.component.html',\r\n  styleUrls: ['tab.component.scss']\r\n})\r\nexport class TabComponent {}\r\n", "<h1 style=\"text-align:center\">Tab</h1>\r\n<p style=\"text-align:center\">You opened a new tab!</p>\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TabComponent } from './pages/tab/tab.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: TabComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TabRoutingModule {}\r\n", "import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { TabComponent } from './pages/tab/tab.component';\r\nimport { TabRoutingModule } from './tab-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [TabComponent],\r\n  imports: [CommonModule, TabRoutingModule]\r\n})\r\nexport class TabModule {}\r\n"]}