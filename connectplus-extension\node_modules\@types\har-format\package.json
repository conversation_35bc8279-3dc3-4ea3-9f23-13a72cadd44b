{"_from": "@types/har-format@1.2.7", "_id": "@types/har-format@1.2.7", "_inBundle": false, "_integrity": "sha512-/TPzUG0tJn5x1TUcVLlDx2LqbE58hyOzDVAc9kf8SpOEmguHjU6bKUyfqb211AdqLOmU/SNyXvLKPNP5qTlfRw==", "_location": "/@types/har-format", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/har-format@1.2.7", "name": "@types/har-format", "escapedName": "@types%2fhar-format", "scope": "@types", "rawSpec": "1.2.7", "saveSpec": null, "fetchSpec": "1.2.7"}, "_requiredBy": ["#DEV:/", "#USER", "/@types/chrome"], "_resolved": "https://registry.npmjs.org/@types/har-format/-/har-format-1.2.7.tgz", "_shasum": "debfe36378f26c4fc2abca1df99f00a8ff94fd29", "_spec": "@types/har-format@1.2.7", "_where": "C:\\Users\\<USER>\\Downloads\\connectplus-extension\\connectplus-extension", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/micmro"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/marcelltoth"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for HAR", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/har-format", "license": "MIT", "main": "", "name": "@types/har-format", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/har-format"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "85cf407212ae7f3024cc92258123b2df2ea54d3436fc1fe6d12f75f0ac4ba977", "version": "1.2.7"}