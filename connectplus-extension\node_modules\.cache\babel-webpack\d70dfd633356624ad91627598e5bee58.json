{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\n\nclass NgxsBootstrapper {\n  constructor() {\n    /**\n     * Use `ReplaySubject`, thus we can get cached value even if the stream is completed\n     */\n    this.bootstrap$ = new ReplaySubject(1);\n  }\n\n  get appBootstrapped$() {\n    return this.bootstrap$.asObservable();\n  }\n  /**\n   * This event will be emitted after attaching `ComponentRef` of the root component\n   * to the tree of views, that's a signal that application has been fully rendered\n   */\n\n\n  bootstrap() {\n    this.bootstrap$.next(true);\n    this.bootstrap$.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsBootstrapper.ɵfac = function NgxsBootstrapper_Factory(t) {\n  return new (t || NgxsBootstrapper)();\n};\n/** @nocollapse */\n\n\nNgxsBootstrapper.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxsBootstrapper,\n  factory: NgxsBootstrapper.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsBootstrapper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nfunction defaultEqualityCheck(a, b) {\n  return a === b;\n}\n\nfunction areArgumentsShallowlyEqual(equalityCheck, prev, next) {\n  if (prev === null || next === null || prev.length !== next.length) {\n    return false;\n  } // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\n\n\n  const length = prev.length;\n\n  for (let i = 0; i < length; i++) {\n    if (!equalityCheck(prev[i], next[i])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n/**\n * Memoize a function on its last inputs only.\n * Originally from: https://github.com/reduxjs/reselect/blob/master/src/index.js\n *\n * @ignore\n */\n\n\nfunction memoize(func, equalityCheck = defaultEqualityCheck) {\n  let lastArgs = null;\n  let lastResult = null; // we reference arguments instead of spreading them for performance reasons\n\n  function memoized() {\n    // eslint-disable-next-line prefer-rest-params\n    if (!areArgumentsShallowlyEqual(equalityCheck, lastArgs, arguments)) {\n      // apply arguments instead of spreading for performance.\n      // eslint-disable-next-line prefer-rest-params, prefer-spread\n      lastResult = func.apply(null, arguments);\n    } // eslint-disable-next-line prefer-rest-params\n\n\n    lastArgs = arguments;\n    return lastResult;\n  }\n\n  memoized.reset = function () {\n    // The hidden (for now) ability to reset the memoization\n    lastArgs = null;\n    lastResult = null;\n  };\n\n  return memoized;\n}\n\nclass InitialState {\n  static set(state) {\n    this._value = state;\n  }\n\n  static pop() {\n    const state = this._value;\n    this._value = {};\n    return state;\n  }\n\n}\n\nInitialState._value = {};\nconst INITIAL_STATE_TOKEN = new InjectionToken('INITIAL_STATE_TOKEN', {\n  providedIn: 'root',\n  factory: () => InitialState.pop()\n}); // These tokens are internal and can change at any point.\n\nconst ɵNGXS_STATE_FACTORY = new InjectionToken('ɵNGXS_STATE_FACTORY');\nconst ɵNGXS_STATE_CONTEXT_FACTORY = new InjectionToken('ɵNGXS_STATE_CONTEXT_FACTORY');\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_STATE_TOKEN, InitialState, NgxsBootstrapper, memoize, ɵNGXS_STATE_CONTEXT_FACTORY, ɵNGXS_STATE_FACTORY };", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@ngxs/store/fesm2015/ngxs-store-internals.js"], "names": ["i0", "Injectable", "InjectionToken", "ReplaySubject", "NgxsBootstrapper", "constructor", "bootstrap$", "appBootstrapped$", "asObservable", "bootstrap", "next", "complete", "ɵfac", "ɵprov", "type", "args", "providedIn", "defaultEqualityCheck", "a", "b", "areArgumentsShallowlyEqual", "equalityCheck", "prev", "length", "i", "memoize", "func", "lastArgs", "lastResult", "memoized", "arguments", "apply", "reset", "InitialState", "set", "state", "_value", "pop", "INITIAL_STATE_TOKEN", "factory", "ɵNGXS_STATE_FACTORY", "ɵNGXS_STATE_CONTEXT_FACTORY"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,cAArB,QAA2C,eAA3C;AACA,SAASC,aAAT,QAA8B,MAA9B;;AAEA,MAAMC,gBAAN,CAAuB;AACnBC,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACQ,SAAKC,UAAL,GAAkB,IAAIH,aAAJ,CAAkB,CAAlB,CAAlB;AACH;;AACmB,MAAhBI,gBAAgB,GAAG;AACnB,WAAO,KAAKD,UAAL,CAAgBE,YAAhB,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,SAAS,GAAG;AACR,SAAKH,UAAL,CAAgBI,IAAhB,CAAqB,IAArB;AACA,SAAKJ,UAAL,CAAgBK,QAAhB;AACH;;AAjBkB;AAmBvB;;;AAAmBP,gBAAgB,CAACQ,IAAjB;AAAA,mBAA8GR,gBAA9G;AAAA;AACnB;;;AAAmBA,gBAAgB,CAACS,KAAjB,kBADoGb,EACpG;AAAA,SAAkHI,gBAAlH;AAAA,WAAkHA,gBAAlH;AAAA,cAAgJ;AAAhJ;;AACnB;AAAA,qDAFuHJ,EAEvH,mBAA4FI,gBAA5F,EAA0H,CAAC;AAC/GU,IAAAA,IAAI,EAAEb,UADyG;AAE/Gc,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFyG,GAAD,CAA1H;AAAA;;AAKA,SAASC,oBAAT,CAA8BC,CAA9B,EAAiCC,CAAjC,EAAoC;AAChC,SAAOD,CAAC,KAAKC,CAAb;AACH;;AACD,SAASC,0BAAT,CAAoCC,aAApC,EAAmDC,IAAnD,EAAyDZ,IAAzD,EAA+D;AAC3D,MAAIY,IAAI,KAAK,IAAT,IAAiBZ,IAAI,KAAK,IAA1B,IAAkCY,IAAI,CAACC,MAAL,KAAgBb,IAAI,CAACa,MAA3D,EAAmE;AAC/D,WAAO,KAAP;AACH,GAH0D,CAI3D;;;AACA,QAAMA,MAAM,GAAGD,IAAI,CAACC,MAApB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,MAApB,EAA4BC,CAAC,EAA7B,EAAiC;AAC7B,QAAI,CAACH,aAAa,CAACC,IAAI,CAACE,CAAD,CAAL,EAAUd,IAAI,CAACc,CAAD,CAAd,CAAlB,EAAsC;AAClC,aAAO,KAAP;AACH;AACJ;;AACD,SAAO,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,OAAT,CAAiBC,IAAjB,EAAuBL,aAAa,GAAGJ,oBAAvC,EAA6D;AACzD,MAAIU,QAAQ,GAAG,IAAf;AACA,MAAIC,UAAU,GAAG,IAAjB,CAFyD,CAGzD;;AACA,WAASC,QAAT,GAAoB;AAChB;AACA,QAAI,CAACT,0BAA0B,CAACC,aAAD,EAAgBM,QAAhB,EAA0BG,SAA1B,CAA/B,EAAqE;AACjE;AACA;AACAF,MAAAA,UAAU,GAAGF,IAAI,CAACK,KAAL,CAAW,IAAX,EAAiBD,SAAjB,CAAb;AACH,KANe,CAOhB;;;AACAH,IAAAA,QAAQ,GAAGG,SAAX;AACA,WAAOF,UAAP;AACH;;AACDC,EAAAA,QAAQ,CAACG,KAAT,GAAiB,YAAY;AACzB;AACAL,IAAAA,QAAQ,GAAG,IAAX;AACAC,IAAAA,UAAU,GAAG,IAAb;AACH,GAJD;;AAKA,SAAOC,QAAP;AACH;;AAED,MAAMI,YAAN,CAAmB;AACL,SAAHC,GAAG,CAACC,KAAD,EAAQ;AACd,SAAKC,MAAL,GAAcD,KAAd;AACH;;AACS,SAAHE,GAAG,GAAG;AACT,UAAMF,KAAK,GAAG,KAAKC,MAAnB;AACA,SAAKA,MAAL,GAAc,EAAd;AACA,WAAOD,KAAP;AACH;;AARc;;AAUnBF,YAAY,CAACG,MAAb,GAAsB,EAAtB;AACA,MAAME,mBAAmB,GAAG,IAAIpC,cAAJ,CAAmB,qBAAnB,EAA0C;AAClEc,EAAAA,UAAU,EAAE,MADsD;AAElEuB,EAAAA,OAAO,EAAE,MAAMN,YAAY,CAACI,GAAb;AAFmD,CAA1C,CAA5B,C,CAKA;;AACA,MAAMG,mBAAmB,GAAG,IAAItC,cAAJ,CAAmB,qBAAnB,CAA5B;AACA,MAAMuC,2BAA2B,GAAG,IAAIvC,cAAJ,CAAmB,6BAAnB,CAApC;AAEA;AACA;AACA;;AAEA,SAASoC,mBAAT,EAA8BL,YAA9B,EAA4C7B,gBAA5C,EAA8DqB,OAA9D,EAAuEgB,2BAAvE,EAAoGD,mBAApG", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\n\nclass NgxsBootstrapper {\n    constructor() {\n        /**\n         * Use `ReplaySubject`, thus we can get cached value even if the stream is completed\n         */\n        this.bootstrap$ = new ReplaySubject(1);\n    }\n    get appBootstrapped$() {\n        return this.bootstrap$.asObservable();\n    }\n    /**\n     * This event will be emitted after attaching `ComponentRef` of the root component\n     * to the tree of views, that's a signal that application has been fully rendered\n     */\n    bootstrap() {\n        this.bootstrap$.next(true);\n        this.bootstrap$.complete();\n    }\n}\n/** @nocollapse */ NgxsBootstrapper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsBootstrapper, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NgxsBootstrapper.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsBootstrapper, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsBootstrapper, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nfunction defaultEqualityCheck(a, b) {\n    return a === b;\n}\nfunction areArgumentsShallowlyEqual(equalityCheck, prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n        return false;\n    }\n    // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\n    const length = prev.length;\n    for (let i = 0; i < length; i++) {\n        if (!equalityCheck(prev[i], next[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Memoize a function on its last inputs only.\n * Originally from: https://github.com/reduxjs/reselect/blob/master/src/index.js\n *\n * @ignore\n */\nfunction memoize(func, equalityCheck = defaultEqualityCheck) {\n    let lastArgs = null;\n    let lastResult = null;\n    // we reference arguments instead of spreading them for performance reasons\n    function memoized() {\n        // eslint-disable-next-line prefer-rest-params\n        if (!areArgumentsShallowlyEqual(equalityCheck, lastArgs, arguments)) {\n            // apply arguments instead of spreading for performance.\n            // eslint-disable-next-line prefer-rest-params, prefer-spread\n            lastResult = func.apply(null, arguments);\n        }\n        // eslint-disable-next-line prefer-rest-params\n        lastArgs = arguments;\n        return lastResult;\n    }\n    memoized.reset = function () {\n        // The hidden (for now) ability to reset the memoization\n        lastArgs = null;\n        lastResult = null;\n    };\n    return memoized;\n}\n\nclass InitialState {\n    static set(state) {\n        this._value = state;\n    }\n    static pop() {\n        const state = this._value;\n        this._value = {};\n        return state;\n    }\n}\nInitialState._value = {};\nconst INITIAL_STATE_TOKEN = new InjectionToken('INITIAL_STATE_TOKEN', {\n    providedIn: 'root',\n    factory: () => InitialState.pop()\n});\n\n// These tokens are internal and can change at any point.\nconst ɵNGXS_STATE_FACTORY = new InjectionToken('ɵNGXS_STATE_FACTORY');\nconst ɵNGXS_STATE_CONTEXT_FACTORY = new InjectionToken('ɵNGXS_STATE_CONTEXT_FACTORY');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_STATE_TOKEN, InitialState, NgxsBootstrapper, memoize, ɵNGXS_STATE_CONTEXT_FACTORY, ɵNGXS_STATE_FACTORY };\n"]}, "metadata": {}, "sourceType": "module"}