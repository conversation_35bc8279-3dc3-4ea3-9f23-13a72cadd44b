{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { <PERSON><PERSON><PERSON>, PLA<PERSON>ORM_ID, Injectable, Inject, InjectionToken, inject, INJECTO<PERSON>, ɵglobal, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Optional, SkipSelf, NgModule, APP_BOOTSTRAP_LISTENER } from '@angular/core';\nimport * as i5 from '@ngxs/store/internals';\nimport { memoize, INITIAL_STATE_TOKEN, NgxsBootstrapper, ɵNGXS_STATE_CONTEXT_FACTORY, ɵNGXS_STATE_FACTORY } from '@ngxs/store/internals';\nimport { isPlatformServer } from '@angular/common';\nimport { Observable, Subject, BehaviorSubject, of, forkJoin, throwError, EMPTY, from, isObservable } from 'rxjs';\nimport { filter, map, share, shareReplay, take, exhaustMap, mergeMap, defaultIfEmpty, catchError, takeUntil, distinctUntilChanged, tap, startWith, pairwise } from 'rxjs/operators';\nimport { isStateOperator } from '@ngxs/store/operators';\n/**\n * Returns the type from an action instance/class.\n * @ignore\n */\n\nfunction getActionTypeFromInstance(action) {\n  if (action.constructor && action.constructor.type) {\n    return action.constructor.type;\n  } else {\n    return action.type;\n  }\n}\n/**\n * Matches a action\n * @ignore\n */\n\n\nfunction actionMatcher(action1) {\n  const type1 = getActionTypeFromInstance(action1);\n  return function (action2) {\n    return type1 === getActionTypeFromInstance(action2);\n  };\n}\n/**\n * Set a deeply nested value. Example:\n *\n *   setValue({ foo: { bar: { eat: false } } },\n *      'foo.bar.eat', true) //=> { foo: { bar: { eat: true } } }\n *\n * While it traverses it also creates new objects from top down.\n *\n * @ignore\n */\n\n\nconst setValue = (obj, prop, val) => {\n  obj = Object.assign({}, obj);\n  const split = prop.split('.');\n  const lastIndex = split.length - 1;\n  split.reduce((acc, part, index) => {\n    if (index === lastIndex) {\n      acc[part] = val;\n    } else {\n      acc[part] = Array.isArray(acc[part]) ? acc[part].slice() : Object.assign({}, acc[part]);\n    }\n\n    return acc && acc[part];\n  }, obj);\n  return obj;\n};\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * @ignore\n */\n\n\nconst getValue = (obj, prop) => prop.split('.').reduce((acc, part) => acc && acc[part], obj);\n/**\n * Simple object check.\n *\n *    isObject({a:1}) //=> true\n *    isObject(1) //=> false\n *\n * @ignore\n */\n\n\nconst isObject$1 = item => {\n  return item && typeof item === 'object' && !Array.isArray(item);\n};\n/**\n * Deep merge two objects.\n *\n *    mergeDeep({a:1, b:{x: 1, y:2}}, {b:{x: 3}, c:4}) //=> {a:1, b:{x:3, y:2}, c:4}\n *\n * @param base base object onto which `sources` will be applied\n */\n\n\nconst mergeDeep = (base, ...sources) => {\n  if (!sources.length) return base;\n  const source = sources.shift();\n\n  if (isObject$1(base) && isObject$1(source)) {\n    for (const key in source) {\n      if (isObject$1(source[key])) {\n        if (!base[key]) Object.assign(base, {\n          [key]: {}\n        });\n        mergeDeep(base[key], source[key]);\n      } else {\n        Object.assign(base, {\n          [key]: source[key]\n        });\n      }\n    }\n  }\n\n  return mergeDeep(base, ...sources);\n};\n\nfunction throwStateNameError(name) {\n  throw new Error(`${name} is not a valid state name. It needs to be a valid object property name.`);\n}\n\nfunction throwStateNamePropertyError() {\n  throw new Error(`States must register a 'name' property.`);\n}\n\nfunction throwStateUniqueError(current, newName, oldName) {\n  throw new Error(`State name '${current}' from ${newName} already exists in ${oldName}.`);\n}\n\nfunction throwStateDecoratorError(name) {\n  throw new Error(`States must be decorated with @State() decorator, but \"${name}\" isn't.`);\n}\n\nfunction throwActionDecoratorError() {\n  throw new Error('@Action() decorator cannot be used with static methods.');\n}\n\nfunction throwSelectorDecoratorError() {\n  throw new Error('Selectors only work on methods.');\n}\n\nfunction getZoneWarningMessage() {\n  return 'Your application was bootstrapped with nooped zone and your execution strategy requires an actual NgZone!\\n' + 'Please set the value of the executionStrategy property to NoopNgxsExecutionStrategy.\\n' + 'NgxsModule.forRoot(states, { executionStrategy: NoopNgxsExecutionStrategy })';\n}\n\nfunction getUndecoratedStateInIvyWarningMessage(name) {\n  return `'${name}' class should be decorated with @Injectable() right after the @State() decorator`;\n}\n\nfunction throwSelectFactoryNotConnectedError() {\n  throw new Error('You have forgotten to import the NGXS module!');\n}\n\nfunction throwPatchingArrayError() {\n  throw new Error('Patching arrays is not supported.');\n}\n\nfunction throwPatchingPrimitiveError() {\n  throw new Error('Patching primitives is not supported.');\n}\n\nclass DispatchOutsideZoneNgxsExecutionStrategy {\n  constructor(_ngZone, _platformId) {\n    this._ngZone = _ngZone;\n    this._platformId = _platformId; // Caretaker note: we have still left the `typeof` condition in order to avoid\n    // creating a breaking change for projects that still use the View Engine.\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      verifyZoneIsNotNooped(_ngZone);\n    }\n  }\n\n  enter(func) {\n    if (isPlatformServer(this._platformId)) {\n      return this.runInsideAngular(func);\n    }\n\n    return this.runOutsideAngular(func);\n  }\n\n  leave(func) {\n    return this.runInsideAngular(func);\n  }\n\n  runInsideAngular(func) {\n    if (NgZone.isInAngularZone()) {\n      return func();\n    }\n\n    return this._ngZone.run(func);\n  }\n\n  runOutsideAngular(func) {\n    if (NgZone.isInAngularZone()) {\n      return this._ngZone.runOutsideAngular(func);\n    }\n\n    return func();\n  }\n\n}\n/** @nocollapse */\n\n\nDispatchOutsideZoneNgxsExecutionStrategy.ɵfac = function DispatchOutsideZoneNgxsExecutionStrategy_Factory(t) {\n  return new (t || DispatchOutsideZoneNgxsExecutionStrategy)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(PLATFORM_ID));\n};\n/** @nocollapse */\n\n\nDispatchOutsideZoneNgxsExecutionStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DispatchOutsideZoneNgxsExecutionStrategy,\n  factory: DispatchOutsideZoneNgxsExecutionStrategy.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DispatchOutsideZoneNgxsExecutionStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})(); // Caretaker note: this should exist as a separate function and not a class method,\n// since class methods are not tree-shakable.\n\n\nfunction verifyZoneIsNotNooped(ngZone) {\n  // `NoopNgZone` is not exposed publicly as it doesn't expect\n  // to be used outside of the core Angular code, thus we just have\n  // to check if the zone doesn't extend or instanceof `NgZone`.\n  if (ngZone instanceof NgZone) {\n    return;\n  }\n\n  console.warn(getZoneWarningMessage());\n}\n\nconst ROOT_OPTIONS = new InjectionToken('ROOT_OPTIONS');\nconst ROOT_STATE_TOKEN = new InjectionToken('ROOT_STATE_TOKEN');\nconst FEATURE_STATE_TOKEN = new InjectionToken('FEATURE_STATE_TOKEN');\nconst NGXS_PLUGINS = new InjectionToken('NGXS_PLUGINS');\nconst META_KEY = 'NGXS_META';\nconst META_OPTIONS_KEY = 'NGXS_OPTIONS_META';\nconst SELECTOR_META_KEY = 'NGXS_SELECTOR_META';\n/**\n * The NGXS config settings.\n */\n\nclass NgxsConfig {\n  constructor() {\n    /**\n     * Defining the default state before module initialization\n     * This is convenient if we need to create a define our own set of states.\n     * @deprecated will be removed after v4\n     * (default: {})\n     */\n    this.defaultsState = {};\n    /**\n     * Defining shared selector options\n     */\n\n    this.selectorOptions = {\n      injectContainerState: true,\n      suppressErrors: true // TODO: default is true in v3, will change in v4\n\n    };\n    this.compatibility = {\n      strictContentSecurityPolicy: false\n    };\n    this.executionStrategy = DispatchOutsideZoneNgxsExecutionStrategy;\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsConfig.ɵfac = function NgxsConfig_Factory(t) {\n  return new (t || NgxsConfig)();\n};\n/** @nocollapse */\n\n\nNgxsConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxsConfig,\n  factory: function NgxsConfig_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = (options => mergeDeep(new NgxsConfig(), options))(i0.ɵɵinject(ROOT_OPTIONS));\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: options => mergeDeep(new NgxsConfig(), options),\n      deps: [ROOT_OPTIONS]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n/**\n * Represents a basic change from a previous to a new value for a single state instance.\n * Passed as a value in a NgxsSimpleChanges object to the ngxsOnChanges hook.\n */\n\n\nclass NgxsSimpleChange {\n  constructor(previousValue, currentValue, firstChange) {\n    this.previousValue = previousValue;\n    this.currentValue = currentValue;\n    this.firstChange = firstChange;\n  }\n\n}\n\nclass NoopNgxsExecutionStrategy {\n  enter(func) {\n    return func();\n  }\n\n  leave(func) {\n    return func();\n  }\n\n}\n/** @nocollapse */\n\n\nNoopNgxsExecutionStrategy.ɵfac = function NoopNgxsExecutionStrategy_Factory(t) {\n  return new (t || NoopNgxsExecutionStrategy)();\n};\n/** @nocollapse */\n\n\nNoopNgxsExecutionStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NoopNgxsExecutionStrategy,\n  factory: NoopNgxsExecutionStrategy.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopNgxsExecutionStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * The strategy that might be provided by users through `options.executionStrategy`.\n */\n\n\nconst USER_PROVIDED_NGXS_EXECUTION_STRATEGY = new InjectionToken('USER_PROVIDED_NGXS_EXECUTION_STRATEGY');\n/*\n * Internal execution strategy injection token\n */\n\nconst NGXS_EXECUTION_STRATEGY = new InjectionToken('NGXS_EXECUTION_STRATEGY', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(INJECTOR);\n    const executionStrategy = injector.get(USER_PROVIDED_NGXS_EXECUTION_STRATEGY);\n    return executionStrategy ? injector.get(executionStrategy) : injector.get(typeof ɵglobal.Zone !== 'undefined' ? DispatchOutsideZoneNgxsExecutionStrategy : NoopNgxsExecutionStrategy);\n  }\n});\n/**\n * Ensures metadata is attached to the class and returns it.\n *\n * @ignore\n */\n\nfunction ensureStoreMetadata$1(target) {\n  if (!target.hasOwnProperty(META_KEY)) {\n    const defaultMetadata = {\n      name: null,\n      actions: {},\n      defaults: {},\n      path: null,\n\n      makeRootSelector(context) {\n        return context.getStateGetter(defaultMetadata.name);\n      },\n\n      children: []\n    };\n    Object.defineProperty(target, META_KEY, {\n      value: defaultMetadata\n    });\n  }\n\n  return getStoreMetadata$1(target);\n}\n/**\n * Get the metadata attached to the state class if it exists.\n *\n * @ignore\n */\n\n\nfunction getStoreMetadata$1(target) {\n  return target[META_KEY];\n}\n/**\n * Ensures metadata is attached to the selector and returns it.\n *\n * @ignore\n */\n\n\nfunction ensureSelectorMetadata$1(target) {\n  if (!target.hasOwnProperty(SELECTOR_META_KEY)) {\n    const defaultMetadata = {\n      makeRootSelector: null,\n      originalFn: null,\n      containerClass: null,\n      selectorName: null,\n      getSelectorOptions: () => ({})\n    };\n    Object.defineProperty(target, SELECTOR_META_KEY, {\n      value: defaultMetadata\n    });\n  }\n\n  return getSelectorMetadata$1(target);\n}\n/**\n * Get the metadata attached to the selector if it exists.\n *\n * @ignore\n */\n\n\nfunction getSelectorMetadata$1(target) {\n  return target[SELECTOR_META_KEY];\n}\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * Note: This is not as fast as the `fastPropGetter` but is strict Content Security Policy compliant.\n * See perf hit: https://jsperf.com/fast-value-getter-given-path/1\n *\n * @ignore\n */\n\n\nfunction compliantPropGetter(paths) {\n  const copyOfPaths = paths.slice();\n  return obj => copyOfPaths.reduce((acc, part) => acc && acc[part], obj);\n}\n/**\n * The generated function is faster than:\n * - pluck (Observable operator)\n * - memoize\n *\n * @ignore\n */\n\n\nfunction fastPropGetter(paths) {\n  const segments = paths;\n  let seg = 'store.' + segments[0];\n  let i = 0;\n  const l = segments.length;\n  let expr = seg;\n\n  while (++i < l) {\n    expr = expr + ' && ' + (seg = seg + '.' + segments[i]);\n  }\n\n  const fn = new Function('store', 'return ' + expr + ';');\n  return fn;\n}\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * @ignore\n */\n\n\nfunction propGetter(paths, config) {\n  if (config && config.compatibility && config.compatibility.strictContentSecurityPolicy) {\n    return compliantPropGetter(paths);\n  } else {\n    return fastPropGetter(paths);\n  }\n}\n/**\n * Given an array of states, it will return a object graph. Example:\n *    const states = [\n *      Cart,\n *      CartSaved,\n *      CartSavedItems\n *    ]\n *\n * would return:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * @ignore\n */\n\n\nfunction buildGraph(stateClasses) {\n  const findName = stateClass => {\n    const meta = stateClasses.find(g => g === stateClass); // Caretaker note: we have still left the `typeof` condition in order to avoid\n    // creating a breaking change for projects that still use the View Engine.\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !meta) {\n      throw new Error(`Child state not found: ${stateClass}. \\r\\nYou may have forgotten to add states to module`);\n    }\n\n    return meta[META_KEY].name;\n  };\n\n  return stateClasses.reduce((result, stateClass) => {\n    const {\n      name,\n      children\n    } = stateClass[META_KEY];\n    result[name] = (children || []).map(findName);\n    return result;\n  }, {});\n}\n/**\n * Given a states array, returns object graph\n * returning the name and state metadata. Example:\n *\n *  const graph = {\n *    cart: { metadata }\n *  };\n *\n * @ignore\n */\n\n\nfunction nameToState(states) {\n  return states.reduce((result, stateClass) => {\n    const meta = stateClass[META_KEY];\n    result[meta.name] = stateClass;\n    return result;\n  }, {});\n}\n/**\n * Given a object relationship graph will return the full path\n * for the child items. Example:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * would return:\n *\n *  const r = {\n *    cart: 'cart',\n *    saved: 'cart.saved',\n *    items: 'cart.saved.items'\n *  };\n *\n * @ignore\n */\n\n\nfunction findFullParentPath(obj, newObj = {}) {\n  const visit = (child, keyToFind) => {\n    for (const key in child) {\n      if (child.hasOwnProperty(key) && child[key].indexOf(keyToFind) >= 0) {\n        const parent = visit(child, key);\n        return parent !== null ? `${parent}.${key}` : key;\n      }\n    }\n\n    return null;\n  };\n\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      const parent = visit(obj, key);\n      newObj[key] = parent ? `${parent}.${key}` : key;\n    }\n  }\n\n  return newObj;\n}\n/**\n * Given a object graph, it will return the items topologically sorted Example:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * would return:\n *\n *  const results = [\n *    'items',\n *    'saved',\n *    'cart'\n *  ];\n *\n * @ignore\n */\n\n\nfunction topologicalSort(graph) {\n  const sorted = [];\n  const visited = {};\n\n  const visit = (name, ancestors = []) => {\n    if (!Array.isArray(ancestors)) {\n      ancestors = [];\n    }\n\n    ancestors.push(name);\n    visited[name] = true;\n    graph[name].forEach(dep => {\n      // Caretaker note: we have still left the `typeof` condition in order to avoid\n      // creating a breaking change for projects that still use the View Engine.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && ancestors.indexOf(dep) >= 0) {\n        throw new Error(`Circular dependency '${dep}' is required by '${name}': ${ancestors.join(' -> ')}`);\n      }\n\n      if (visited[dep]) {\n        return;\n      }\n\n      visit(dep, ancestors.slice(0));\n    });\n\n    if (sorted.indexOf(name) < 0) {\n      sorted.push(name);\n    }\n  };\n\n  Object.keys(graph).forEach(k => visit(k));\n  return sorted.reverse();\n}\n/**\n * Returns if the parameter is a object or not.\n *\n * @ignore\n */\n\n\nfunction isObject(obj) {\n  return typeof obj === 'object' && obj !== null || typeof obj === 'function';\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will grab actions that have just been dispatched as well as actions that have completed\n */\n\n\nfunction ofAction(...allowedTypes) {\n  return ofActionOperator(allowedTypes);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been dispatched\n */\n\n\nfunction ofActionDispatched(...allowedTypes) {\n  return ofActionOperator(allowedTypes, [\"DISPATCHED\"\n  /* Dispatched */\n  ]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been successfully completed\n */\n\n\nfunction ofActionSuccessful(...allowedTypes) {\n  return ofActionOperator(allowedTypes, [\"SUCCESSFUL\"\n  /* Successful */\n  ]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been canceled\n */\n\n\nfunction ofActionCanceled(...allowedTypes) {\n  return ofActionOperator(allowedTypes, [\"CANCELED\"\n  /* Canceled */\n  ]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been completed\n */\n\n\nfunction ofActionCompleted(...allowedTypes) {\n  const allowedStatuses = [\"SUCCESSFUL\"\n  /* Successful */\n  , \"CANCELED\"\n  /* Canceled */\n  , \"ERRORED\"\n  /* Errored */\n  ];\n  return ofActionOperator(allowedTypes, allowedStatuses, mapActionResult);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just thrown an error\n */\n\n\nfunction ofActionErrored(...allowedTypes) {\n  return ofActionOperator(allowedTypes, [\"ERRORED\"\n  /* Errored */\n  ]);\n}\n\nfunction ofActionOperator(allowedTypes, statuses, // This actually could've been `OperatorFunction<ActionContext, ActionCompletion | any>`,\n// since it maps either to `ctx.action` OR to `ActionCompletion`. But `ActionCompleteion | any`\n// defaults to `any`, thus there is no sense from union type.\nmapOperator = mapAction) {\n  const allowedMap = createAllowedActionTypesMap(allowedTypes);\n  const allowedStatusMap = statuses && createAllowedStatusesMap(statuses);\n  return function (o) {\n    return o.pipe(filterStatus(allowedMap, allowedStatusMap), mapOperator());\n  };\n}\n\nfunction filterStatus(allowedTypes, allowedStatuses) {\n  return filter(ctx => {\n    const actionType = getActionTypeFromInstance(ctx.action);\n    const typeMatch = allowedTypes[actionType];\n    const statusMatch = allowedStatuses ? allowedStatuses[ctx.status] : true;\n    return typeMatch && statusMatch;\n  });\n}\n\nfunction mapActionResult() {\n  return map(({\n    action,\n    status,\n    error\n  }) => {\n    return {\n      action,\n      result: {\n        successful: \"SUCCESSFUL\"\n        /* Successful */\n        === status,\n        canceled: \"CANCELED\"\n        /* Canceled */\n        === status,\n        error\n      }\n    };\n  });\n}\n\nfunction mapAction() {\n  return map(ctx => ctx.action);\n}\n\nfunction createAllowedActionTypesMap(types) {\n  return types.reduce((filterMap, klass) => {\n    filterMap[getActionTypeFromInstance(klass)] = true;\n    return filterMap;\n  }, {});\n}\n\nfunction createAllowedStatusesMap(statuses) {\n  return statuses.reduce((filterMap, status) => {\n    filterMap[status] = true;\n    return filterMap;\n  }, {});\n}\n/**\n * Returns operator that will run\n * `subscribe` outside of the ngxs execution context\n */\n\n\nfunction leaveNgxs(ngxsExecutionStrategy) {\n  return source => {\n    return new Observable(sink => {\n      return source.subscribe({\n        next(value) {\n          ngxsExecutionStrategy.leave(() => sink.next(value));\n        },\n\n        error(error) {\n          ngxsExecutionStrategy.leave(() => sink.error(error));\n        },\n\n        complete() {\n          ngxsExecutionStrategy.leave(() => sink.complete());\n        }\n\n      });\n    });\n  };\n}\n\nclass InternalNgxsExecutionStrategy {\n  constructor(_executionStrategy) {\n    this._executionStrategy = _executionStrategy;\n  }\n\n  enter(func) {\n    return this._executionStrategy.enter(func);\n  }\n\n  leave(func) {\n    return this._executionStrategy.leave(func);\n  }\n\n}\n/** @nocollapse */\n\n\nInternalNgxsExecutionStrategy.ɵfac = function InternalNgxsExecutionStrategy_Factory(t) {\n  return new (t || InternalNgxsExecutionStrategy)(i0.ɵɵinject(NGXS_EXECUTION_STRATEGY));\n};\n/** @nocollapse */\n\n\nInternalNgxsExecutionStrategy.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalNgxsExecutionStrategy,\n  factory: InternalNgxsExecutionStrategy.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalNgxsExecutionStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGXS_EXECUTION_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * This wraps the provided function, and will enforce the following:\n * - The calls will execute in the order that they are made\n * - A call will only be initiated when the previous call has completed\n * - If there is a call currently executing then the new call will be added\n *   to the queue and the function will return immediately\n *\n * NOTE: The following assumptions about the operation must hold true:\n * - The operation is synchronous in nature\n * - If any asynchronous side effects of the call exist, it should not\n *   have any bearing on the correctness of the next call in the queue\n * - The operation has a void return\n * - The caller should not assume that the call has completed upon\n *   return of the function\n * - The caller can assume that all the queued calls will complete\n *   within the current microtask\n * - The only way that a call will encounter another call in the queue\n *   would be if the call at the front of the queue initiated this call\n *   as part of its synchronous execution\n */\n\n\nfunction orderedQueueOperation(operation) {\n  const callsQueue = [];\n  let busyPushingNext = false;\n  return function callOperation(...args) {\n    if (busyPushingNext) {\n      callsQueue.unshift(args);\n      return;\n    }\n\n    busyPushingNext = true;\n    operation(...args);\n\n    while (callsQueue.length > 0) {\n      const nextCallArgs = callsQueue.pop();\n      nextCallArgs && operation(...nextCallArgs);\n    }\n\n    busyPushingNext = false;\n  };\n}\n/**\n * Custom Subject that ensures that subscribers are notified of values in the order that they arrived.\n * A standard Subject does not have this guarantee.\n * For example, given the following code:\n * ```typescript\n *   const subject = new Subject<string>();\n     subject.subscribe(value => {\n       if (value === 'start') subject.next('end');\n     });\n     subject.subscribe(value => { });\n     subject.next('start');\n * ```\n * When `subject` is a standard `Subject<T>` the second subscriber would recieve `end` and then `start`.\n * When `subject` is a `OrderedSubject<T>` the second subscriber would recieve `start` and then `end`.\n */\n\n\nclass OrderedSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._orderedNext = orderedQueueOperation(value => super.next(value));\n  }\n\n  next(value) {\n    this._orderedNext(value);\n  }\n\n}\n/**\n * Custom BehaviorSubject that ensures that subscribers are notified of values in the order that they arrived.\n * A standard BehaviorSubject does not have this guarantee.\n * For example, given the following code:\n * ```typescript\n *   const subject = new BehaviorSubject<string>();\n     subject.subscribe(value => {\n       if (value === 'start') subject.next('end');\n     });\n     subject.subscribe(value => { });\n     subject.next('start');\n * ```\n * When `subject` is a standard `BehaviorSubject<T>` the second subscriber would recieve `end` and then `start`.\n * When `subject` is a `OrderedBehaviorSubject<T>` the second subscriber would recieve `start` and then `end`.\n */\n\n\nclass OrderedBehaviorSubject extends BehaviorSubject {\n  constructor(value) {\n    super(value);\n    this._orderedNext = orderedQueueOperation(value => super.next(value));\n    this._currentValue = value;\n  }\n\n  getValue() {\n    return this._currentValue;\n  }\n\n  next(value) {\n    this._currentValue = value;\n\n    this._orderedNext(value);\n  }\n\n}\n/**\n * Internal Action stream that is emitted anytime an action is dispatched.\n */\n\n\nclass InternalActions extends OrderedSubject {\n  ngOnDestroy() {\n    this.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nInternalActions.ɵfac = /* @__PURE__ */function () {\n  let ɵInternalActions_BaseFactory;\n  return function InternalActions_Factory(t) {\n    return (ɵInternalActions_BaseFactory || (ɵInternalActions_BaseFactory = i0.ɵɵgetInheritedFactory(InternalActions)))(t || InternalActions);\n  };\n}();\n/** @nocollapse */\n\n\nInternalActions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalActions,\n  factory: InternalActions.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalActions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * Action stream that is emitted anytime an action is dispatched.\n *\n * You can listen to this in services to react without stores.\n */\n\n\nclass Actions extends Observable {\n  constructor(internalActions$, internalExecutionStrategy) {\n    const sharedInternalActions$ = internalActions$.pipe(leaveNgxs(internalExecutionStrategy), // The `InternalActions` subject emits outside of the Angular zone.\n    // We have to re-enter the Angular zone for any incoming consumer.\n    // The `share()` operator reduces the number of change detections.\n    // This would call leave only once for any stream emission across all active subscribers.\n    share());\n    super(observer => {\n      const childSubscription = sharedInternalActions$.subscribe({\n        next: ctx => observer.next(ctx),\n        error: error => observer.error(error),\n        complete: () => observer.complete()\n      });\n      observer.add(childSubscription);\n    });\n  }\n\n}\n/** @nocollapse */\n\n\nActions.ɵfac = function Actions_Factory(t) {\n  return new (t || Actions)(i0.ɵɵinject(InternalActions), i0.ɵɵinject(InternalNgxsExecutionStrategy));\n};\n/** @nocollapse */\n\n\nActions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Actions,\n  factory: Actions.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Actions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InternalActions\n    }, {\n      type: InternalNgxsExecutionStrategy\n    }];\n  }, null);\n})();\n/**\n * Composes a array of functions from left to right. Example:\n *\n *      compose([fn, final])(state, action);\n *\n * then the funcs have a signature like:\n *\n *      function fn (state, action, next) {\n *          console.log('here', state, action, next);\n *          return next(state, action);\n *      }\n *\n *      function final (state, action) {\n *          console.log('here', state, action);\n *          return state;\n *      }\n *\n * the last function should not call `next`.\n *\n * @ignore\n */\n\n\nconst compose = funcs => (...args) => {\n  const curr = funcs.shift();\n  return curr(...args, (...nextArgs) => compose(funcs)(...nextArgs));\n};\n/**\n * This operator is used for piping the observable result\n * from the `dispatch()`. It has a \"smart\" error handling\n * strategy that allows us to decide whether we propagate\n * errors to Angular's `ErrorHandler` or enable users to\n * handle them manually. We consider following cases:\n * 1) `store.dispatch()` (no subscribe) -> call `handleError()`\n * 2) `store.dispatch().subscribe()` (no error callback) -> call `handleError()`\n * 3) `store.dispatch().subscribe({ error: ... })` -> don't call `handleError()`\n * 4) `toPromise()` without `catch` -> do `handleError()`\n * 5) `toPromise()` with `catch` -> don't `handleError()`\n */\n\n\nfunction ngxsErrorHandler(internalErrorReporter, ngxsExecutionStrategy) {\n  return source => {\n    let subscribed = false;\n    source.subscribe({\n      error: error => {\n        // Do not trigger change detection for a microtask. This depends on the execution\n        // strategy being used, but the default `DispatchOutsideZoneNgxsExecutionStrategy`\n        // leaves the Angular zone.\n        ngxsExecutionStrategy.enter(() => Promise.resolve().then(() => {\n          if (!subscribed) {\n            ngxsExecutionStrategy.leave(() => internalErrorReporter.reportErrorSafely(error));\n          }\n        }));\n      }\n    });\n    return new Observable(subscriber => {\n      subscribed = true;\n      return source.pipe(leaveNgxs(ngxsExecutionStrategy)).subscribe(subscriber);\n    });\n  };\n}\n\nclass InternalErrorReporter {\n  constructor(_injector) {\n    this._injector = _injector;\n    /** Will be set lazily to be backward compatible. */\n\n    this._errorHandler = null;\n  }\n\n  reportErrorSafely(error) {\n    if (this._errorHandler === null) {\n      this._errorHandler = this._injector.get(ErrorHandler);\n    } // The `try-catch` is used to avoid handling the error twice. Suppose we call\n    // `handleError` which re-throws the error internally. The re-thrown error will\n    // be caught by zone.js which will then get to the `zone.onError.emit()` and the\n    // `onError` subscriber will call `handleError` again.\n\n\n    try {\n      this._errorHandler.handleError(error);\n    } catch (_a) {}\n  }\n\n}\n/** @nocollapse */\n\n\nInternalErrorReporter.ɵfac = function InternalErrorReporter_Factory(t) {\n  return new (t || InternalErrorReporter)(i0.ɵɵinject(i0.Injector));\n};\n/** @nocollapse */\n\n\nInternalErrorReporter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalErrorReporter,\n  factory: InternalErrorReporter.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalErrorReporter, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.Injector\n    }];\n  }, null);\n})();\n/**\n * BehaviorSubject of the entire state.\n * @ignore\n */\n\n\nclass StateStream extends OrderedBehaviorSubject {\n  constructor() {\n    super({});\n  }\n\n  ngOnDestroy() {\n    // The `StateStream` should never emit values once the root view is removed, e.g. when the `NgModuleRef.destroy()` is called.\n    // This will eliminate memory leaks in server-side rendered apps where the `StateStream` is created per each HTTP request, users\n    // might forget to unsubscribe from `store.select` or `store.subscribe`, thus this will lead to huge memory leaks in SSR apps.\n    this.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nStateStream.ɵfac = function StateStream_Factory(t) {\n  return new (t || StateStream)();\n};\n/** @nocollapse */\n\n\nStateStream.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: StateStream,\n  factory: StateStream.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateStream, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\nclass PluginManager {\n  constructor(_parentManager, _pluginHandlers) {\n    this._parentManager = _parentManager;\n    this._pluginHandlers = _pluginHandlers;\n    this.plugins = [];\n    this.registerHandlers();\n  }\n\n  get rootPlugins() {\n    return this._parentManager && this._parentManager.plugins || this.plugins;\n  }\n\n  registerHandlers() {\n    const pluginHandlers = this.getPluginHandlers();\n    this.rootPlugins.push(...pluginHandlers);\n  }\n\n  getPluginHandlers() {\n    const handlers = this._pluginHandlers || [];\n    return handlers.map(plugin => plugin.handle ? plugin.handle.bind(plugin) : plugin);\n  }\n\n}\n/** @nocollapse */\n\n\nPluginManager.ɵfac = function PluginManager_Factory(t) {\n  return new (t || PluginManager)(i0.ɵɵinject(PluginManager, 12), i0.ɵɵinject(NGXS_PLUGINS, 8));\n};\n/** @nocollapse */\n\n\nPluginManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: PluginManager,\n  factory: PluginManager.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PluginManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: PluginManager,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGXS_PLUGINS]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * Internal Action result stream that is emitted when an action is completed.\n * This is used as a method of returning the action result to the dispatcher\n * for the observable returned by the dispatch(...) call.\n * The dispatcher then asynchronously pushes the result from this stream onto the main action stream as a result.\n */\n\n\nclass InternalDispatchedActionResults extends Subject {}\n/** @nocollapse */\n\n\nInternalDispatchedActionResults.ɵfac = /* @__PURE__ */function () {\n  let ɵInternalDispatchedActionResults_BaseFactory;\n  return function InternalDispatchedActionResults_Factory(t) {\n    return (ɵInternalDispatchedActionResults_BaseFactory || (ɵInternalDispatchedActionResults_BaseFactory = i0.ɵɵgetInheritedFactory(InternalDispatchedActionResults)))(t || InternalDispatchedActionResults);\n  };\n}();\n/** @nocollapse */\n\n\nInternalDispatchedActionResults.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalDispatchedActionResults,\n  factory: InternalDispatchedActionResults.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalDispatchedActionResults, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass InternalDispatcher {\n  constructor(_actions, _actionResults, _pluginManager, _stateStream, _ngxsExecutionStrategy, _internalErrorReporter) {\n    this._actions = _actions;\n    this._actionResults = _actionResults;\n    this._pluginManager = _pluginManager;\n    this._stateStream = _stateStream;\n    this._ngxsExecutionStrategy = _ngxsExecutionStrategy;\n    this._internalErrorReporter = _internalErrorReporter;\n  }\n  /**\n   * Dispatches event(s).\n   */\n\n\n  dispatch(actionOrActions) {\n    const result = this._ngxsExecutionStrategy.enter(() => this.dispatchByEvents(actionOrActions));\n\n    return result.pipe(ngxsErrorHandler(this._internalErrorReporter, this._ngxsExecutionStrategy));\n  }\n\n  dispatchByEvents(actionOrActions) {\n    if (Array.isArray(actionOrActions)) {\n      if (actionOrActions.length === 0) return of(this._stateStream.getValue());\n      return forkJoin(actionOrActions.map(action => this.dispatchSingle(action)));\n    } else {\n      return this.dispatchSingle(actionOrActions);\n    }\n  }\n\n  dispatchSingle(action) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const type = getActionTypeFromInstance(action);\n\n      if (!type) {\n        const error = new Error(`This action doesn't have a type property: ${action.constructor.name}`);\n        return throwError(error);\n      }\n    }\n\n    const prevState = this._stateStream.getValue();\n\n    const plugins = this._pluginManager.plugins;\n    return compose([...plugins, (nextState, nextAction) => {\n      if (nextState !== prevState) {\n        this._stateStream.next(nextState);\n      }\n\n      const actionResult$ = this.getActionResultStream(nextAction);\n      actionResult$.subscribe(ctx => this._actions.next(ctx));\n\n      this._actions.next({\n        action: nextAction,\n        status: \"DISPATCHED\"\n        /* Dispatched */\n\n      });\n\n      return this.createDispatchObservable(actionResult$);\n    }])(prevState, action).pipe(shareReplay());\n  }\n\n  getActionResultStream(action) {\n    return this._actionResults.pipe(filter(ctx => ctx.action === action && ctx.status !== \"DISPATCHED\"\n    /* Dispatched */\n    ), take(1), shareReplay());\n  }\n\n  createDispatchObservable(actionResult$) {\n    return actionResult$.pipe(exhaustMap(ctx => {\n      switch (ctx.status) {\n        case \"SUCCESSFUL\"\n        /* Successful */\n        :\n          return of(this._stateStream.getValue());\n\n        case \"ERRORED\"\n        /* Errored */\n        :\n          return throwError(ctx.error);\n\n        default:\n          return EMPTY;\n      }\n    })).pipe(shareReplay());\n  }\n\n}\n/** @nocollapse */\n\n\nInternalDispatcher.ɵfac = function InternalDispatcher_Factory(t) {\n  return new (t || InternalDispatcher)(i0.ɵɵinject(InternalActions), i0.ɵɵinject(InternalDispatchedActionResults), i0.ɵɵinject(PluginManager), i0.ɵɵinject(StateStream), i0.ɵɵinject(InternalNgxsExecutionStrategy), i0.ɵɵinject(InternalErrorReporter));\n};\n/** @nocollapse */\n\n\nInternalDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalDispatcher,\n  factory: InternalDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InternalActions\n    }, {\n      type: InternalDispatchedActionResults\n    }, {\n      type: PluginManager\n    }, {\n      type: StateStream\n    }, {\n      type: InternalNgxsExecutionStrategy\n    }, {\n      type: InternalErrorReporter\n    }];\n  }, null);\n})();\n/**\n * Object freeze code\n * https://github.com/jsdf/deep-freeze\n */\n\n\nconst deepFreeze = o => {\n  Object.freeze(o);\n  const oIsFunction = typeof o === 'function';\n  const hasOwnProp = Object.prototype.hasOwnProperty;\n  Object.getOwnPropertyNames(o).forEach(function (prop) {\n    if (hasOwnProp.call(o, prop) && (oIsFunction ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments' : true) && o[prop] !== null && (typeof o[prop] === 'object' || typeof o[prop] === 'function') && !Object.isFrozen(o[prop])) {\n      deepFreeze(o[prop]);\n    }\n  });\n  return o;\n};\n/**\n * @ignore\n */\n\n\nclass InternalStateOperations {\n  constructor(_stateStream, _dispatcher, _config) {\n    this._stateStream = _stateStream;\n    this._dispatcher = _dispatcher;\n    this._config = _config;\n  }\n  /**\n   * Returns the root state operators.\n   */\n\n\n  getRootStateOperations() {\n    const rootStateOperations = {\n      getState: () => this._stateStream.getValue(),\n      setState: newState => this._stateStream.next(newState),\n      dispatch: actionOrActions => this._dispatcher.dispatch(actionOrActions)\n    };\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return this._config.developmentMode ? ensureStateAndActionsAreImmutable(rootStateOperations) : rootStateOperations;\n    } else {\n      return rootStateOperations;\n    }\n  }\n\n  setStateToTheCurrentWithNew(results) {\n    const stateOperations = this.getRootStateOperations(); // Get our current stream\n\n    const currentState = stateOperations.getState(); // Set the state to the current + new\n\n    stateOperations.setState(Object.assign(Object.assign({}, currentState), results.defaults));\n  }\n\n}\n/** @nocollapse */\n\n\nInternalStateOperations.ɵfac = function InternalStateOperations_Factory(t) {\n  return new (t || InternalStateOperations)(i0.ɵɵinject(StateStream), i0.ɵɵinject(InternalDispatcher), i0.ɵɵinject(NgxsConfig));\n};\n/** @nocollapse */\n\n\nInternalStateOperations.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InternalStateOperations,\n  factory: InternalStateOperations.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternalStateOperations, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: StateStream\n    }, {\n      type: InternalDispatcher\n    }, {\n      type: NgxsConfig\n    }];\n  }, null);\n})();\n\nfunction ensureStateAndActionsAreImmutable(root) {\n  return {\n    getState: () => root.getState(),\n    setState: value => {\n      const frozenValue = deepFreeze(value);\n      return root.setState(frozenValue);\n    },\n    dispatch: actions => {\n      return root.dispatch(actions);\n    }\n  };\n}\n\nfunction simplePatch(value) {\n  return existingState => {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (Array.isArray(value)) {\n        throwPatchingArrayError();\n      } else if (typeof value !== 'object') {\n        throwPatchingPrimitiveError();\n      }\n    }\n\n    const newState = Object.assign({}, existingState);\n\n    for (const key in value) {\n      // deep clone for patch compatibility\n      newState[key] = value[key];\n    }\n\n    return newState;\n  };\n}\n/**\n * State Context factory class\n * @ignore\n */\n\n\nclass StateContextFactory {\n  constructor(_internalStateOperations) {\n    this._internalStateOperations = _internalStateOperations;\n  }\n  /**\n   * Create the state context\n   */\n\n\n  createStateContext(mappedStore) {\n    const root = this._internalStateOperations.getRootStateOperations();\n\n    return {\n      getState() {\n        const currentAppState = root.getState();\n        return getState(currentAppState, mappedStore.path);\n      },\n\n      patchState(val) {\n        const currentAppState = root.getState();\n        const patchOperator = simplePatch(val);\n        return setStateFromOperator(root, currentAppState, patchOperator, mappedStore.path);\n      },\n\n      setState(val) {\n        const currentAppState = root.getState();\n        return isStateOperator(val) ? setStateFromOperator(root, currentAppState, val, mappedStore.path) : setStateValue(root, currentAppState, val, mappedStore.path);\n      },\n\n      dispatch(actions) {\n        return root.dispatch(actions);\n      }\n\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nStateContextFactory.ɵfac = function StateContextFactory_Factory(t) {\n  return new (t || StateContextFactory)(i0.ɵɵinject(InternalStateOperations));\n};\n/** @nocollapse */\n\n\nStateContextFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: StateContextFactory,\n  factory: StateContextFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateContextFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InternalStateOperations\n    }];\n  }, null);\n})();\n\nfunction setStateValue(root, currentAppState, newValue, path) {\n  const newAppState = setValue(currentAppState, path, newValue);\n  root.setState(newAppState);\n  return newAppState; // In doing this refactoring I noticed that there is a 'bug' where the\n  // application state is returned instead of this state slice.\n  // This has worked this way since the beginning see:\n  // https://github.com/ngxs/store/blame/324c667b4b7debd8eb979006c67ca0ae347d88cd/src/state-factory.ts\n  // This needs to be fixed, but is a 'breaking' change.\n  // I will do this fix in a subsequent PR and we can decide how to handle it.\n}\n\nfunction setStateFromOperator(root, currentAppState, stateOperator, path) {\n  const local = getState(currentAppState, path);\n  const newValue = stateOperator(local);\n  return setStateValue(root, currentAppState, newValue, path);\n}\n\nfunction getState(currentAppState, path) {\n  return getValue(currentAppState, path);\n}\n\nconst stateNameRegex = new RegExp('^[a-zA-Z0-9_]+$');\n\nfunction ensureStateNameIsValid(name) {\n  if (!name) {\n    throwStateNamePropertyError();\n  } else if (!stateNameRegex.test(name)) {\n    throwStateNameError(name);\n  }\n}\n\nfunction ensureStateNameIsUnique(stateName, state, statesByName) {\n  const existingState = statesByName[stateName];\n\n  if (existingState && existingState !== state) {\n    throwStateUniqueError(stateName, state.name, existingState.name);\n  }\n}\n\nfunction ensureStatesAreDecorated(stateClasses) {\n  stateClasses.forEach(stateClass => {\n    if (!getStoreMetadata$1(stateClass)) {\n      throwStateDecoratorError(stateClass.name);\n    }\n  });\n}\n/**\n * All provided or injected tokens must have `@Injectable` decorator\n * (previously, injected tokens without `@Injectable` were allowed\n * if another decorator was used, e.g. pipes).\n */\n\n\nfunction ensureStateClassIsInjectable(stateClass) {\n  if (jit_hasInjectableAnnotation(stateClass) || aot_hasNgInjectableDef(stateClass)) {\n    return;\n  }\n\n  console.warn(getUndecoratedStateInIvyWarningMessage(stateClass.name));\n}\n\nfunction aot_hasNgInjectableDef(stateClass) {\n  // `ɵprov` is a static property added by the NGCC compiler. It always exists in\n  // AOT mode because this property is added before runtime. If an application is running in\n  // JIT mode then this property can be added by the `@Injectable()` decorator. The `@Injectable()`\n  // decorator has to go after the `@State()` decorator, thus we prevent users from unwanted DI errors.\n  return !!stateClass.ɵprov;\n}\n\nfunction jit_hasInjectableAnnotation(stateClass) {\n  // `ɵprov` doesn't exist in JIT mode (for instance when running unit tests with Jest).\n  const annotations = stateClass.__annotations__ || [];\n  return annotations.some(annotation => (annotation === null || annotation === void 0 ? void 0 : annotation.ngMetadataName) === 'Injectable');\n}\n/**\n * Init action\n */\n\n\nclass InitState {}\n\nInitState.type = '@@INIT';\n/**\n * Update action\n */\n\nclass UpdateState {\n  constructor(addedStates) {\n    this.addedStates = addedStates;\n  }\n\n}\n\nUpdateState.type = '@@UPDATE_STATE';\nconst NGXS_DEVELOPMENT_OPTIONS = new InjectionToken('NGXS_DEVELOPMENT_OPTIONS', {\n  providedIn: 'root',\n  factory: () => ({\n    warnOnUnhandledActions: true\n  })\n});\n\nclass NgxsUnhandledActionsLogger {\n  constructor(options) {\n    /**\n     * These actions should be ignored by default; the user can increase this\n     * list in the future via the `ignoreActions` method.\n     */\n    this._ignoredActions = new Set([InitState.type, UpdateState.type]);\n\n    if (typeof options.warnOnUnhandledActions === 'object') {\n      this.ignoreActions(...options.warnOnUnhandledActions.ignore);\n    }\n  }\n  /**\n   * Adds actions to the internal list of actions that should be ignored.\n   */\n\n\n  ignoreActions(...actions) {\n    for (const action of actions) {\n      this._ignoredActions.add(action.type);\n    }\n  }\n  /** @internal */\n\n\n  warn(action) {\n    const actionShouldBeIgnored = Array.from(this._ignoredActions).some(type => type === getActionTypeFromInstance(action));\n\n    if (actionShouldBeIgnored) {\n      return;\n    }\n\n    action = action.constructor && action.constructor.name !== 'Object' ? action.constructor.name : action.type;\n    console.warn(`The ${action} action has been dispatched but hasn't been handled. This may happen if the state with an action handler for this action is not registered.`);\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsUnhandledActionsLogger.ɵfac = function NgxsUnhandledActionsLogger_Factory(t) {\n  return new (t || NgxsUnhandledActionsLogger)(i0.ɵɵinject(NGXS_DEVELOPMENT_OPTIONS));\n};\n/** @nocollapse */\n\n\nNgxsUnhandledActionsLogger.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxsUnhandledActionsLogger,\n  factory: NgxsUnhandledActionsLogger.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsUnhandledActionsLogger, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGXS_DEVELOPMENT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\n/**\n * The `StateFactory` class adds root and feature states to the graph.\n * This extracts state names from state classes, checks if they already\n * exist in the global graph, throws errors if their names are invalid, etc.\n * See its constructor, state factories inject state factories that are\n * parent-level providers. This is required to get feature states from the\n * injector on the same level.\n *\n * The `NgxsModule.forFeature(...)` returns `providers: [StateFactory, ...states]`.\n * The `StateFactory` is initialized on the feature level and goes through `...states`\n * to get them from the injector through `injector.get(state)`.\n * @ignore\n */\n\nclass StateFactory {\n  constructor(_injector, _config, _parentFactory, _actions, _actionResults, _stateContextFactory, _initialState) {\n    this._injector = _injector;\n    this._config = _config;\n    this._parentFactory = _parentFactory;\n    this._actions = _actions;\n    this._actionResults = _actionResults;\n    this._stateContextFactory = _stateContextFactory;\n    this._initialState = _initialState;\n    this._actionsSubscription = null;\n    this._states = [];\n    this._statesByName = {};\n    this._statePaths = {};\n    this.getRuntimeSelectorContext = memoize(() => {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const stateFactory = this;\n\n      function resolveGetter(key) {\n        const path = stateFactory.statePaths[key];\n        return path ? propGetter(path.split('.'), stateFactory._config) : null;\n      }\n\n      const context = this._parentFactory ? this._parentFactory.getRuntimeSelectorContext() : {\n        getStateGetter(key) {\n          let getter = resolveGetter(key);\n\n          if (getter) {\n            return getter;\n          }\n\n          return (...args) => {\n            // Late loaded getter\n            if (!getter) {\n              getter = resolveGetter(key);\n            }\n\n            return getter ? getter(...args) : undefined;\n          };\n        },\n\n        getSelectorOptions(localOptions) {\n          const globalSelectorOptions = stateFactory._config.selectorOptions;\n          return Object.assign(Object.assign({}, globalSelectorOptions), localOptions || {});\n        }\n\n      };\n      return context;\n    });\n  }\n\n  get states() {\n    return this._parentFactory ? this._parentFactory.states : this._states;\n  }\n\n  get statesByName() {\n    return this._parentFactory ? this._parentFactory.statesByName : this._statesByName;\n  }\n\n  get statePaths() {\n    return this._parentFactory ? this._parentFactory.statePaths : this._statePaths;\n  }\n\n  static _cloneDefaults(defaults) {\n    let value = defaults;\n\n    if (Array.isArray(defaults)) {\n      value = defaults.slice();\n    } else if (isObject(defaults)) {\n      value = Object.assign({}, defaults);\n    } else if (defaults === undefined) {\n      value = {};\n    }\n\n    return value;\n  }\n\n  ngOnDestroy() {\n    var _a;\n\n    (_a = this._actionsSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n  }\n  /**\n   * Add a new state to the global defs.\n   */\n\n\n  add(stateClasses) {\n    if (NG_DEV_MODE) {\n      ensureStatesAreDecorated(stateClasses);\n    }\n\n    const {\n      newStates\n    } = this.addToStatesMap(stateClasses);\n    if (!newStates.length) return [];\n    const stateGraph = buildGraph(newStates);\n    const sortedStates = topologicalSort(stateGraph);\n    const paths = findFullParentPath(stateGraph);\n    const nameGraph = nameToState(newStates);\n    const bootstrappedStores = [];\n\n    for (const name of sortedStates) {\n      const stateClass = nameGraph[name];\n      const path = paths[name];\n      const meta = stateClass[META_KEY];\n      this.addRuntimeInfoToMeta(meta, path); // Note: previously we called `ensureStateClassIsInjectable` within the\n      // `State` decorator. This check is moved here because the `ɵprov` property\n      // will not exist on the class in JIT mode (because it's set asynchronously\n      // during JIT compilation through `Object.defineProperty`).\n\n      if (NG_DEV_MODE) {\n        ensureStateClassIsInjectable(stateClass);\n      }\n\n      const stateMap = {\n        name,\n        path,\n        isInitialised: false,\n        actions: meta.actions,\n        instance: this._injector.get(stateClass),\n        defaults: StateFactory._cloneDefaults(meta.defaults)\n      }; // ensure our store hasn't already been added\n      // but don't throw since it could be lazy\n      // loaded from different paths\n\n      if (!this.hasBeenMountedAndBootstrapped(name, path)) {\n        bootstrappedStores.push(stateMap);\n      }\n\n      this.states.push(stateMap);\n    }\n\n    return bootstrappedStores;\n  }\n  /**\n   * Add a set of states to the store and return the defaults\n   */\n\n\n  addAndReturnDefaults(stateClasses) {\n    const classes = stateClasses || [];\n    const mappedStores = this.add(classes);\n    const defaults = mappedStores.reduce((result, mappedStore) => setValue(result, mappedStore.path, mappedStore.defaults), {});\n    return {\n      defaults,\n      states: mappedStores\n    };\n  }\n\n  connectActionHandlers() {\n    // Note: We have to connect actions only once when the `StateFactory`\n    //       is being created for the first time. This checks if we're in\n    //       a child state factory and the parent state factory already exists.\n    if (this._parentFactory || this._actionsSubscription !== null) {\n      return;\n    }\n\n    const dispatched$ = new Subject();\n    this._actionsSubscription = this._actions.pipe(filter(ctx => ctx.status === \"DISPATCHED\"\n    /* Dispatched */\n    ), mergeMap(ctx => {\n      dispatched$.next(ctx);\n      const action = ctx.action;\n      return this.invokeActions(dispatched$, action).pipe(map(() => ({\n        action,\n        status: \"SUCCESSFUL\"\n        /* Successful */\n\n      })), defaultIfEmpty({\n        action,\n        status: \"CANCELED\"\n        /* Canceled */\n\n      }), catchError(error => of({\n        action,\n        status: \"ERRORED\"\n        /* Errored */\n        ,\n        error\n      })));\n    })).subscribe(ctx => this._actionResults.next(ctx));\n  }\n  /**\n   * Invoke actions on the states.\n   */\n\n\n  invokeActions(dispatched$, action) {\n    const type = getActionTypeFromInstance(action);\n    const results = []; // Determines whether the dispatched action has been handled, this is assigned\n    // to `true` within the below `for` loop if any `actionMetas` has been found.\n\n    let actionHasBeenHandled = false;\n\n    for (const metadata of this.states) {\n      const actionMetas = metadata.actions[type];\n\n      if (actionMetas) {\n        for (const actionMeta of actionMetas) {\n          const stateContext = this._stateContextFactory.createStateContext(metadata);\n\n          try {\n            let result = metadata.instance[actionMeta.fn](stateContext, action);\n\n            if (result instanceof Promise) {\n              result = from(result);\n            }\n\n            if (isObservable(result)) {\n              // If this observable has been completed w/o emitting\n              // any value then we wouldn't want to complete the whole chain\n              // of actions. Since if any observable completes then\n              // action will be canceled.\n              // For instance if any action handler would've had such statement:\n              // `handler(ctx) { return EMPTY; }`\n              // then the action will be canceled.\n              // See https://github.com/ngxs/store/issues/1568\n              result = result.pipe(mergeMap(value => {\n                if (value instanceof Promise) {\n                  return from(value);\n                }\n\n                if (isObservable(value)) {\n                  return value;\n                }\n\n                return of(value);\n              }), defaultIfEmpty({}));\n\n              if (actionMeta.options.cancelUncompleted) {\n                // todo: ofActionDispatched should be used with action class\n                result = result.pipe(takeUntil(dispatched$.pipe(ofActionDispatched(action))));\n              }\n            } else {\n              result = of({}).pipe(shareReplay());\n            }\n\n            results.push(result);\n          } catch (e) {\n            results.push(throwError(e));\n          }\n\n          actionHasBeenHandled = true;\n        }\n      }\n    } // The `NgxsUnhandledActionsLogger` is a tree-shakable class which functions\n    // only during development.\n\n\n    if (NG_DEV_MODE && !actionHasBeenHandled) {\n      const unhandledActionsLogger = this._injector.get(NgxsUnhandledActionsLogger, null); // The `NgxsUnhandledActionsLogger` will not be resolved by the injector if the\n      // `NgxsDevelopmentModule` is not provided. It's enough to check whether the `injector.get`\n      // didn't return `null` so we may ensure the module has been imported.\n\n\n      if (unhandledActionsLogger) {\n        unhandledActionsLogger.warn(action);\n      }\n    }\n\n    if (!results.length) {\n      results.push(of({}));\n    }\n\n    return forkJoin(results);\n  }\n\n  addToStatesMap(stateClasses) {\n    const newStates = [];\n    const statesMap = this.statesByName;\n\n    for (const stateClass of stateClasses) {\n      const stateName = getStoreMetadata$1(stateClass).name;\n\n      if (NG_DEV_MODE) {\n        ensureStateNameIsUnique(stateName, stateClass, statesMap);\n      }\n\n      const unmountedState = !statesMap[stateName];\n\n      if (unmountedState) {\n        newStates.push(stateClass);\n        statesMap[stateName] = stateClass;\n      }\n    }\n\n    return {\n      newStates\n    };\n  }\n\n  addRuntimeInfoToMeta(meta, path) {\n    this.statePaths[meta.name] = path; // TODO: v4 - we plan to get rid of the path property because it is non-deterministic\n    // we can do this when we get rid of the incorrectly exposed getStoreMetadata\n    // We will need to come up with an alternative in v4 because this is used by many plugins\n\n    meta.path = path;\n  }\n\n  hasBeenMountedAndBootstrapped(name, path) {\n    const valueIsBootstrappedInInitialState = getValue(this._initialState, path) !== undefined; // This checks whether a state has been already added to the global graph and\n    // its lifecycle is in 'bootstrapped' state.\n\n    return this.statesByName[name] && valueIsBootstrappedInInitialState;\n  }\n\n}\n/** @nocollapse */\n\n\nStateFactory.ɵfac = function StateFactory_Factory(t) {\n  return new (t || StateFactory)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(NgxsConfig), i0.ɵɵinject(StateFactory, 12), i0.ɵɵinject(InternalActions), i0.ɵɵinject(InternalDispatchedActionResults), i0.ɵɵinject(StateContextFactory), i0.ɵɵinject(INITIAL_STATE_TOKEN, 8));\n};\n/** @nocollapse */\n\n\nStateFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: StateFactory,\n  factory: StateFactory.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateFactory, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: NgxsConfig\n    }, {\n      type: StateFactory,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: InternalActions\n    }, {\n      type: InternalDispatchedActionResults\n    }, {\n      type: StateContextFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [INITIAL_STATE_TOKEN]\n      }]\n    }];\n  }, null);\n})();\n\nfunction createRootSelectorFactory(selectorMetaData, selectors, memoizedSelectorFn) {\n  return context => {\n    const {\n      argumentSelectorFunctions,\n      selectorOptions\n    } = getRuntimeSelectorInfo(context, selectorMetaData, selectors);\n    return function selectFromRoot(rootState) {\n      // Determine arguments from the app state using the selectors\n      const results = argumentSelectorFunctions.map(argFn => argFn(rootState)); // if the lambda tries to access a something on the\n      // state that doesn't exist, it will throw a TypeError.\n      // since this is quite usual behaviour, we simply return undefined if so.\n\n      try {\n        return memoizedSelectorFn(...results);\n      } catch (ex) {\n        if (ex instanceof TypeError && selectorOptions.suppressErrors) {\n          return undefined;\n        }\n\n        throw ex;\n      }\n    };\n  };\n}\n\nfunction createMemoizedSelectorFn(originalFn, creationMetadata) {\n  const containerClass = creationMetadata && creationMetadata.containerClass;\n\n  const wrappedFn = function wrappedSelectorFn(...args) {\n    const returnValue = originalFn.apply(containerClass, args);\n\n    if (returnValue instanceof Function) {\n      const innerMemoizedFn = memoize.apply(null, [returnValue]);\n      return innerMemoizedFn;\n    }\n\n    return returnValue;\n  };\n\n  const memoizedFn = memoize(wrappedFn);\n  Object.setPrototypeOf(memoizedFn, originalFn);\n  return memoizedFn;\n}\n\nfunction getRuntimeSelectorInfo(context, selectorMetaData, selectors = []) {\n  const localSelectorOptions = selectorMetaData.getSelectorOptions();\n  const selectorOptions = context.getSelectorOptions(localSelectorOptions);\n  const selectorsToApply = getSelectorsToApply(selectors, selectorOptions, selectorMetaData.containerClass);\n  const argumentSelectorFunctions = selectorsToApply.map(selector => {\n    const factory = getRootSelectorFactory(selector);\n    return factory(context);\n  });\n  return {\n    selectorOptions,\n    argumentSelectorFunctions\n  };\n}\n\nfunction getSelectorsToApply(selectors = [], selectorOptions, containerClass) {\n  const selectorsToApply = [];\n  const canInjectContainerState = selectors.length === 0 || selectorOptions.injectContainerState;\n\n  if (containerClass && canInjectContainerState) {\n    // If we are on a state class, add it as the first selector parameter\n    const metadata = getStoreMetadata$1(containerClass);\n\n    if (metadata) {\n      selectorsToApply.push(containerClass);\n    }\n  }\n\n  if (selectors) {\n    selectorsToApply.push(...selectors);\n  }\n\n  return selectorsToApply;\n}\n/**\n * This function gets the factory function to create the selector to get the selected slice from the app state\n * @ignore\n */\n\n\nfunction getRootSelectorFactory(selector) {\n  const metadata = getSelectorMetadata$1(selector) || getStoreMetadata$1(selector);\n  return metadata && metadata.makeRootSelector || (() => selector);\n} // tslint:disable:unified-signatures\n\n\nclass Store {\n  constructor(_stateStream, _internalStateOperations, _config, _internalExecutionStrategy, _stateFactory, initialStateValue) {\n    this._stateStream = _stateStream;\n    this._internalStateOperations = _internalStateOperations;\n    this._config = _config;\n    this._internalExecutionStrategy = _internalExecutionStrategy;\n    this._stateFactory = _stateFactory;\n    /**\n     * This is a derived state stream that leaves NGXS execution strategy to emit state changes within the Angular zone,\n     * because state is being changed actually within the `<root>` zone, see `InternalDispatcher#dispatchSingle`.\n     * All selects would use this stream, and it would call leave only once for any state change across all active selectors.\n     */\n\n    this._selectableStateStream = this._stateStream.pipe(leaveNgxs(this._internalExecutionStrategy), shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n    this.initStateStream(initialStateValue);\n  }\n  /**\n   * Dispatches event(s).\n   */\n\n\n  dispatch(actionOrActions) {\n    return this._internalStateOperations.getRootStateOperations().dispatch(actionOrActions);\n  }\n\n  select(selector) {\n    const selectorFn = this.getStoreBoundSelectorFn(selector);\n    return this._selectableStateStream.pipe(map(selectorFn), catchError(err => {\n      // if error is TypeError we swallow it to prevent usual errors with property access\n      const {\n        suppressErrors\n      } = this._config.selectorOptions;\n\n      if (err instanceof TypeError && suppressErrors) {\n        return of(undefined);\n      } // rethrow other errors\n\n\n      return throwError(err);\n    }), distinctUntilChanged(), leaveNgxs(this._internalExecutionStrategy));\n  }\n\n  selectOnce(selector) {\n    return this.select(selector).pipe(take(1));\n  }\n\n  selectSnapshot(selector) {\n    const selectorFn = this.getStoreBoundSelectorFn(selector);\n    return selectorFn(this._stateStream.getValue());\n  }\n  /**\n   * Allow the user to subscribe to the root of the state\n   */\n\n\n  subscribe(fn) {\n    return this._selectableStateStream.pipe(leaveNgxs(this._internalExecutionStrategy)).subscribe(fn);\n  }\n  /**\n   * Return the raw value of the state.\n   */\n\n\n  snapshot() {\n    return this._internalStateOperations.getRootStateOperations().getState();\n  }\n  /**\n   * Reset the state to a specific point in time. This method is useful\n   * for plugin's who need to modify the state directly or unit testing.\n   */\n\n\n  reset(state) {\n    return this._internalStateOperations.getRootStateOperations().setState(state);\n  }\n\n  getStoreBoundSelectorFn(selector) {\n    const makeSelectorFn = getRootSelectorFactory(selector);\n\n    const runtimeContext = this._stateFactory.getRuntimeSelectorContext();\n\n    return makeSelectorFn(runtimeContext);\n  }\n\n  initStateStream(initialStateValue) {\n    const value = this._stateStream.value;\n    const storeIsEmpty = !value || Object.keys(value).length === 0;\n\n    if (storeIsEmpty) {\n      const defaultStateNotEmpty = Object.keys(this._config.defaultsState).length > 0;\n      const storeValues = defaultStateNotEmpty ? Object.assign(Object.assign({}, this._config.defaultsState), initialStateValue) : initialStateValue;\n\n      this._stateStream.next(storeValues);\n    }\n  }\n\n}\n/** @nocollapse */\n\n\nStore.ɵfac = function Store_Factory(t) {\n  return new (t || Store)(i0.ɵɵinject(StateStream), i0.ɵɵinject(InternalStateOperations), i0.ɵɵinject(NgxsConfig), i0.ɵɵinject(InternalNgxsExecutionStrategy), i0.ɵɵinject(StateFactory), i0.ɵɵinject(INITIAL_STATE_TOKEN, 8));\n};\n/** @nocollapse */\n\n\nStore.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Store,\n  factory: Store.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Store, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: StateStream\n    }, {\n      type: InternalStateOperations\n    }, {\n      type: NgxsConfig\n    }, {\n      type: InternalNgxsExecutionStrategy\n    }, {\n      type: StateFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [INITIAL_STATE_TOKEN]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Allows the select decorator to get access to the DI store, this is used internally\n * in `@Select` decorator.\n */\n\n\nclass SelectFactory {\n  constructor(store, config) {\n    SelectFactory.store = store;\n    SelectFactory.config = config;\n  }\n\n  ngOnDestroy() {\n    SelectFactory.store = null;\n    SelectFactory.config = null;\n  }\n\n}\n\nSelectFactory.store = null;\nSelectFactory.config = null;\n/** @nocollapse */\n\nSelectFactory.ɵfac = function SelectFactory_Factory(t) {\n  return new (t || SelectFactory)(i0.ɵɵinject(Store), i0.ɵɵinject(NgxsConfig));\n};\n/** @nocollapse */\n\n\nSelectFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SelectFactory,\n  factory: SelectFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SelectFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Store\n    }, {\n      type: NgxsConfig\n    }];\n  }, null);\n})();\n\nclass LifecycleStateManager {\n  constructor(_store, _internalErrorReporter, _internalStateOperations, _stateContextFactory, _bootstrapper) {\n    this._store = _store;\n    this._internalErrorReporter = _internalErrorReporter;\n    this._internalStateOperations = _internalStateOperations;\n    this._stateContextFactory = _stateContextFactory;\n    this._bootstrapper = _bootstrapper;\n    this._destroy$ = new Subject();\n  }\n\n  ngOnDestroy() {\n    this._destroy$.next();\n  }\n\n  ngxsBootstrap(action, results) {\n    this._internalStateOperations.getRootStateOperations().dispatch(action).pipe(filter(() => !!results), tap(() => this._invokeInitOnStates(results.states)), mergeMap(() => this._bootstrapper.appBootstrapped$), filter(appBootstrapped => !!appBootstrapped), catchError(error => {\n      // The `SafeSubscriber` (which is used by most RxJS operators) re-throws\n      // errors asynchronously (`setTimeout(() => { throw error })`). This might\n      // break existing user's code or unit tests. We catch the error manually to\n      // be backward compatible with the old behavior.\n      this._internalErrorReporter.reportErrorSafely(error);\n\n      return EMPTY;\n    }), takeUntil(this._destroy$)).subscribe(() => this._invokeBootstrapOnStates(results.states));\n  }\n\n  _invokeInitOnStates(mappedStores) {\n    for (const mappedStore of mappedStores) {\n      const instance = mappedStore.instance;\n\n      if (instance.ngxsOnChanges) {\n        this._store.select(state => getValue(state, mappedStore.path)).pipe(startWith(undefined), pairwise(), takeUntil(this._destroy$)).subscribe(([previousValue, currentValue]) => {\n          const change = new NgxsSimpleChange(previousValue, currentValue, !mappedStore.isInitialised);\n          instance.ngxsOnChanges(change);\n        });\n      }\n\n      if (instance.ngxsOnInit) {\n        instance.ngxsOnInit(this._getStateContext(mappedStore));\n      }\n\n      mappedStore.isInitialised = true;\n    }\n  }\n\n  _invokeBootstrapOnStates(mappedStores) {\n    for (const mappedStore of mappedStores) {\n      const instance = mappedStore.instance;\n\n      if (instance.ngxsAfterBootstrap) {\n        instance.ngxsAfterBootstrap(this._getStateContext(mappedStore));\n      }\n    }\n  }\n\n  _getStateContext(mappedStore) {\n    return this._stateContextFactory.createStateContext(mappedStore);\n  }\n\n}\n/** @nocollapse */\n\n\nLifecycleStateManager.ɵfac = function LifecycleStateManager_Factory(t) {\n  return new (t || LifecycleStateManager)(i0.ɵɵinject(Store), i0.ɵɵinject(InternalErrorReporter), i0.ɵɵinject(InternalStateOperations), i0.ɵɵinject(StateContextFactory), i0.ɵɵinject(i5.NgxsBootstrapper));\n};\n/** @nocollapse */\n\n\nLifecycleStateManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LifecycleStateManager,\n  factory: LifecycleStateManager.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LifecycleStateManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Store\n    }, {\n      type: InternalErrorReporter\n    }, {\n      type: InternalStateOperations\n    }, {\n      type: StateContextFactory\n    }, {\n      type: i5.NgxsBootstrapper\n    }];\n  }, null);\n})();\n/**\n * Root module\n * @ignore\n */\n\n\nclass NgxsRootModule {\n  constructor(factory, internalStateOperations, _store, _select, states = [], lifecycleStateManager) {\n    // Add stores to the state graph and return their defaults\n    const results = factory.addAndReturnDefaults(states);\n    internalStateOperations.setStateToTheCurrentWithNew(results); // Connect our actions stream\n\n    factory.connectActionHandlers(); // Dispatch the init action and invoke init and bootstrap functions after\n\n    lifecycleStateManager.ngxsBootstrap(new InitState(), results);\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsRootModule.ɵfac = function NgxsRootModule_Factory(t) {\n  return new (t || NgxsRootModule)(i0.ɵɵinject(StateFactory), i0.ɵɵinject(InternalStateOperations), i0.ɵɵinject(Store), i0.ɵɵinject(SelectFactory), i0.ɵɵinject(ROOT_STATE_TOKEN, 8), i0.ɵɵinject(LifecycleStateManager));\n};\n/** @nocollapse */\n\n\nNgxsRootModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsRootModule\n});\n/** @nocollapse */\n\nNgxsRootModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsRootModule, [{\n    type: NgModule\n  }], function () {\n    return [{\n      type: StateFactory\n    }, {\n      type: InternalStateOperations\n    }, {\n      type: Store\n    }, {\n      type: SelectFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ROOT_STATE_TOKEN]\n      }]\n    }, {\n      type: LifecycleStateManager\n    }];\n  }, null);\n})();\n/**\n * Feature module\n * @ignore\n */\n\n\nclass NgxsFeatureModule {\n  constructor(_store, internalStateOperations, factory, states = [], lifecycleStateManager) {\n    // Since FEATURE_STATE_TOKEN is a multi token, we need to\n    // flatten it [[Feature1State, Feature2State], [Feature3State]]\n    const flattenedStates = NgxsFeatureModule.flattenStates(states); // add stores to the state graph and return their defaults\n\n    const results = factory.addAndReturnDefaults(flattenedStates);\n\n    if (results.states.length) {\n      internalStateOperations.setStateToTheCurrentWithNew(results); // dispatch the update action and invoke init and bootstrap functions after\n\n      lifecycleStateManager.ngxsBootstrap(new UpdateState(results.defaults), results);\n    }\n  }\n\n  static flattenStates(states = []) {\n    return states.reduce((total, values) => total.concat(values), []);\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsFeatureModule.ɵfac = function NgxsFeatureModule_Factory(t) {\n  return new (t || NgxsFeatureModule)(i0.ɵɵinject(Store), i0.ɵɵinject(InternalStateOperations), i0.ɵɵinject(StateFactory), i0.ɵɵinject(FEATURE_STATE_TOKEN, 8), i0.ɵɵinject(LifecycleStateManager));\n};\n/** @nocollapse */\n\n\nNgxsFeatureModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsFeatureModule\n});\n/** @nocollapse */\n\nNgxsFeatureModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsFeatureModule, [{\n    type: NgModule\n  }], function () {\n    return [{\n      type: Store\n    }, {\n      type: InternalStateOperations\n    }, {\n      type: StateFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FEATURE_STATE_TOKEN]\n      }]\n    }, {\n      type: LifecycleStateManager\n    }];\n  }, null);\n})();\n/**\n * Ngxs Module\n */\n\n\nclass NgxsModule {\n  /**\n   * Root module factory\n   */\n  static forRoot(states = [], options = {}) {\n    return {\n      ngModule: NgxsRootModule,\n      providers: [StateFactory, PluginManager, ...states, ...NgxsModule.ngxsTokenProviders(states, options)]\n    };\n  }\n  /**\n   * Feature module factory\n   */\n\n\n  static forFeature(states = []) {\n    return {\n      ngModule: NgxsFeatureModule,\n      providers: [// This is required on the feature level, see comments in `state-factory.ts`.\n      StateFactory, PluginManager, ...states, {\n        provide: FEATURE_STATE_TOKEN,\n        multi: true,\n        useValue: states\n      }]\n    };\n  }\n\n  static ngxsTokenProviders(states, options) {\n    return [{\n      provide: USER_PROVIDED_NGXS_EXECUTION_STRATEGY,\n      useValue: options.executionStrategy\n    }, {\n      provide: ROOT_STATE_TOKEN,\n      useValue: states\n    }, {\n      provide: ROOT_OPTIONS,\n      useValue: options\n    }, {\n      provide: APP_BOOTSTRAP_LISTENER,\n      useFactory: NgxsModule.appBootstrapListenerFactory,\n      multi: true,\n      deps: [NgxsBootstrapper]\n    }, {\n      provide: ɵNGXS_STATE_CONTEXT_FACTORY,\n      useExisting: StateContextFactory\n    }, {\n      provide: ɵNGXS_STATE_FACTORY,\n      useExisting: StateFactory\n    }];\n  }\n\n  static appBootstrapListenerFactory(bootstrapper) {\n    return () => bootstrapper.bootstrap();\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsModule.ɵfac = function NgxsModule_Factory(t) {\n  return new (t || NgxsModule)();\n};\n/** @nocollapse */\n\n\nNgxsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsModule\n});\n/** @nocollapse */\n\nNgxsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsModule, [{\n    type: NgModule\n  }], null, null);\n})();\n/**\n * Decorates a method with a action information.\n */\n\n\nfunction Action(actions, options) {\n  return (target, name) => {\n    // Caretaker note: we have still left the `typeof` condition in order to avoid\n    // creating a breaking change for projects that still use the View Engine.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const isStaticMethod = target.hasOwnProperty('prototype');\n\n      if (isStaticMethod) {\n        throwActionDecoratorError();\n      }\n    }\n\n    const meta = ensureStoreMetadata$1(target.constructor);\n\n    if (!Array.isArray(actions)) {\n      actions = [actions];\n    }\n\n    for (const action of actions) {\n      const type = action.type;\n\n      if (!meta.actions[type]) {\n        meta.actions[type] = [];\n      }\n\n      meta.actions[type].push({\n        fn: name,\n        options: options || {},\n        type\n      });\n    }\n  };\n}\n/**\n * Decorates a class with ngxs state information.\n */\n\n\nfunction State(options) {\n  return target => {\n    const stateClass = target;\n    const meta = ensureStoreMetadata$1(stateClass);\n    const inheritedStateClass = Object.getPrototypeOf(stateClass);\n    const optionsWithInheritance = getStateOptions(inheritedStateClass, options);\n    mutateMetaData({\n      meta,\n      inheritedStateClass,\n      optionsWithInheritance\n    });\n    stateClass[META_OPTIONS_KEY] = optionsWithInheritance;\n  };\n}\n\nfunction getStateOptions(inheritedStateClass, options) {\n  const inheritanceOptions = inheritedStateClass[META_OPTIONS_KEY] || {};\n  return Object.assign(Object.assign({}, inheritanceOptions), options);\n}\n\nfunction mutateMetaData(params) {\n  const {\n    meta,\n    inheritedStateClass,\n    optionsWithInheritance\n  } = params;\n  const {\n    children,\n    defaults,\n    name\n  } = optionsWithInheritance;\n  const stateName = typeof name === 'string' ? name : name && name.getName() || null;\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    ensureStateNameIsValid(stateName);\n  }\n\n  if (inheritedStateClass.hasOwnProperty(META_KEY)) {\n    const inheritedMeta = inheritedStateClass[META_KEY] || {};\n    meta.actions = Object.assign(Object.assign({}, meta.actions), inheritedMeta.actions);\n  }\n\n  meta.children = children;\n  meta.defaults = defaults;\n  meta.name = stateName;\n}\n\nconst DOLLAR_CHAR_CODE = 36;\n\nfunction createSelectObservable(selector) {\n  if (!SelectFactory.store) {\n    throwSelectFactoryNotConnectedError();\n  }\n\n  return SelectFactory.store.select(selector);\n}\n\nfunction createSelectorFn(name, rawSelector, paths = []) {\n  rawSelector = !rawSelector ? removeDollarAtTheEnd(name) : rawSelector;\n\n  if (typeof rawSelector === 'string') {\n    const propsArray = paths.length ? [rawSelector, ...paths] : rawSelector.split('.');\n    return propGetter(propsArray, SelectFactory.config);\n  }\n\n  return rawSelector;\n}\n/**\n * @example If `foo$` => make it just `foo`\n */\n\n\nfunction removeDollarAtTheEnd(name) {\n  const lastCharIndex = name.length - 1;\n  const dollarAtTheEnd = name.charCodeAt(lastCharIndex) === DOLLAR_CHAR_CODE;\n  return dollarAtTheEnd ? name.slice(0, lastCharIndex) : name;\n}\n/**\n * Decorator for selecting a slice of state from the store.\n */\n\n\nfunction Select(rawSelector, ...paths) {\n  return function (target, key) {\n    const name = key.toString();\n    const selectorId = `__${name}__selector`;\n    const selector = createSelectorFn(name, rawSelector, paths);\n    Object.defineProperties(target, {\n      [selectorId]: {\n        writable: true,\n        enumerable: false,\n        configurable: true\n      },\n      [name]: {\n        enumerable: true,\n        configurable: true,\n\n        get() {\n          return this[selectorId] || (this[selectorId] = createSelectObservable(selector));\n        }\n\n      }\n    });\n  };\n}\n\nconst SELECTOR_OPTIONS_META_KEY = 'NGXS_SELECTOR_OPTIONS_META';\nconst selectorOptionsMetaAccessor = {\n  getOptions: target => {\n    return target && target[SELECTOR_OPTIONS_META_KEY] || {};\n  },\n  defineOptions: (target, options) => {\n    if (!target) return;\n    target[SELECTOR_OPTIONS_META_KEY] = options;\n  }\n};\n\nfunction setupSelectorMetadata(originalFn, creationMetadata) {\n  const selectorMetaData = ensureSelectorMetadata$1(originalFn);\n  selectorMetaData.originalFn = originalFn;\n\n  let getExplicitSelectorOptions = () => ({});\n\n  if (creationMetadata) {\n    selectorMetaData.containerClass = creationMetadata.containerClass;\n    selectorMetaData.selectorName = creationMetadata.selectorName || null;\n    getExplicitSelectorOptions = creationMetadata.getSelectorOptions || getExplicitSelectorOptions;\n  }\n\n  const selectorMetaDataClone = Object.assign({}, selectorMetaData);\n\n  selectorMetaData.getSelectorOptions = () => getLocalSelectorOptions(selectorMetaDataClone, getExplicitSelectorOptions());\n\n  return selectorMetaData;\n}\n\nfunction getLocalSelectorOptions(selectorMetaData, explicitOptions) {\n  return Object.assign(Object.assign(Object.assign(Object.assign({}, selectorOptionsMetaAccessor.getOptions(selectorMetaData.containerClass) || {}), selectorOptionsMetaAccessor.getOptions(selectorMetaData.originalFn) || {}), selectorMetaData.getSelectorOptions() || {}), explicitOptions);\n}\n/**\n * Decorator for setting selector options at a method or class level.\n */\n\n\nfunction SelectorOptions(options) {\n  return function decorate(target, methodName, descriptor) {\n    if (methodName) {\n      descriptor || (descriptor = Object.getOwnPropertyDescriptor(target, methodName)); // Method Decorator\n\n      const originalFn = descriptor.value || descriptor.originalFn;\n\n      if (originalFn) {\n        selectorOptionsMetaAccessor.defineOptions(originalFn, options);\n      }\n    } else {\n      // Class Decorator\n      selectorOptionsMetaAccessor.defineOptions(target, options);\n    }\n  };\n}\n\nfunction ensureStoreMetadata(target) {\n  return ensureStoreMetadata$1(target);\n}\n\nfunction getStoreMetadata(target) {\n  return getStoreMetadata$1(target);\n}\n\nfunction ensureSelectorMetadata(target) {\n  return ensureSelectorMetadata$1(target);\n}\n\nfunction getSelectorMetadata(target) {\n  return getSelectorMetadata$1(target);\n}\n\nfunction createSelector(selectors, projector, creationMetadata) {\n  const memoizedFn = createMemoizedSelectorFn(projector, creationMetadata);\n  const selectorMetaData = setupSelectorMetadata(projector, creationMetadata);\n  selectorMetaData.makeRootSelector = createRootSelectorFactory(selectorMetaData, selectors, memoizedFn);\n  return memoizedFn;\n}\n\nfunction Selector(selectors) {\n  return (target, key, descriptor) => {\n    descriptor || (descriptor = Object.getOwnPropertyDescriptor(target, key));\n    const originalFn = descriptor === null || descriptor === void 0 ? void 0 : descriptor.value; // Caretaker note: we have still left the `typeof` condition in order to avoid\n    // creating a breaking change for projects that still use the View Engine.\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (originalFn && typeof originalFn !== 'function') {\n        throwSelectorDecoratorError();\n      }\n    }\n\n    const memoizedFn = createSelector(selectors, originalFn, {\n      containerClass: target,\n      selectorName: key.toString(),\n\n      getSelectorOptions() {\n        return {};\n      }\n\n    });\n    const newDescriptor = {\n      configurable: true,\n\n      get() {\n        return memoizedFn;\n      }\n\n    }; // Add hidden property to descriptor\n\n    newDescriptor['originalFn'] = originalFn;\n    return newDescriptor;\n  };\n}\n\nclass StateToken {\n  constructor(name) {\n    this.name = name;\n    const selectorMetadata = ensureSelectorMetadata$1(this);\n\n    selectorMetadata.makeRootSelector = runtimeContext => {\n      return runtimeContext.getStateGetter(this.name);\n    };\n  }\n\n  getName() {\n    return this.name;\n  }\n\n  toString() {\n    return `StateToken[${this.name}]`;\n  }\n\n}\n\nclass NgxsDevelopmentModule {\n  static forRoot(options) {\n    return {\n      ngModule: NgxsDevelopmentModule,\n      providers: [NgxsUnhandledActionsLogger, {\n        provide: NGXS_DEVELOPMENT_OPTIONS,\n        useValue: options\n      }]\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsDevelopmentModule.ɵfac = function NgxsDevelopmentModule_Factory(t) {\n  return new (t || NgxsDevelopmentModule)();\n};\n/** @nocollapse */\n\n\nNgxsDevelopmentModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsDevelopmentModule\n});\n/** @nocollapse */\n\nNgxsDevelopmentModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsDevelopmentModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\nfunction ensureValidSelector(selector, context = {}) {\n  const noun = context.noun || 'selector';\n  const prefix = context.prefix ? context.prefix + ': ' : '';\n  ensureValueProvided(selector, {\n    noun,\n    prefix: context.prefix\n  });\n  const metadata = getSelectorMetadata$1(selector) || getStoreMetadata$1(selector);\n\n  if (!metadata) {\n    throw new Error(`${prefix}The value provided as the ${noun} is not a valid selector.`);\n  }\n}\n\nfunction ensureValueProvided(value, context = {}) {\n  const noun = context.noun || 'value';\n  const prefix = context.prefix ? context.prefix + ': ' : '';\n\n  if (!value) {\n    throw new Error(`${prefix}A ${noun} must be provided.`);\n  }\n}\n\nfunction createModelSelector(selectorMap) {\n  const selectorKeys = Object.keys(selectorMap);\n  const selectors = Object.values(selectorMap);\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    ensureValidSelectorMap({\n      prefix: '[createModelSelector]',\n      selectorMap,\n      selectorKeys,\n      selectors\n    });\n  }\n\n  return createSelector(selectors, (...args) => {\n    return selectorKeys.reduce((obj, key, index) => {\n      obj[key] = args[index];\n      return obj;\n    }, {});\n  });\n}\n\nfunction ensureValidSelectorMap({\n  prefix,\n  selectorMap,\n  selectorKeys,\n  selectors\n}) {\n  ensureValueProvided(selectorMap, {\n    prefix,\n    noun: 'selector map'\n  });\n  ensureValueProvided(typeof selectorMap === 'object', {\n    prefix,\n    noun: 'valid selector map'\n  });\n  ensureValueProvided(selectorKeys.length, {\n    prefix,\n    noun: 'non-empty selector map'\n  });\n  selectors.forEach((selector, index) => ensureValidSelector(selector, {\n    prefix,\n    noun: `selector for the '${selectorKeys[index]}' property`\n  }));\n}\n\nfunction createPickSelector(selector, keys) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    ensureValidSelector(selector, {\n      prefix: '[createPickSelector]'\n    });\n  }\n\n  const validKeys = keys.filter(Boolean);\n  const selectors = validKeys.map(key => createSelector([selector], s => s[key]));\n  return createSelector([...selectors], (...props) => {\n    return validKeys.reduce((acc, key, index) => {\n      acc[key] = props[index];\n      return acc;\n    }, {});\n  });\n}\n\nfunction createPropertySelectors(parentSelector) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    ensureValidSelector(parentSelector, {\n      prefix: '[createPropertySelectors]',\n      noun: 'parent selector'\n    });\n  }\n\n  const cache = {};\n  return new Proxy({}, {\n    get(_target, prop) {\n      const selector = cache[prop] || createSelector([parentSelector], s => s === null || s === void 0 ? void 0 : s[prop]);\n      cache[prop] = selector;\n      return selector;\n    }\n\n  });\n}\n/**\n * The public api for consumers of @ngxs/store\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Action, Actions, InitState, NGXS_PLUGINS, NgxsDevelopmentModule, NgxsModule, NgxsSimpleChange, NgxsUnhandledActionsLogger, NoopNgxsExecutionStrategy, Select, Selector, SelectorOptions, State, StateStream, StateToken, Store, UpdateState, actionMatcher, createModelSelector, createPickSelector, createPropertySelectors, createSelector, ensureSelectorMetadata, ensureStoreMetadata, getActionTypeFromInstance, getSelectorMetadata, getStoreMetadata, getValue, ofAction, ofActionCanceled, ofActionCompleted, ofActionDispatched, ofActionErrored, ofActionSuccessful, setValue, NgxsFeatureModule as ɵNgxsFeatureModule, NgxsRootModule as ɵNgxsRootModule };", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@ngxs/store/fesm2015/ngxs-store.js"], "names": ["i0", "NgZone", "PLATFORM_ID", "Injectable", "Inject", "InjectionToken", "inject", "INJECTOR", "ɵglobal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Optional", "SkipSelf", "NgModule", "APP_BOOTSTRAP_LISTENER", "i5", "memoize", "INITIAL_STATE_TOKEN", "NgxsBootstrapper", "ɵNGXS_STATE_CONTEXT_FACTORY", "ɵNGXS_STATE_FACTORY", "isPlatformServer", "Observable", "Subject", "BehaviorSubject", "of", "fork<PERSON><PERSON>n", "throwError", "EMPTY", "from", "isObservable", "filter", "map", "share", "shareReplay", "take", "exhaustMap", "mergeMap", "defaultIfEmpty", "catchError", "takeUntil", "distinctUntilChanged", "tap", "startWith", "pairwise", "isStateOperator", "getActionTypeFromInstance", "action", "constructor", "type", "actionMatcher", "action1", "type1", "action2", "setValue", "obj", "prop", "val", "Object", "assign", "split", "lastIndex", "length", "reduce", "acc", "part", "index", "Array", "isArray", "slice", "getValue", "isObject$1", "item", "mergeDeep", "base", "sources", "source", "shift", "key", "throwStateNameError", "name", "Error", "throwStateNamePropertyError", "throwStateUniqueError", "current", "newName", "old<PERSON>ame", "throwStateDecoratorError", "throwActionDecoratorError", "throwSelectorDecoratorError", "getZoneWarningMessage", "getUndecoratedStateInIvyWarningMessage", "throwSelectFactoryNotConnectedError", "throwPatchingArrayError", "throwPatchingPrimitiveError", "DispatchOutsideZoneNgxsExecutionStrategy", "_ngZone", "_platformId", "ngDevMode", "verifyZoneIsNotNooped", "enter", "func", "runInsideAngular", "runOutsideAngular", "leave", "isInAngularZone", "run", "ɵfac", "ɵprov", "args", "providedIn", "undefined", "decorators", "ngZone", "console", "warn", "ROOT_OPTIONS", "ROOT_STATE_TOKEN", "FEATURE_STATE_TOKEN", "NGXS_PLUGINS", "META_KEY", "META_OPTIONS_KEY", "SELECTOR_META_KEY", "NgxsConfig", "defaultsState", "selectorOptions", "injectContainerState", "suppressErrors", "compatibility", "strictContentSecurityPolicy", "executionStrategy", "options", "useFactory", "deps", "NgxsSimpleChange", "previousValue", "currentValue", "firstChange", "NoopNgxsExecutionStrategy", "USER_PROVIDED_NGXS_EXECUTION_STRATEGY", "NGXS_EXECUTION_STRATEGY", "factory", "injector", "get", "Zone", "ensureStoreMetadata$1", "target", "hasOwnProperty", "defaultMetadata", "actions", "defaults", "path", "makeRootSelector", "context", "getStateGetter", "children", "defineProperty", "value", "getStoreMetadata$1", "ensureSelectorMetadata$1", "originalFn", "containerClass", "selector<PERSON>ame", "getSelectorOptions", "getSelectorMetadata$1", "compliantPropGetter", "paths", "copyOfPaths", "fastPropGetter", "segments", "seg", "i", "l", "expr", "fn", "Function", "propGetter", "config", "buildGraph", "stateClasses", "<PERSON><PERSON><PERSON>", "stateClass", "meta", "find", "g", "result", "nameToState", "states", "find<PERSON>ull<PERSON><PERSON>nt<PERSON>ath", "newObj", "visit", "child", "keyToFind", "indexOf", "parent", "topologicalSort", "graph", "sorted", "visited", "ancestors", "push", "for<PERSON>ach", "dep", "join", "keys", "k", "reverse", "isObject", "ofAction", "allowedTypes", "ofActionOperator", "ofActionDispatched", "ofActionSuccessful", "ofActionCanceled", "ofActionCompleted", "allowedStatuses", "mapActionResult", "ofActionErrored", "statuses", "mapOperator", "mapAction", "allowedMap", "createAllowedActionTypesMap", "allowedStatusMap", "createAllowedStatusesMap", "o", "pipe", "filterStatus", "ctx", "actionType", "typeMatch", "statusMatch", "status", "error", "successful", "canceled", "types", "filterMap", "klass", "leaveNgxs", "ngxsExecutionStrategy", "sink", "subscribe", "next", "complete", "InternalNgxsExecutionStrategy", "_executionStrategy", "orderedQueueOperation", "operation", "callsQueue", "busyPushingNext", "callOperation", "unshift", "nextCallArgs", "pop", "OrderedSubject", "arguments", "_orderedNext", "OrderedBehaviorSubject", "_currentValue", "InternalActions", "ngOnDestroy", "Actions", "internalActions$", "internalExecutionStrategy", "sharedInternalActions$", "observer", "childSubscription", "add", "compose", "funcs", "curr", "nextArgs", "ngxsErrorHandler", "internalErrorReporter", "subscribed", "Promise", "resolve", "then", "reportErrorSafely", "subscriber", "InternalErrorReporter", "_injector", "_error<PERSON><PERSON><PERSON>", "handleError", "_a", "Injector", "StateStream", "Plugin<PERSON>anager", "_parentManager", "_pluginHandlers", "plugins", "registerHandlers", "rootPlugins", "pluginHandlers", "getPluginHandlers", "handlers", "plugin", "handle", "bind", "InternalDispatchedActionResults", "InternalDispatcher", "_actions", "_actionResults", "_pluginManager", "_stateStream", "_ngxsExecutionStrategy", "_internalErrorReporter", "dispatch", "actionOrActions", "dispatchByEvents", "dispatchSingle", "prevState", "nextState", "nextAction", "actionResult$", "getActionResultStream", "createDispatchObservable", "deepFreeze", "freeze", "oIsFunction", "hasOwnProp", "prototype", "getOwnPropertyNames", "call", "isFrozen", "InternalStateOperations", "_dispatcher", "_config", "getRootStateOperations", "rootStateOperations", "getState", "setState", "newState", "developmentMode", "ensureStateAndActionsAreImmutable", "setStateToTheCurrentWithNew", "results", "stateOperations", "currentState", "root", "frozenValue", "simplePatch", "existingState", "StateContextFactory", "_internalStateOperations", "createStateContext", "mappedStore", "currentAppState", "patchState", "patchOperator", "setStateFromOperator", "setStateValue", "newValue", "newAppState", "stateOperator", "local", "stateNameRegex", "RegExp", "ensureStateNameIsValid", "test", "ensureStateNameIsUnique", "stateName", "state", "statesByName", "ensureStatesAreDecorated", "ensureStateClassIsInjectable", "jit_hasInjectableAnnotation", "aot_hasNgInjectableDef", "annotations", "__annotations__", "some", "annotation", "ngMetadataName", "InitState", "UpdateState", "addedStates", "NGXS_DEVELOPMENT_OPTIONS", "warnOnUnhandledActions", "NgxsUnhandledActionsLogger", "_ignoredActions", "Set", "ignoreActions", "ignore", "actionShouldBeIgnored", "NG_DEV_MODE", "StateFactory", "_parentFactory", "_stateContextFactory", "_initialState", "_actionsSubscription", "_states", "_statesByName", "_statePaths", "getRuntimeSelectorContext", "stateFactory", "resolveGetter", "statePaths", "getter", "localOptions", "globalSelectorOptions", "_cloneDefaults", "unsubscribe", "newStates", "addToStatesMap", "stateGraph", "sortedStates", "nameGraph", "bootstrappedStores", "addRuntimeInfoToMeta", "stateMap", "isInitialised", "instance", "hasBeenMountedAndBootstrapped", "addAndReturnDefaults", "classes", "mappedStores", "connectActionHandlers", "dispatched$", "invokeActions", "actionHasBeenHandled", "metadata", "actionMetas", "actionMeta", "stateContext", "cancelUncompleted", "e", "unhandledActions<PERSON><PERSON>ger", "statesMap", "unmountedState", "valueIsBootstrappedInInitialState", "createRootSelectorFactory", "selectorMetaData", "selectors", "memoizedSelectorFn", "argumentSelectorFunctions", "getRuntimeSelectorInfo", "selectFromRoot", "rootState", "argFn", "ex", "TypeError", "createMemoizedSelectorFn", "creationMetadata", "wrappedFn", "wrappedSelectorFn", "returnValue", "apply", "innerMemoizedFn", "memoizedFn", "setPrototypeOf", "localSelectorOptions", "selectorsToApply", "getSelectorsToApply", "selector", "getRootSelectorFactory", "canInjectContainerState", "Store", "_internalExecutionStrategy", "_stateFactory", "initialStateValue", "_selectableStateStream", "bufferSize", "refCount", "initStateStream", "select", "selectorFn", "getStoreBoundSelectorFn", "err", "selectOnce", "selectSnapshot", "snapshot", "reset", "makeSelectorFn", "runtimeContext", "storeIsEmpty", "defaultStateNotEmpty", "storeValues", "SelectFactory", "store", "LifecycleStateManager", "_store", "_bootstrapper", "_destroy$", "ngxsBootstrap", "_invokeInitOnStates", "appBootstrapped$", "appBootstrapped", "_invokeBootstrapOnStates", "ngxsOnChanges", "change", "ngxsOnInit", "_getStateContext", "ngxsAfterBootstrap", "NgxsRootModule", "internalStateOperations", "_select", "lifecycleStateManager", "ɵmod", "ɵinj", "NgxsFeatureModule", "flattenedStates", "flattenStates", "total", "values", "concat", "NgxsModule", "forRoot", "ngModule", "providers", "ngxsTokenProviders", "forFeature", "provide", "multi", "useValue", "appBootstrapListenerFactory", "useExisting", "bootstrapper", "bootstrap", "Action", "isStaticMethod", "State", "inheritedStateClass", "getPrototypeOf", "optionsWithInheritance", "getStateOptions", "mutateMetaData", "inheritanceOptions", "params", "getName", "inheritedMeta", "DOLLAR_CHAR_CODE", "createSelectObservable", "createSelectorFn", "rawSelector", "removeDollarAtTheEnd", "propsArray", "lastCharIndex", "dollarAtTheEnd", "charCodeAt", "Select", "toString", "selectorId", "defineProperties", "writable", "enumerable", "configurable", "SELECTOR_OPTIONS_META_KEY", "selectorOptionsMetaAccessor", "getOptions", "defineOptions", "setupSelectorMetadata", "getExplicitSelectorOptions", "selectorMetaDataClone", "getLocalSelectorOptions", "explicitOptions", "SelectorOptions", "decorate", "methodName", "descriptor", "getOwnPropertyDescriptor", "ensureStoreMetadata", "getStoreMetadata", "ensureSelectorMetadata", "getSelectorMetadata", "createSelector", "projector", "Selector", "newDescriptor", "StateToken", "selectorMetadata", "NgxsDevelopmentModule", "ensureValidSelector", "noun", "prefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createModelSelector", "selectorMap", "selector<PERSON><PERSON><PERSON>", "ensureValidSelectorMap", "createPickSelector", "validKeys", "Boolean", "s", "props", "createPropertySelectors", "parentSelector", "cache", "Proxy", "_target", "ɵNgxsFeatureModule", "ɵNgxsRootModule"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,MAAT,EAAiBC,WAAjB,EAA8BC,UAA9B,EAA0CC,MAA1C,EAAkDC,cAAlD,EAAkEC,MAAlE,EAA0EC,QAA1E,EAAoFC,OAApF,EAA6FC,YAA7F,EAA2GC,QAA3G,EAAqHC,QAArH,EAA+HC,QAA/H,EAAyIC,sBAAzI,QAAuK,eAAvK;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,OAAT,EAAkBC,mBAAlB,EAAuCC,gBAAvC,EAAyDC,2BAAzD,EAAsFC,mBAAtF,QAAiH,uBAAjH;AACA,SAASC,gBAAT,QAAiC,iBAAjC;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,eAA9B,EAA+CC,EAA/C,EAAmDC,QAAnD,EAA6DC,UAA7D,EAAyEC,KAAzE,EAAgFC,IAAhF,EAAsFC,YAAtF,QAA0G,MAA1G;AACA,SAASC,MAAT,EAAiBC,GAAjB,EAAsBC,KAAtB,EAA6BC,WAA7B,EAA0CC,IAA1C,EAAgDC,UAAhD,EAA4DC,QAA5D,EAAsEC,cAAtE,EAAsFC,UAAtF,EAAkGC,SAAlG,EAA6GC,oBAA7G,EAAmIC,GAAnI,EAAwIC,SAAxI,EAAmJC,QAAnJ,QAAmK,gBAAnK;AACA,SAASC,eAAT,QAAgC,uBAAhC;AAEA;AACA;AACA;AACA;;AACA,SAASC,yBAAT,CAAmCC,MAAnC,EAA2C;AACvC,MAAIA,MAAM,CAACC,WAAP,IAAsBD,MAAM,CAACC,WAAP,CAAmBC,IAA7C,EAAmD;AAC/C,WAAOF,MAAM,CAACC,WAAP,CAAmBC,IAA1B;AACH,GAFD,MAGK;AACD,WAAOF,MAAM,CAACE,IAAd;AACH;AACJ;AACD;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuBC,OAAvB,EAAgC;AAC5B,QAAMC,KAAK,GAAGN,yBAAyB,CAACK,OAAD,CAAvC;AACA,SAAO,UAAUE,OAAV,EAAmB;AACtB,WAAOD,KAAK,KAAKN,yBAAyB,CAACO,OAAD,CAA1C;AACH,GAFD;AAGH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,QAAQ,GAAG,CAACC,GAAD,EAAMC,IAAN,EAAYC,GAAZ,KAAoB;AACjCF,EAAAA,GAAG,GAAGG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBJ,GAAlB,CAAN;AACA,QAAMK,KAAK,GAAGJ,IAAI,CAACI,KAAL,CAAW,GAAX,CAAd;AACA,QAAMC,SAAS,GAAGD,KAAK,CAACE,MAAN,GAAe,CAAjC;AACAF,EAAAA,KAAK,CAACG,MAAN,CAAa,CAACC,GAAD,EAAMC,IAAN,EAAYC,KAAZ,KAAsB;AAC/B,QAAIA,KAAK,KAAKL,SAAd,EAAyB;AACrBG,MAAAA,GAAG,CAACC,IAAD,CAAH,GAAYR,GAAZ;AACH,KAFD,MAGK;AACDO,MAAAA,GAAG,CAACC,IAAD,CAAH,GAAYE,KAAK,CAACC,OAAN,CAAcJ,GAAG,CAACC,IAAD,CAAjB,IAA2BD,GAAG,CAACC,IAAD,CAAH,CAAUI,KAAV,EAA3B,GAA+CX,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBK,GAAG,CAACC,IAAD,CAArB,CAA3D;AACH;;AACD,WAAOD,GAAG,IAAIA,GAAG,CAACC,IAAD,CAAjB;AACH,GARD,EAQGV,GARH;AASA,SAAOA,GAAP;AACH,CAdD;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMe,QAAQ,GAAG,CAACf,GAAD,EAAMC,IAAN,KAAeA,IAAI,CAACI,KAAL,CAAW,GAAX,EAAgBG,MAAhB,CAAuB,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,IAAIA,GAAG,CAACC,IAAD,CAAhD,EAAwDV,GAAxD,CAAhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgB,UAAU,GAAIC,IAAD,IAAU;AACzB,SAAOA,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,IAAoC,CAACL,KAAK,CAACC,OAAN,CAAcI,IAAd,CAA5C;AACH,CAFD;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,SAAS,GAAG,CAACC,IAAD,EAAO,GAAGC,OAAV,KAAsB;AACpC,MAAI,CAACA,OAAO,CAACb,MAAb,EACI,OAAOY,IAAP;AACJ,QAAME,MAAM,GAAGD,OAAO,CAACE,KAAR,EAAf;;AACA,MAAIN,UAAU,CAACG,IAAD,CAAV,IAAoBH,UAAU,CAACK,MAAD,CAAlC,EAA4C;AACxC,SAAK,MAAME,GAAX,IAAkBF,MAAlB,EAA0B;AACtB,UAAIL,UAAU,CAACK,MAAM,CAACE,GAAD,CAAP,CAAd,EAA6B;AACzB,YAAI,CAACJ,IAAI,CAACI,GAAD,CAAT,EACIpB,MAAM,CAACC,MAAP,CAAce,IAAd,EAAoB;AAAE,WAACI,GAAD,GAAO;AAAT,SAApB;AACJL,QAAAA,SAAS,CAACC,IAAI,CAACI,GAAD,CAAL,EAAYF,MAAM,CAACE,GAAD,CAAlB,CAAT;AACH,OAJD,MAKK;AACDpB,QAAAA,MAAM,CAACC,MAAP,CAAce,IAAd,EAAoB;AAAE,WAACI,GAAD,GAAOF,MAAM,CAACE,GAAD;AAAf,SAApB;AACH;AACJ;AACJ;;AACD,SAAOL,SAAS,CAACC,IAAD,EAAO,GAAGC,OAAV,CAAhB;AACH,CAjBD;;AAmBA,SAASI,mBAAT,CAA6BC,IAA7B,EAAmC;AAC/B,QAAM,IAAIC,KAAJ,CAAW,GAAED,IAAK,0EAAlB,CAAN;AACH;;AACD,SAASE,2BAAT,GAAuC;AACnC,QAAM,IAAID,KAAJ,CAAW,yCAAX,CAAN;AACH;;AACD,SAASE,qBAAT,CAA+BC,OAA/B,EAAwCC,OAAxC,EAAiDC,OAAjD,EAA0D;AACtD,QAAM,IAAIL,KAAJ,CAAW,eAAcG,OAAQ,UAASC,OAAQ,sBAAqBC,OAAQ,GAA/E,CAAN;AACH;;AACD,SAASC,wBAAT,CAAkCP,IAAlC,EAAwC;AACpC,QAAM,IAAIC,KAAJ,CAAW,0DAAyDD,IAAK,UAAzE,CAAN;AACH;;AACD,SAASQ,yBAAT,GAAqC;AACjC,QAAM,IAAIP,KAAJ,CAAU,yDAAV,CAAN;AACH;;AACD,SAASQ,2BAAT,GAAuC;AACnC,QAAM,IAAIR,KAAJ,CAAU,iCAAV,CAAN;AACH;;AACD,SAASS,qBAAT,GAAiC;AAC7B,SAAQ,gHACJ,wFADI,GAEJ,8EAFJ;AAGH;;AACD,SAASC,sCAAT,CAAgDX,IAAhD,EAAsD;AAClD,SAAQ,IAAGA,IAAK,mFAAhB;AACH;;AACD,SAASY,mCAAT,GAA+C;AAC3C,QAAM,IAAIX,KAAJ,CAAU,+CAAV,CAAN;AACH;;AACD,SAASY,uBAAT,GAAmC;AAC/B,QAAM,IAAIZ,KAAJ,CAAU,mCAAV,CAAN;AACH;;AACD,SAASa,2BAAT,GAAuC;AACnC,QAAM,IAAIb,KAAJ,CAAU,uCAAV,CAAN;AACH;;AAED,MAAMc,wCAAN,CAA+C;AAC3C/C,EAAAA,WAAW,CAACgD,OAAD,EAAUC,WAAV,EAAuB;AAC9B,SAAKD,OAAL,GAAeA,OAAf;AACA,SAAKC,WAAL,GAAmBA,WAAnB,CAF8B,CAG9B;AACA;;AACA,QAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/CC,MAAAA,qBAAqB,CAACH,OAAD,CAArB;AACH;AACJ;;AACDI,EAAAA,KAAK,CAACC,IAAD,EAAO;AACR,QAAIhF,gBAAgB,CAAC,KAAK4E,WAAN,CAApB,EAAwC;AACpC,aAAO,KAAKK,gBAAL,CAAsBD,IAAtB,CAAP;AACH;;AACD,WAAO,KAAKE,iBAAL,CAAuBF,IAAvB,CAAP;AACH;;AACDG,EAAAA,KAAK,CAACH,IAAD,EAAO;AACR,WAAO,KAAKC,gBAAL,CAAsBD,IAAtB,CAAP;AACH;;AACDC,EAAAA,gBAAgB,CAACD,IAAD,EAAO;AACnB,QAAInG,MAAM,CAACuG,eAAP,EAAJ,EAA8B;AAC1B,aAAOJ,IAAI,EAAX;AACH;;AACD,WAAO,KAAKL,OAAL,CAAaU,GAAb,CAAiBL,IAAjB,CAAP;AACH;;AACDE,EAAAA,iBAAiB,CAACF,IAAD,EAAO;AACpB,QAAInG,MAAM,CAACuG,eAAP,EAAJ,EAA8B;AAC1B,aAAO,KAAKT,OAAL,CAAaO,iBAAb,CAA+BF,IAA/B,CAAP;AACH;;AACD,WAAOA,IAAI,EAAX;AACH;;AA9B0C;AAgC/C;;;AAAmBN,wCAAwC,CAACY,IAAzC;AAAA,mBAAsIZ,wCAAtI,EAA4H9F,EAA5H,UAAgMA,EAAE,CAACC,MAAnM,GAA4HD,EAA5H,UAAsNE,WAAtN;AAAA;AACnB;;;AAAmB4F,wCAAwC,CAACa,KAAzC,kBAD4H3G,EAC5H;AAAA,SAA0I8F,wCAA1I;AAAA,WAA0IA,wCAA1I;AAAA,cAAgM;AAAhM;;AACnB;AAAA,qDAF+I9F,EAE/I,mBAA4F8F,wCAA5F,EAAkJ,CAAC;AACvI9C,IAAAA,IAAI,EAAE7C,UADiI;AAEvIyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFiI,GAAD,CAAlJ,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEhD,EAAE,CAACC;AAAX,KAAD,EAAsB;AAAE+C,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACnF/D,QAAAA,IAAI,EAAE5C,MAD6E;AAEnFwG,QAAAA,IAAI,EAAE,CAAC1G,WAAD;AAF6E,OAAD;AAA/B,KAAtB,CAAP;AAGlB,GANxB;AAAA,K,CAOA;AACA;;;AACA,SAASgG,qBAAT,CAA+Bc,MAA/B,EAAuC;AACnC;AACA;AACA;AACA,MAAIA,MAAM,YAAY/G,MAAtB,EAA8B;AAC1B;AACH;;AACDgH,EAAAA,OAAO,CAACC,IAAR,CAAazB,qBAAqB,EAAlC;AACH;;AAED,MAAM0B,YAAY,GAAG,IAAI9G,cAAJ,CAAmB,cAAnB,CAArB;AACA,MAAM+G,gBAAgB,GAAG,IAAI/G,cAAJ,CAAmB,kBAAnB,CAAzB;AACA,MAAMgH,mBAAmB,GAAG,IAAIhH,cAAJ,CAAmB,qBAAnB,CAA5B;AACA,MAAMiH,YAAY,GAAG,IAAIjH,cAAJ,CAAmB,cAAnB,CAArB;AACA,MAAMkH,QAAQ,GAAG,WAAjB;AACA,MAAMC,gBAAgB,GAAG,mBAAzB;AACA,MAAMC,iBAAiB,GAAG,oBAA1B;AACA;AACA;AACA;;AACA,MAAMC,UAAN,CAAiB;AACb3E,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACA;AACA;AACA;AACQ,SAAK4E,aAAL,GAAqB,EAArB;AACA;AACR;AACA;;AACQ,SAAKC,eAAL,GAAuB;AACnBC,MAAAA,oBAAoB,EAAE,IADH;AAEnBC,MAAAA,cAAc,EAAE,IAFG,CAEE;;AAFF,KAAvB;AAIA,SAAKC,aAAL,GAAqB;AACjBC,MAAAA,2BAA2B,EAAE;AADZ,KAArB;AAGA,SAAKC,iBAAL,GAAyBnC,wCAAzB;AACH;;AApBY;AAsBjB;;;AAAmB4B,UAAU,CAAChB,IAAX;AAAA,mBAAwGgB,UAAxG;AAAA;AACnB;;;AAAmBA,UAAU,CAACf,KAAX,kBAtD4H3G,EAsD5H;AAAA,SAA4G0H,UAA5G;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA,WAAyJQ,OAAD,IAAa1D,SAAS,CAAC,IAAIkD,UAAJ,EAAD,EAAmBQ,OAAnB,CAA9K,EAtD4HlI,EAsD5H,UAA2NmH,YAA3N;AAAA;;AAAA;AAAA;AAAA,cAAoI;AAApI;;AACnB;AAAA,qDAvD+InH,EAuD/I,mBAA4F0H,UAA5F,EAAoH,CAAC;AACzG1E,IAAAA,IAAI,EAAE7C,UADmG;AAEzGyG,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,UAAU,EAAE,MADb;AAECsB,MAAAA,UAAU,EAAGD,OAAD,IAAa1D,SAAS,CAAC,IAAIkD,UAAJ,EAAD,EAAmBQ,OAAnB,CAFnC;AAGCE,MAAAA,IAAI,EAAE,CAACjB,YAAD;AAHP,KAAD;AAFmG,GAAD,CAApH,EAO4B,YAAY;AAAE,WAAO,EAAP;AAAY,GAPtD;AAAA;AAQA;AACA;AACA;AACA;;;AACA,MAAMkB,gBAAN,CAAuB;AACnBtF,EAAAA,WAAW,CAACuF,aAAD,EAAgBC,YAAhB,EAA8BC,WAA9B,EAA2C;AAClD,SAAKF,aAAL,GAAqBA,aAArB;AACA,SAAKC,YAAL,GAAoBA,YAApB;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACH;;AALkB;;AAQvB,MAAMC,yBAAN,CAAgC;AAC5BtC,EAAAA,KAAK,CAACC,IAAD,EAAO;AACR,WAAOA,IAAI,EAAX;AACH;;AACDG,EAAAA,KAAK,CAACH,IAAD,EAAO;AACR,WAAOA,IAAI,EAAX;AACH;;AAN2B;AAQhC;;;AAAmBqC,yBAAyB,CAAC/B,IAA1B;AAAA,mBAAuH+B,yBAAvH;AAAA;AACnB;;;AAAmBA,yBAAyB,CAAC9B,KAA1B,kBApF4H3G,EAoF5H;AAAA,SAA2HyI,yBAA3H;AAAA,WAA2HA,yBAA3H;AAAA,cAAkK;AAAlK;;AACnB;AAAA,qDArF+IzI,EAqF/I,mBAA4FyI,yBAA5F,EAAmI,CAAC;AACxHzF,IAAAA,IAAI,EAAE7C,UADkH;AAExHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFkH,GAAD,CAAnI;AAAA;AAKA;AACA;AACA;;;AACA,MAAM6B,qCAAqC,GAAG,IAAIrI,cAAJ,CAAmB,uCAAnB,CAA9C;AACA;AACA;AACA;;AACA,MAAMsI,uBAAuB,GAAG,IAAItI,cAAJ,CAAmB,yBAAnB,EAA8C;AAC1EwG,EAAAA,UAAU,EAAE,MAD8D;AAE1E+B,EAAAA,OAAO,EAAE,MAAM;AACX,UAAMC,QAAQ,GAAGvI,MAAM,CAACC,QAAD,CAAvB;AACA,UAAM0H,iBAAiB,GAAGY,QAAQ,CAACC,GAAT,CAAaJ,qCAAb,CAA1B;AACA,WAAOT,iBAAiB,GAClBY,QAAQ,CAACC,GAAT,CAAab,iBAAb,CADkB,GAElBY,QAAQ,CAACC,GAAT,CAAa,OAAOtI,OAAO,CAACuI,IAAf,KAAwB,WAAxB,GACTjD,wCADS,GAET2C,yBAFJ,CAFN;AAKH;AAVyE,CAA9C,CAAhC;AAaA;AACA;AACA;AACA;AACA;;AACA,SAASO,qBAAT,CAA+BC,MAA/B,EAAuC;AACnC,MAAI,CAACA,MAAM,CAACC,cAAP,CAAsB3B,QAAtB,CAAL,EAAsC;AAClC,UAAM4B,eAAe,GAAG;AACpBpE,MAAAA,IAAI,EAAE,IADc;AAEpBqE,MAAAA,OAAO,EAAE,EAFW;AAGpBC,MAAAA,QAAQ,EAAE,EAHU;AAIpBC,MAAAA,IAAI,EAAE,IAJc;;AAKpBC,MAAAA,gBAAgB,CAACC,OAAD,EAAU;AACtB,eAAOA,OAAO,CAACC,cAAR,CAAuBN,eAAe,CAACpE,IAAvC,CAAP;AACH,OAPmB;;AAQpB2E,MAAAA,QAAQ,EAAE;AARU,KAAxB;AAUAjG,IAAAA,MAAM,CAACkG,cAAP,CAAsBV,MAAtB,EAA8B1B,QAA9B,EAAwC;AAAEqC,MAAAA,KAAK,EAAET;AAAT,KAAxC;AACH;;AACD,SAAOU,kBAAkB,CAACZ,MAAD,CAAzB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASY,kBAAT,CAA4BZ,MAA5B,EAAoC;AAChC,SAAOA,MAAM,CAAC1B,QAAD,CAAb;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASuC,wBAAT,CAAkCb,MAAlC,EAA0C;AACtC,MAAI,CAACA,MAAM,CAACC,cAAP,CAAsBzB,iBAAtB,CAAL,EAA+C;AAC3C,UAAM0B,eAAe,GAAG;AACpBI,MAAAA,gBAAgB,EAAE,IADE;AAEpBQ,MAAAA,UAAU,EAAE,IAFQ;AAGpBC,MAAAA,cAAc,EAAE,IAHI;AAIpBC,MAAAA,YAAY,EAAE,IAJM;AAKpBC,MAAAA,kBAAkB,EAAE,OAAO,EAAP;AALA,KAAxB;AAOAzG,IAAAA,MAAM,CAACkG,cAAP,CAAsBV,MAAtB,EAA8BxB,iBAA9B,EAAiD;AAAEmC,MAAAA,KAAK,EAAET;AAAT,KAAjD;AACH;;AACD,SAAOgB,qBAAqB,CAAClB,MAAD,CAA5B;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASkB,qBAAT,CAA+BlB,MAA/B,EAAuC;AACnC,SAAOA,MAAM,CAACxB,iBAAD,CAAb;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS2C,mBAAT,CAA6BC,KAA7B,EAAoC;AAChC,QAAMC,WAAW,GAAGD,KAAK,CAACjG,KAAN,EAApB;AACA,SAAOd,GAAG,IAAIgH,WAAW,CAACxG,MAAZ,CAAmB,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,IAAIA,GAAG,CAACC,IAAD,CAA5C,EAAoDV,GAApD,CAAd;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASiH,cAAT,CAAwBF,KAAxB,EAA+B;AAC3B,QAAMG,QAAQ,GAAGH,KAAjB;AACA,MAAII,GAAG,GAAG,WAAWD,QAAQ,CAAC,CAAD,CAA7B;AACA,MAAIE,CAAC,GAAG,CAAR;AACA,QAAMC,CAAC,GAAGH,QAAQ,CAAC3G,MAAnB;AACA,MAAI+G,IAAI,GAAGH,GAAX;;AACA,SAAO,EAAEC,CAAF,GAAMC,CAAb,EAAgB;AACZC,IAAAA,IAAI,GAAGA,IAAI,GAAG,MAAP,IAAiBH,GAAG,GAAGA,GAAG,GAAG,GAAN,GAAYD,QAAQ,CAACE,CAAD,CAA3C,CAAP;AACH;;AACD,QAAMG,EAAE,GAAG,IAAIC,QAAJ,CAAa,OAAb,EAAsB,YAAYF,IAAZ,GAAmB,GAAzC,CAAX;AACA,SAAOC,EAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,UAAT,CAAoBV,KAApB,EAA2BW,MAA3B,EAAmC;AAC/B,MAAIA,MAAM,IAAIA,MAAM,CAACjD,aAAjB,IAAkCiD,MAAM,CAACjD,aAAP,CAAqBC,2BAA3D,EAAwF;AACpF,WAAOoC,mBAAmB,CAACC,KAAD,CAA1B;AACH,GAFD,MAGK;AACD,WAAOE,cAAc,CAACF,KAAD,CAArB;AACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASY,UAAT,CAAoBC,YAApB,EAAkC;AAC9B,QAAMC,QAAQ,GAAIC,UAAD,IAAgB;AAC7B,UAAMC,IAAI,GAAGH,YAAY,CAACI,IAAb,CAAkBC,CAAC,IAAIA,CAAC,KAAKH,UAA7B,CAAb,CAD6B,CAE7B;AACA;;AACA,QAAI,CAAC,OAAOnF,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD,CAACoF,IAAxD,EAA8D;AAC1D,YAAM,IAAIrG,KAAJ,CAAW,0BAAyBoG,UAAW,sDAA/C,CAAN;AACH;;AACD,WAAOC,IAAI,CAAC9D,QAAD,CAAJ,CAAexC,IAAtB;AACH,GARD;;AASA,SAAOmG,YAAY,CAACpH,MAAb,CAAoB,CAAC0H,MAAD,EAASJ,UAAT,KAAwB;AAC/C,UAAM;AAAErG,MAAAA,IAAF;AAAQ2E,MAAAA;AAAR,QAAqB0B,UAAU,CAAC7D,QAAD,CAArC;AACAiE,IAAAA,MAAM,CAACzG,IAAD,CAAN,GAAe,CAAC2E,QAAQ,IAAI,EAAb,EAAiB3H,GAAjB,CAAqBoJ,QAArB,CAAf;AACA,WAAOK,MAAP;AACH,GAJM,EAIJ,EAJI,CAAP;AAKH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AACzB,SAAOA,MAAM,CAAC5H,MAAP,CAAc,CAAC0H,MAAD,EAASJ,UAAT,KAAwB;AACzC,UAAMC,IAAI,GAAGD,UAAU,CAAC7D,QAAD,CAAvB;AACAiE,IAAAA,MAAM,CAACH,IAAI,CAACtG,IAAN,CAAN,GAAoBqG,UAApB;AACA,WAAOI,MAAP;AACH,GAJM,EAIJ,EAJI,CAAP;AAKH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,kBAAT,CAA4BrI,GAA5B,EAAiCsI,MAAM,GAAG,EAA1C,EAA8C;AAC1C,QAAMC,KAAK,GAAG,CAACC,KAAD,EAAQC,SAAR,KAAsB;AAChC,SAAK,MAAMlH,GAAX,IAAkBiH,KAAlB,EAAyB;AACrB,UAAIA,KAAK,CAAC5C,cAAN,CAAqBrE,GAArB,KAA6BiH,KAAK,CAACjH,GAAD,CAAL,CAAWmH,OAAX,CAAmBD,SAAnB,KAAiC,CAAlE,EAAqE;AACjE,cAAME,MAAM,GAAGJ,KAAK,CAACC,KAAD,EAAQjH,GAAR,CAApB;AACA,eAAOoH,MAAM,KAAK,IAAX,GAAmB,GAAEA,MAAO,IAAGpH,GAAI,EAAnC,GAAuCA,GAA9C;AACH;AACJ;;AACD,WAAO,IAAP;AACH,GARD;;AASA,OAAK,MAAMA,GAAX,IAAkBvB,GAAlB,EAAuB;AACnB,QAAIA,GAAG,CAAC4F,cAAJ,CAAmBrE,GAAnB,CAAJ,EAA6B;AACzB,YAAMoH,MAAM,GAAGJ,KAAK,CAACvI,GAAD,EAAMuB,GAAN,CAApB;AACA+G,MAAAA,MAAM,CAAC/G,GAAD,CAAN,GAAcoH,MAAM,GAAI,GAAEA,MAAO,IAAGpH,GAAI,EAApB,GAAwBA,GAA5C;AACH;AACJ;;AACD,SAAO+G,MAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASM,eAAT,CAAyBC,KAAzB,EAAgC;AAC5B,QAAMC,MAAM,GAAG,EAAf;AACA,QAAMC,OAAO,GAAG,EAAhB;;AACA,QAAMR,KAAK,GAAG,CAAC9G,IAAD,EAAOuH,SAAS,GAAG,EAAnB,KAA0B;AACpC,QAAI,CAACpI,KAAK,CAACC,OAAN,CAAcmI,SAAd,CAAL,EAA+B;AAC3BA,MAAAA,SAAS,GAAG,EAAZ;AACH;;AACDA,IAAAA,SAAS,CAACC,IAAV,CAAexH,IAAf;AACAsH,IAAAA,OAAO,CAACtH,IAAD,CAAP,GAAgB,IAAhB;AACAoH,IAAAA,KAAK,CAACpH,IAAD,CAAL,CAAYyH,OAAZ,CAAqBC,GAAD,IAAS;AACzB;AACA;AACA,UAAI,CAAC,OAAOxG,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDqG,SAAS,CAACN,OAAV,CAAkBS,GAAlB,KAA0B,CAAjF,EAAoF;AAChF,cAAM,IAAIzH,KAAJ,CAAW,wBAAuByH,GAAI,qBAAoB1H,IAAK,MAAKuH,SAAS,CAACI,IAAV,CAAe,MAAf,CAAuB,EAA3F,CAAN;AACH;;AACD,UAAIL,OAAO,CAACI,GAAD,CAAX,EAAkB;AACd;AACH;;AACDZ,MAAAA,KAAK,CAACY,GAAD,EAAMH,SAAS,CAAClI,KAAV,CAAgB,CAAhB,CAAN,CAAL;AACH,KAVD;;AAWA,QAAIgI,MAAM,CAACJ,OAAP,CAAejH,IAAf,IAAuB,CAA3B,EAA8B;AAC1BqH,MAAAA,MAAM,CAACG,IAAP,CAAYxH,IAAZ;AACH;AACJ,GApBD;;AAqBAtB,EAAAA,MAAM,CAACkJ,IAAP,CAAYR,KAAZ,EAAmBK,OAAnB,CAA2BI,CAAC,IAAIf,KAAK,CAACe,CAAD,CAArC;AACA,SAAOR,MAAM,CAACS,OAAP,EAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,QAAT,CAAkBxJ,GAAlB,EAAuB;AACnB,SAAQ,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,KAAK,IAApC,IAA6C,OAAOA,GAAP,KAAe,UAAnE;AACH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASyJ,QAAT,CAAkB,GAAGC,YAArB,EAAmC;AAC/B,SAAOC,gBAAgB,CAACD,YAAD,CAAvB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASE,kBAAT,CAA4B,GAAGF,YAA/B,EAA6C;AACzC,SAAOC,gBAAgB,CAACD,YAAD,EAAe,CAAC;AAAa;AAAd,GAAf,CAAvB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASG,kBAAT,CAA4B,GAAGH,YAA/B,EAA6C;AACzC,SAAOC,gBAAgB,CAACD,YAAD,EAAe,CAAC;AAAa;AAAd,GAAf,CAAvB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASI,gBAAT,CAA0B,GAAGJ,YAA7B,EAA2C;AACvC,SAAOC,gBAAgB,CAACD,YAAD,EAAe,CAAC;AAAW;AAAZ,GAAf,CAAvB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASK,iBAAT,CAA2B,GAAGL,YAA9B,EAA4C;AACxC,QAAMM,eAAe,GAAG,CACpB;AAAa;AADO,IAEpB;AAAW;AAFS,IAGpB;AAAU;AAHU,GAAxB;AAKA,SAAOL,gBAAgB,CAACD,YAAD,EAAeM,eAAf,EAAgCC,eAAhC,CAAvB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,eAAT,CAAyB,GAAGR,YAA5B,EAA0C;AACtC,SAAOC,gBAAgB,CAACD,YAAD,EAAe,CAAC;AAAU;AAAX,GAAf,CAAvB;AACH;;AACD,SAASC,gBAAT,CAA0BD,YAA1B,EAAwCS,QAAxC,EACA;AACA;AACA;AACAC,WAAW,GAAGC,SAJd,EAIyB;AACrB,QAAMC,UAAU,GAAGC,2BAA2B,CAACb,YAAD,CAA9C;AACA,QAAMc,gBAAgB,GAAGL,QAAQ,IAAIM,wBAAwB,CAACN,QAAD,CAA7D;AACA,SAAO,UAAUO,CAAV,EAAa;AAChB,WAAOA,CAAC,CAACC,IAAF,CAAOC,YAAY,CAACN,UAAD,EAAaE,gBAAb,CAAnB,EAAmDJ,WAAW,EAA9D,CAAP;AACH,GAFD;AAGH;;AACD,SAASQ,YAAT,CAAsBlB,YAAtB,EAAoCM,eAApC,EAAqD;AACjD,SAAOxL,MAAM,CAAEqM,GAAD,IAAS;AACnB,UAAMC,UAAU,GAAGvL,yBAAyB,CAACsL,GAAG,CAACrL,MAAL,CAA5C;AACA,UAAMuL,SAAS,GAAGrB,YAAY,CAACoB,UAAD,CAA9B;AACA,UAAME,WAAW,GAAGhB,eAAe,GAAGA,eAAe,CAACa,GAAG,CAACI,MAAL,CAAlB,GAAiC,IAApE;AACA,WAAOF,SAAS,IAAIC,WAApB;AACH,GALY,CAAb;AAMH;;AACD,SAASf,eAAT,GAA2B;AACvB,SAAOxL,GAAG,CAAC,CAAC;AAAEe,IAAAA,MAAF;AAAUyL,IAAAA,MAAV;AAAkBC,IAAAA;AAAlB,GAAD,KAA+B;AACtC,WAAO;AACH1L,MAAAA,MADG;AAEH0I,MAAAA,MAAM,EAAE;AACJiD,QAAAA,UAAU,EAAE;AAAa;AAAb,YAAkCF,MAD1C;AAEJG,QAAAA,QAAQ,EAAE;AAAW;AAAX,YAA8BH,MAFpC;AAGJC,QAAAA;AAHI;AAFL,KAAP;AAQH,GATS,CAAV;AAUH;;AACD,SAASb,SAAT,GAAqB;AACjB,SAAO5L,GAAG,CAAEoM,GAAD,IAASA,GAAG,CAACrL,MAAd,CAAV;AACH;;AACD,SAAS+K,2BAAT,CAAqCc,KAArC,EAA4C;AACxC,SAAOA,KAAK,CAAC7K,MAAN,CAAa,CAAC8K,SAAD,EAAYC,KAAZ,KAAsB;AACtCD,IAAAA,SAAS,CAAC/L,yBAAyB,CAACgM,KAAD,CAA1B,CAAT,GAA8C,IAA9C;AACA,WAAOD,SAAP;AACH,GAHM,EAGJ,EAHI,CAAP;AAIH;;AACD,SAASb,wBAAT,CAAkCN,QAAlC,EAA4C;AACxC,SAAOA,QAAQ,CAAC3J,MAAT,CAAgB,CAAC8K,SAAD,EAAYL,MAAZ,KAAuB;AAC1CK,IAAAA,SAAS,CAACL,MAAD,CAAT,GAAoB,IAApB;AACA,WAAOK,SAAP;AACH,GAHM,EAGJ,EAHI,CAAP;AAIH;AAED;AACA;AACA;AACA;;;AACA,SAASE,SAAT,CAAmBC,qBAAnB,EAA0C;AACtC,SAAQpK,MAAD,IAAY;AACf,WAAO,IAAItD,UAAJ,CAAgB2N,IAAD,IAAU;AAC5B,aAAOrK,MAAM,CAACsK,SAAP,CAAiB;AACpBC,QAAAA,IAAI,CAACtF,KAAD,EAAQ;AACRmF,UAAAA,qBAAqB,CAACxI,KAAtB,CAA4B,MAAMyI,IAAI,CAACE,IAAL,CAAUtF,KAAV,CAAlC;AACH,SAHmB;;AAIpB4E,QAAAA,KAAK,CAACA,KAAD,EAAQ;AACTO,UAAAA,qBAAqB,CAACxI,KAAtB,CAA4B,MAAMyI,IAAI,CAACR,KAAL,CAAWA,KAAX,CAAlC;AACH,SANmB;;AAOpBW,QAAAA,QAAQ,GAAG;AACPJ,UAAAA,qBAAqB,CAACxI,KAAtB,CAA4B,MAAMyI,IAAI,CAACG,QAAL,EAAlC;AACH;;AATmB,OAAjB,CAAP;AAWH,KAZM,CAAP;AAaH,GAdD;AAeH;;AAED,MAAMC,6BAAN,CAAoC;AAChCrM,EAAAA,WAAW,CAACsM,kBAAD,EAAqB;AAC5B,SAAKA,kBAAL,GAA0BA,kBAA1B;AACH;;AACDlJ,EAAAA,KAAK,CAACC,IAAD,EAAO;AACR,WAAO,KAAKiJ,kBAAL,CAAwBlJ,KAAxB,CAA8BC,IAA9B,CAAP;AACH;;AACDG,EAAAA,KAAK,CAACH,IAAD,EAAO;AACR,WAAO,KAAKiJ,kBAAL,CAAwB9I,KAAxB,CAA8BH,IAA9B,CAAP;AACH;;AAT+B;AAWpC;;;AAAmBgJ,6BAA6B,CAAC1I,IAA9B;AAAA,mBAA2H0I,6BAA3H,EA1e4HpP,EA0e5H,UAA0K2I,uBAA1K;AAAA;AACnB;;;AAAmByG,6BAA6B,CAACzI,KAA9B,kBA3e4H3G,EA2e5H;AAAA,SAA+HoP,6BAA/H;AAAA,WAA+HA,6BAA/H;AAAA,cAA0K;AAA1K;;AACnB;AAAA,qDA5e+IpP,EA4e/I,mBAA4FoP,6BAA5F,EAAuI,CAAC;AAC5HpM,IAAAA,IAAI,EAAE7C,UADsH;AAE5HyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFsH,GAAD,CAAvI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9D/D,QAAAA,IAAI,EAAE5C,MADwD;AAE9DwG,QAAAA,IAAI,EAAE,CAAC+B,uBAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS2G,qBAAT,CAA+BC,SAA/B,EAA0C;AACtC,QAAMC,UAAU,GAAG,EAAnB;AACA,MAAIC,eAAe,GAAG,KAAtB;AACA,SAAO,SAASC,aAAT,CAAuB,GAAG9I,IAA1B,EAAgC;AACnC,QAAI6I,eAAJ,EAAqB;AACjBD,MAAAA,UAAU,CAACG,OAAX,CAAmB/I,IAAnB;AACA;AACH;;AACD6I,IAAAA,eAAe,GAAG,IAAlB;AACAF,IAAAA,SAAS,CAAC,GAAG3I,IAAJ,CAAT;;AACA,WAAO4I,UAAU,CAAC3L,MAAX,GAAoB,CAA3B,EAA8B;AAC1B,YAAM+L,YAAY,GAAGJ,UAAU,CAACK,GAAX,EAArB;AACAD,MAAAA,YAAY,IAAIL,SAAS,CAAC,GAAGK,YAAJ,CAAzB;AACH;;AACDH,IAAAA,eAAe,GAAG,KAAlB;AACH,GAZD;AAaH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMK,cAAN,SAA6BxO,OAA7B,CAAqC;AACjCyB,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGgN,SAAT;AACA,SAAKC,YAAL,GAAoBV,qBAAqB,CAAE1F,KAAD,IAAW,MAAMsF,IAAN,CAAWtF,KAAX,CAAZ,CAAzC;AACH;;AACDsF,EAAAA,IAAI,CAACtF,KAAD,EAAQ;AACR,SAAKoG,YAAL,CAAkBpG,KAAlB;AACH;;AAPgC;AASrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqG,sBAAN,SAAqC1O,eAArC,CAAqD;AACjDwB,EAAAA,WAAW,CAAC6G,KAAD,EAAQ;AACf,UAAMA,KAAN;AACA,SAAKoG,YAAL,GAAoBV,qBAAqB,CAAE1F,KAAD,IAAW,MAAMsF,IAAN,CAAWtF,KAAX,CAAZ,CAAzC;AACA,SAAKsG,aAAL,GAAqBtG,KAArB;AACH;;AACDvF,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAK6L,aAAZ;AACH;;AACDhB,EAAAA,IAAI,CAACtF,KAAD,EAAQ;AACR,SAAKsG,aAAL,GAAqBtG,KAArB;;AACA,SAAKoG,YAAL,CAAkBpG,KAAlB;AACH;;AAZgD;AAerD;AACA;AACA;;;AACA,MAAMuG,eAAN,SAA8BL,cAA9B,CAA6C;AACzCM,EAAAA,WAAW,GAAG;AACV,SAAKjB,QAAL;AACH;;AAHwC;AAK7C;;;AAAmBgB,eAAe,CAACzJ,IAAhB;AAAA;AAAA;AAAA,4EAvlB4H1G,EAulB5H,uBAA6GmQ,eAA7G,SAA6GA,eAA7G;AAAA;AAAA;AACnB;;;AAAmBA,eAAe,CAACxJ,KAAhB,kBAxlB4H3G,EAwlB5H;AAAA,SAAiHmQ,eAAjH;AAAA,WAAiHA,eAAjH;AAAA,cAA8I;AAA9I;;AACnB;AAAA,qDAzlB+InQ,EAylB/I,mBAA4FmQ,eAA5F,EAAyH,CAAC;AAC9GnN,IAAAA,IAAI,EAAE7C,UADwG;AAE9GyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAIA;AACA;AACA;AACA;AACA;;;AACA,MAAMwJ,OAAN,SAAsBhP,UAAtB,CAAiC;AAC7B0B,EAAAA,WAAW,CAACuN,gBAAD,EAAmBC,yBAAnB,EAA8C;AACrD,UAAMC,sBAAsB,GAAGF,gBAAgB,CAACrC,IAAjB,CAAsBa,SAAS,CAACyB,yBAAD,CAA/B,EAC/B;AACA;AACA;AACA;AACAvO,IAAAA,KAAK,EAL0B,CAA/B;AAMA,UAAMyO,QAAQ,IAAI;AACd,YAAMC,iBAAiB,GAAGF,sBAAsB,CAACvB,SAAvB,CAAiC;AACvDC,QAAAA,IAAI,EAAEf,GAAG,IAAIsC,QAAQ,CAACvB,IAAT,CAAcf,GAAd,CAD0C;AAEvDK,QAAAA,KAAK,EAAEA,KAAK,IAAIiC,QAAQ,CAACjC,KAAT,CAAeA,KAAf,CAFuC;AAGvDW,QAAAA,QAAQ,EAAE,MAAMsB,QAAQ,CAACtB,QAAT;AAHuC,OAAjC,CAA1B;AAKAsB,MAAAA,QAAQ,CAACE,GAAT,CAAaD,iBAAb;AACH,KAPD;AAQH;;AAhB4B;AAkBjC;;;AAAmBL,OAAO,CAAC3J,IAAR;AAAA,mBAAqG2J,OAArG,EApnB4HrQ,EAonB5H,UAA8HmQ,eAA9H,GApnB4HnQ,EAonB5H,UAA0JoP,6BAA1J;AAAA;AACnB;;;AAAmBiB,OAAO,CAAC1J,KAAR,kBArnB4H3G,EAqnB5H;AAAA,SAAyGqQ,OAAzG;AAAA,WAAyGA,OAAzG;AAAA,cAA8H;AAA9H;;AACnB;AAAA,qDAtnB+IrQ,EAsnB/I,mBAA4FqQ,OAA5F,EAAiH,CAAC;AACtGrN,IAAAA,IAAI,EAAE7C,UADgG;AAEtGyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFgG,GAAD,CAAjH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEmN;AAAR,KAAD,EAA4B;AAAEnN,MAAAA,IAAI,EAAEoM;AAAR,KAA5B,CAAP;AAA8E,GAHxH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwB,OAAO,GAAIC,KAAD,IAAW,CAAC,GAAGjK,IAAJ,KAAa;AACpC,QAAMkK,IAAI,GAAGD,KAAK,CAACjM,KAAN,EAAb;AACA,SAAOkM,IAAI,CAAC,GAAGlK,IAAJ,EAAU,CAAC,GAAGmK,QAAJ,KAAiBH,OAAO,CAACC,KAAD,CAAP,CAAe,GAAGE,QAAlB,CAA3B,CAAX;AACH,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,gBAAT,CAA0BC,qBAA1B,EAAiDlC,qBAAjD,EAAwE;AACpE,SAAQpK,MAAD,IAAY;AACf,QAAIuM,UAAU,GAAG,KAAjB;AACAvM,IAAAA,MAAM,CAACsK,SAAP,CAAiB;AACbT,MAAAA,KAAK,EAAEA,KAAK,IAAI;AACZ;AACA;AACA;AACAO,QAAAA,qBAAqB,CAAC5I,KAAtB,CAA4B,MAAMgL,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AAC3D,cAAI,CAACH,UAAL,EAAiB;AACbnC,YAAAA,qBAAqB,CAACxI,KAAtB,CAA4B,MAAM0K,qBAAqB,CAACK,iBAAtB,CAAwC9C,KAAxC,CAAlC;AACH;AACJ,SAJiC,CAAlC;AAKH;AAVY,KAAjB;AAYA,WAAO,IAAInN,UAAJ,CAAekQ,UAAU,IAAI;AAChCL,MAAAA,UAAU,GAAG,IAAb;AACA,aAAOvM,MAAM,CAACsJ,IAAP,CAAYa,SAAS,CAACC,qBAAD,CAArB,EAA8CE,SAA9C,CAAwDsC,UAAxD,CAAP;AACH,KAHM,CAAP;AAIH,GAlBD;AAmBH;;AACD,MAAMC,qBAAN,CAA4B;AACxBzO,EAAAA,WAAW,CAAC0O,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKC,aAAL,GAAqB,IAArB;AACH;;AACDJ,EAAAA,iBAAiB,CAAC9C,KAAD,EAAQ;AACrB,QAAI,KAAKkD,aAAL,KAAuB,IAA3B,EAAiC;AAC7B,WAAKA,aAAL,GAAqB,KAAKD,SAAL,CAAe3I,GAAf,CAAmBrI,YAAnB,CAArB;AACH,KAHoB,CAIrB;AACA;AACA;AACA;;;AACA,QAAI;AACA,WAAKiR,aAAL,CAAmBC,WAAnB,CAA+BnD,KAA/B;AACH,KAFD,CAGA,OAAOoD,EAAP,EAAW,CAAG;AACjB;;AAlBuB;AAoB5B;;;AAAmBJ,qBAAqB,CAAC9K,IAAtB;AAAA,mBAAmH8K,qBAAnH,EA1sB4HxR,EA0sB5H,UAA0JA,EAAE,CAAC6R,QAA7J;AAAA;AACnB;;;AAAmBL,qBAAqB,CAAC7K,KAAtB,kBA3sB4H3G,EA2sB5H;AAAA,SAAuHwR,qBAAvH;AAAA,WAAuHA,qBAAvH;AAAA,cAA0J;AAA1J;;AACnB;AAAA,qDA5sB+IxR,EA4sB/I,mBAA4FwR,qBAA5F,EAA+H,CAAC;AACpHxO,IAAAA,IAAI,EAAE7C,UAD8G;AAEpHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF8G,GAAD,CAA/H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEhD,EAAE,CAAC6R;AAAX,KAAD,CAAP;AAAiC,GAH3E;AAAA;AAKA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,SAA0B7B,sBAA1B,CAAiD;AAC7ClN,EAAAA,WAAW,GAAG;AACV,UAAM,EAAN;AACH;;AACDqN,EAAAA,WAAW,GAAG;AACV;AACA;AACA;AACA,SAAKjB,QAAL;AACH;;AAT4C;AAWjD;;;AAAmB2C,WAAW,CAACpL,IAAZ;AAAA,mBAAyGoL,WAAzG;AAAA;AACnB;;;AAAmBA,WAAW,CAACnL,KAAZ,kBAjuB4H3G,EAiuB5H;AAAA,SAA6G8R,WAA7G;AAAA,WAA6GA,WAA7G;AAAA,cAAsI;AAAtI;;AACnB;AAAA,qDAluB+I9R,EAkuB/I,mBAA4F8R,WAA5F,EAAqH,CAAC;AAC1G9O,IAAAA,IAAI,EAAE7C,UADoG;AAE1GyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFoG,GAAD,CAArH,EAG4B,YAAY;AAAE,WAAO,EAAP;AAAY,GAHtD;AAAA;;AAKA,MAAMkL,aAAN,CAAoB;AAChBhP,EAAAA,WAAW,CAACiP,cAAD,EAAiBC,eAAjB,EAAkC;AACzC,SAAKD,cAAL,GAAsBA,cAAtB;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,gBAAL;AACH;;AACc,MAAXC,WAAW,GAAG;AACd,WAAQ,KAAKJ,cAAL,IAAuB,KAAKA,cAAL,CAAoBE,OAA5C,IAAwD,KAAKA,OAApE;AACH;;AACDC,EAAAA,gBAAgB,GAAG;AACf,UAAME,cAAc,GAAG,KAAKC,iBAAL,EAAvB;AACA,SAAKF,WAAL,CAAiB7F,IAAjB,CAAsB,GAAG8F,cAAzB;AACH;;AACDC,EAAAA,iBAAiB,GAAG;AAChB,UAAMC,QAAQ,GAAG,KAAKN,eAAL,IAAwB,EAAzC;AACA,WAAOM,QAAQ,CAACxQ,GAAT,CAAcyQ,MAAD,IAAaA,MAAM,CAACC,MAAP,GAAgBD,MAAM,CAACC,MAAP,CAAcC,IAAd,CAAmBF,MAAnB,CAAhB,GAA6CA,MAAvE,CAAP;AACH;;AAjBe;AAmBpB;;;AAAmBT,aAAa,CAACrL,IAAd;AAAA,mBAA2GqL,aAA3G,EA1vB4H/R,EA0vB5H,UAA0I+R,aAA1I,OA1vB4H/R,EA0vB5H,UAAoMsH,YAApM;AAAA;AACnB;;;AAAmByK,aAAa,CAACpL,KAAd,kBA3vB4H3G,EA2vB5H;AAAA,SAA+G+R,aAA/G;AAAA,WAA+GA,aAA/G;AAAA;;AACnB;AAAA,qDA5vB+I/R,EA4vB/I,mBAA4F+R,aAA5F,EAAuH,CAAC;AAC5G/O,IAAAA,IAAI,EAAE7C;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAE+O,aAAR;AAAuBhL,MAAAA,UAAU,EAAE,CAAC;AAClE/D,QAAAA,IAAI,EAAEtC;AAD4D,OAAD,EAElE;AACCsC,QAAAA,IAAI,EAAErC;AADP,OAFkE;AAAnC,KAAD,EAI3B;AAAEqC,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClC/D,QAAAA,IAAI,EAAE5C,MAD4B;AAElCwG,QAAAA,IAAI,EAAE,CAACU,YAAD;AAF4B,OAAD,EAGlC;AACCtE,QAAAA,IAAI,EAAEtC;AADP,OAHkC;AAA/B,KAJ2B,CAAP;AASlB,GAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiS,+BAAN,SAA8CrR,OAA9C,CAAsD;AAEtD;;;AAAmBqR,+BAA+B,CAACjM,IAAhC;AAAA;AAAA;AAAA,4GAjxB4H1G,EAixB5H,uBAA6H2S,+BAA7H,SAA6HA,+BAA7H;AAAA;AAAA;AACnB;;;AAAmBA,+BAA+B,CAAChM,KAAhC,kBAlxB4H3G,EAkxB5H;AAAA,SAAiI2S,+BAAjI;AAAA,WAAiIA,+BAAjI;AAAA,cAA8K;AAA9K;;AACnB;AAAA,qDAnxB+I3S,EAmxB/I,mBAA4F2S,+BAA5F,EAAyI,CAAC;AAC9H3P,IAAAA,IAAI,EAAE7C,UADwH;AAE9HyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwH,GAAD,CAAzI;AAAA;;AAIA,MAAM+L,kBAAN,CAAyB;AACrB7P,EAAAA,WAAW,CAAC8P,QAAD,EAAWC,cAAX,EAA2BC,cAA3B,EAA2CC,YAA3C,EAAyDC,sBAAzD,EAAiFC,sBAAjF,EAAyG;AAChH,SAAKL,QAAL,GAAgBA,QAAhB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKC,YAAL,GAAoBA,YAApB;AACA,SAAKC,sBAAL,GAA8BA,sBAA9B;AACA,SAAKC,sBAAL,GAA8BA,sBAA9B;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,QAAQ,CAACC,eAAD,EAAkB;AACtB,UAAM5H,MAAM,GAAG,KAAKyH,sBAAL,CAA4B9M,KAA5B,CAAkC,MAAM,KAAKkN,gBAAL,CAAsBD,eAAtB,CAAxC,CAAf;;AACA,WAAO5H,MAAM,CAACyC,IAAP,CAAY+C,gBAAgB,CAAC,KAAKkC,sBAAN,EAA8B,KAAKD,sBAAnC,CAA5B,CAAP;AACH;;AACDI,EAAAA,gBAAgB,CAACD,eAAD,EAAkB;AAC9B,QAAIlP,KAAK,CAACC,OAAN,CAAciP,eAAd,CAAJ,EAAoC;AAChC,UAAIA,eAAe,CAACvP,MAAhB,KAA2B,CAA/B,EACI,OAAOrC,EAAE,CAAC,KAAKwR,YAAL,CAAkB3O,QAAlB,EAAD,CAAT;AACJ,aAAO5C,QAAQ,CAAC2R,eAAe,CAACrR,GAAhB,CAAoBe,MAAM,IAAI,KAAKwQ,cAAL,CAAoBxQ,MAApB,CAA9B,CAAD,CAAf;AACH,KAJD,MAKK;AACD,aAAO,KAAKwQ,cAAL,CAAoBF,eAApB,CAAP;AACH;AACJ;;AACDE,EAAAA,cAAc,CAACxQ,MAAD,EAAS;AACnB,QAAI,OAAOmD,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,YAAMjD,IAAI,GAAGH,yBAAyB,CAACC,MAAD,CAAtC;;AACA,UAAI,CAACE,IAAL,EAAW;AACP,cAAMwL,KAAK,GAAG,IAAIxJ,KAAJ,CAAW,6CAA4ClC,MAAM,CAACC,WAAP,CAAmBgC,IAAK,EAA/E,CAAd;AACA,eAAOrD,UAAU,CAAC8M,KAAD,CAAjB;AACH;AACJ;;AACD,UAAM+E,SAAS,GAAG,KAAKP,YAAL,CAAkB3O,QAAlB,EAAlB;;AACA,UAAM6N,OAAO,GAAG,KAAKa,cAAL,CAAoBb,OAApC;AACA,WAAOtB,OAAO,CAAC,CACX,GAAGsB,OADQ,EAEX,CAACsB,SAAD,EAAYC,UAAZ,KAA2B;AACvB,UAAID,SAAS,KAAKD,SAAlB,EAA6B;AACzB,aAAKP,YAAL,CAAkB9D,IAAlB,CAAuBsE,SAAvB;AACH;;AACD,YAAME,aAAa,GAAG,KAAKC,qBAAL,CAA2BF,UAA3B,CAAtB;AACAC,MAAAA,aAAa,CAACzE,SAAd,CAAwBd,GAAG,IAAI,KAAK0E,QAAL,CAAc3D,IAAd,CAAmBf,GAAnB,CAA/B;;AACA,WAAK0E,QAAL,CAAc3D,IAAd,CAAmB;AAAEpM,QAAAA,MAAM,EAAE2Q,UAAV;AAAsBlF,QAAAA,MAAM,EAAE;AAAa;;AAA3C,OAAnB;;AACA,aAAO,KAAKqF,wBAAL,CAA8BF,aAA9B,CAAP;AACH,KAVU,CAAD,CAAP,CAWJH,SAXI,EAWOzQ,MAXP,EAWemL,IAXf,CAWoBhM,WAAW,EAX/B,CAAP;AAYH;;AACD0R,EAAAA,qBAAqB,CAAC7Q,MAAD,EAAS;AAC1B,WAAO,KAAKgQ,cAAL,CAAoB7E,IAApB,CAAyBnM,MAAM,CAAEqM,GAAD,IAASA,GAAG,CAACrL,MAAJ,KAAeA,MAAf,IAAyBqL,GAAG,CAACI,MAAJ,KAAe;AAAa;AAA/D,KAA/B,EAAiHrM,IAAI,CAAC,CAAD,CAArH,EAA0HD,WAAW,EAArI,CAAP;AACH;;AACD2R,EAAAA,wBAAwB,CAACF,aAAD,EAAgB;AACpC,WAAOA,aAAa,CACfzF,IADE,CACG9L,UAAU,CAAEgM,GAAD,IAAS;AAC1B,cAAQA,GAAG,CAACI,MAAZ;AACI,aAAK;AAAa;AAAlB;AACI,iBAAO/M,EAAE,CAAC,KAAKwR,YAAL,CAAkB3O,QAAlB,EAAD,CAAT;;AACJ,aAAK;AAAU;AAAf;AACI,iBAAO3C,UAAU,CAACyM,GAAG,CAACK,KAAL,CAAjB;;AACJ;AACI,iBAAO7M,KAAP;AANR;AAQH,KATmB,CADb,EAWFsM,IAXE,CAWGhM,WAAW,EAXd,CAAP;AAYH;;AAjEoB;AAmEzB;;;AAAmB2Q,kBAAkB,CAAClM,IAAnB;AAAA,mBAAgHkM,kBAAhH,EA11B4H5S,EA01B5H,UAAoJmQ,eAApJ,GA11B4HnQ,EA01B5H,UAAgL2S,+BAAhL,GA11B4H3S,EA01B5H,UAA4N+R,aAA5N,GA11B4H/R,EA01B5H,UAAsP8R,WAAtP,GA11B4H9R,EA01B5H,UAA8QoP,6BAA9Q,GA11B4HpP,EA01B5H,UAAwTwR,qBAAxT;AAAA;AACnB;;;AAAmBoB,kBAAkB,CAACjM,KAAnB,kBA31B4H3G,EA21B5H;AAAA,SAAoH4S,kBAApH;AAAA,WAAoHA,kBAApH;AAAA,cAAoJ;AAApJ;;AACnB;AAAA,qDA51B+I5S,EA41B/I,mBAA4F4S,kBAA5F,EAA4H,CAAC;AACjH5P,IAAAA,IAAI,EAAE7C,UAD2G;AAEjHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF2G,GAAD,CAA5H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEmN;AAAR,KAAD,EAA4B;AAAEnN,MAAAA,IAAI,EAAE2P;AAAR,KAA5B,EAAuE;AAAE3P,MAAAA,IAAI,EAAE+O;AAAR,KAAvE,EAAgG;AAAE/O,MAAAA,IAAI,EAAE8O;AAAR,KAAhG,EAAuH;AAAE9O,MAAAA,IAAI,EAAEoM;AAAR,KAAvH,EAAgK;AAAEpM,MAAAA,IAAI,EAAEwO;AAAR,KAAhK,CAAP;AAA0M,GAHpP;AAAA;AAKA;AACA;AACA;AACA;;;AACA,MAAMqC,UAAU,GAAI7F,CAAD,IAAO;AACtBvK,EAAAA,MAAM,CAACqQ,MAAP,CAAc9F,CAAd;AACA,QAAM+F,WAAW,GAAG,OAAO/F,CAAP,KAAa,UAAjC;AACA,QAAMgG,UAAU,GAAGvQ,MAAM,CAACwQ,SAAP,CAAiB/K,cAApC;AACAzF,EAAAA,MAAM,CAACyQ,mBAAP,CAA2BlG,CAA3B,EAA8BxB,OAA9B,CAAsC,UAAUjJ,IAAV,EAAgB;AAClD,QAAIyQ,UAAU,CAACG,IAAX,CAAgBnG,CAAhB,EAAmBzK,IAAnB,MACCwQ,WAAW,GAAGxQ,IAAI,KAAK,QAAT,IAAqBA,IAAI,KAAK,QAA9B,IAA0CA,IAAI,KAAK,WAAtD,GAAoE,IADhF,KAEAyK,CAAC,CAACzK,IAAD,CAAD,KAAY,IAFZ,KAGC,OAAOyK,CAAC,CAACzK,IAAD,CAAR,KAAmB,QAAnB,IAA+B,OAAOyK,CAAC,CAACzK,IAAD,CAAR,KAAmB,UAHnD,KAIA,CAACE,MAAM,CAAC2Q,QAAP,CAAgBpG,CAAC,CAACzK,IAAD,CAAjB,CAJL,EAI+B;AAC3BsQ,MAAAA,UAAU,CAAC7F,CAAC,CAACzK,IAAD,CAAF,CAAV;AACH;AACJ,GARD;AASA,SAAOyK,CAAP;AACH,CAdD;AAgBA;AACA;AACA;;;AACA,MAAMqG,uBAAN,CAA8B;AAC1BtR,EAAAA,WAAW,CAACiQ,YAAD,EAAesB,WAAf,EAA4BC,OAA5B,EAAqC;AAC5C,SAAKvB,YAAL,GAAoBA,YAApB;AACA,SAAKsB,WAAL,GAAmBA,WAAnB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,sBAAsB,GAAG;AACrB,UAAMC,mBAAmB,GAAG;AACxBC,MAAAA,QAAQ,EAAE,MAAM,KAAK1B,YAAL,CAAkB3O,QAAlB,EADQ;AAExBsQ,MAAAA,QAAQ,EAAGC,QAAD,IAAc,KAAK5B,YAAL,CAAkB9D,IAAlB,CAAuB0F,QAAvB,CAFA;AAGxBzB,MAAAA,QAAQ,EAAGC,eAAD,IAAqB,KAAKkB,WAAL,CAAiBnB,QAAjB,CAA0BC,eAA1B;AAHP,KAA5B;;AAKA,QAAI,OAAOnN,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,aAAO,KAAKsO,OAAL,CAAaM,eAAb,GACDC,iCAAiC,CAACL,mBAAD,CADhC,GAEDA,mBAFN;AAGH,KAJD,MAKK;AACD,aAAOA,mBAAP;AACH;AACJ;;AACDM,EAAAA,2BAA2B,CAACC,OAAD,EAAU;AACjC,UAAMC,eAAe,GAAG,KAAKT,sBAAL,EAAxB,CADiC,CAEjC;;AACA,UAAMU,YAAY,GAAGD,eAAe,CAACP,QAAhB,EAArB,CAHiC,CAIjC;;AACAO,IAAAA,eAAe,CAACN,QAAhB,CAAyBlR,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBwR,YAAlB,CAAd,EAA+CF,OAAO,CAAC3L,QAAvD,CAAzB;AACH;;AA9ByB;AAgC9B;;;AAAmBgL,uBAAuB,CAAC3N,IAAxB;AAAA,mBAAqH2N,uBAArH,EAx5B4HrU,EAw5B5H,UAA8J8R,WAA9J,GAx5B4H9R,EAw5B5H,UAAsL4S,kBAAtL,GAx5B4H5S,EAw5B5H,UAAqN0H,UAArN;AAAA;AACnB;;;AAAmB2M,uBAAuB,CAAC1N,KAAxB,kBAz5B4H3G,EAy5B5H;AAAA,SAAyHqU,uBAAzH;AAAA,WAAyHA,uBAAzH;AAAA,cAA8J;AAA9J;;AACnB;AAAA,qDA15B+IrU,EA05B/I,mBAA4FqU,uBAA5F,EAAiI,CAAC;AACtHrR,IAAAA,IAAI,EAAE7C,UADgH;AAEtHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFgH,GAAD,CAAjI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAE8O;AAAR,KAAD,EAAwB;AAAE9O,MAAAA,IAAI,EAAE4P;AAAR,KAAxB,EAAsD;AAAE5P,MAAAA,IAAI,EAAE0E;AAAR,KAAtD,CAAP;AAAqF,GAH/H;AAAA;;AAIA,SAASoN,iCAAT,CAA2CK,IAA3C,EAAiD;AAC7C,SAAO;AACHT,IAAAA,QAAQ,EAAE,MAAMS,IAAI,CAACT,QAAL,EADb;AAEHC,IAAAA,QAAQ,EAAE/K,KAAK,IAAI;AACf,YAAMwL,WAAW,GAAGvB,UAAU,CAACjK,KAAD,CAA9B;AACA,aAAOuL,IAAI,CAACR,QAAL,CAAcS,WAAd,CAAP;AACH,KALE;AAMHjC,IAAAA,QAAQ,EAAE/J,OAAO,IAAI;AACjB,aAAO+L,IAAI,CAAChC,QAAL,CAAc/J,OAAd,CAAP;AACH;AARE,GAAP;AAUH;;AAED,SAASiM,WAAT,CAAqBzL,KAArB,EAA4B;AACxB,SAAQ0L,aAAD,IAAmB;AACtB,QAAI,OAAOrP,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,UAAI/B,KAAK,CAACC,OAAN,CAAcyF,KAAd,CAAJ,EAA0B;AACtBhE,QAAAA,uBAAuB;AAC1B,OAFD,MAGK,IAAI,OAAOgE,KAAP,KAAiB,QAArB,EAA+B;AAChC/D,QAAAA,2BAA2B;AAC9B;AACJ;;AACD,UAAM+O,QAAQ,GAAGnR,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB4R,aAAlB,CAAjB;;AACA,SAAK,MAAMzQ,GAAX,IAAkB+E,KAAlB,EAAyB;AACrB;AACAgL,MAAAA,QAAQ,CAAC/P,GAAD,CAAR,GAAgB+E,KAAK,CAAC/E,GAAD,CAArB;AACH;;AACD,WAAO+P,QAAP;AACH,GAfD;AAgBH;AAED;AACA;AACA;AACA;;;AACA,MAAMW,mBAAN,CAA0B;AACtBxS,EAAAA,WAAW,CAACyS,wBAAD,EAA2B;AAClC,SAAKA,wBAAL,GAAgCA,wBAAhC;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,kBAAkB,CAACC,WAAD,EAAc;AAC5B,UAAMP,IAAI,GAAG,KAAKK,wBAAL,CAA8BhB,sBAA9B,EAAb;;AACA,WAAO;AACHE,MAAAA,QAAQ,GAAG;AACP,cAAMiB,eAAe,GAAGR,IAAI,CAACT,QAAL,EAAxB;AACA,eAAOA,QAAQ,CAACiB,eAAD,EAAkBD,WAAW,CAACpM,IAA9B,CAAf;AACH,OAJE;;AAKHsM,MAAAA,UAAU,CAACpS,GAAD,EAAM;AACZ,cAAMmS,eAAe,GAAGR,IAAI,CAACT,QAAL,EAAxB;AACA,cAAMmB,aAAa,GAAGR,WAAW,CAAC7R,GAAD,CAAjC;AACA,eAAOsS,oBAAoB,CAACX,IAAD,EAAOQ,eAAP,EAAwBE,aAAxB,EAAuCH,WAAW,CAACpM,IAAnD,CAA3B;AACH,OATE;;AAUHqL,MAAAA,QAAQ,CAACnR,GAAD,EAAM;AACV,cAAMmS,eAAe,GAAGR,IAAI,CAACT,QAAL,EAAxB;AACA,eAAO9R,eAAe,CAACY,GAAD,CAAf,GACDsS,oBAAoB,CAACX,IAAD,EAAOQ,eAAP,EAAwBnS,GAAxB,EAA6BkS,WAAW,CAACpM,IAAzC,CADnB,GAEDyM,aAAa,CAACZ,IAAD,EAAOQ,eAAP,EAAwBnS,GAAxB,EAA6BkS,WAAW,CAACpM,IAAzC,CAFnB;AAGH,OAfE;;AAgBH6J,MAAAA,QAAQ,CAAC/J,OAAD,EAAU;AACd,eAAO+L,IAAI,CAAChC,QAAL,CAAc/J,OAAd,CAAP;AACH;;AAlBE,KAAP;AAoBH;;AA7BqB;AA+B1B;;;AAAmBmM,mBAAmB,CAAC7O,IAApB;AAAA,mBAAiH6O,mBAAjH,EAj+B4HvV,EAi+B5H,UAAsJqU,uBAAtJ;AAAA;AACnB;;;AAAmBkB,mBAAmB,CAAC5O,KAApB,kBAl+B4H3G,EAk+B5H;AAAA,SAAqHuV,mBAArH;AAAA,WAAqHA,mBAArH;AAAA,cAAsJ;AAAtJ;;AACnB;AAAA,qDAn+B+IvV,EAm+B/I,mBAA4FuV,mBAA5F,EAA6H,CAAC;AAClHvS,IAAAA,IAAI,EAAE7C,UAD4G;AAElHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF4G,GAAD,CAA7H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEqR;AAAR,KAAD,CAAP;AAA6C,GAHvF;AAAA;;AAIA,SAAS0B,aAAT,CAAuBZ,IAAvB,EAA6BQ,eAA7B,EAA8CK,QAA9C,EAAwD1M,IAAxD,EAA8D;AAC1D,QAAM2M,WAAW,GAAG5S,QAAQ,CAACsS,eAAD,EAAkBrM,IAAlB,EAAwB0M,QAAxB,CAA5B;AACAb,EAAAA,IAAI,CAACR,QAAL,CAAcsB,WAAd;AACA,SAAOA,WAAP,CAH0D,CAI1D;AACA;AACA;AACA;AACA;AACA;AACH;;AACD,SAASH,oBAAT,CAA8BX,IAA9B,EAAoCQ,eAApC,EAAqDO,aAArD,EAAoE5M,IAApE,EAA0E;AACtE,QAAM6M,KAAK,GAAGzB,QAAQ,CAACiB,eAAD,EAAkBrM,IAAlB,CAAtB;AACA,QAAM0M,QAAQ,GAAGE,aAAa,CAACC,KAAD,CAA9B;AACA,SAAOJ,aAAa,CAACZ,IAAD,EAAOQ,eAAP,EAAwBK,QAAxB,EAAkC1M,IAAlC,CAApB;AACH;;AACD,SAASoL,QAAT,CAAkBiB,eAAlB,EAAmCrM,IAAnC,EAAyC;AACrC,SAAOjF,QAAQ,CAACsR,eAAD,EAAkBrM,IAAlB,CAAf;AACH;;AAED,MAAM8M,cAAc,GAAG,IAAIC,MAAJ,CAAW,iBAAX,CAAvB;;AACA,SAASC,sBAAT,CAAgCvR,IAAhC,EAAsC;AAClC,MAAI,CAACA,IAAL,EAAW;AACPE,IAAAA,2BAA2B;AAC9B,GAFD,MAGK,IAAI,CAACmR,cAAc,CAACG,IAAf,CAAoBxR,IAApB,CAAL,EAAgC;AACjCD,IAAAA,mBAAmB,CAACC,IAAD,CAAnB;AACH;AACJ;;AACD,SAASyR,uBAAT,CAAiCC,SAAjC,EAA4CC,KAA5C,EAAmDC,YAAnD,EAAiE;AAC7D,QAAMrB,aAAa,GAAGqB,YAAY,CAACF,SAAD,CAAlC;;AACA,MAAInB,aAAa,IAAIA,aAAa,KAAKoB,KAAvC,EAA8C;AAC1CxR,IAAAA,qBAAqB,CAACuR,SAAD,EAAYC,KAAK,CAAC3R,IAAlB,EAAwBuQ,aAAa,CAACvQ,IAAtC,CAArB;AACH;AACJ;;AACD,SAAS6R,wBAAT,CAAkC1L,YAAlC,EAAgD;AAC5CA,EAAAA,YAAY,CAACsB,OAAb,CAAsBpB,UAAD,IAAgB;AACjC,QAAI,CAACvB,kBAAkB,CAACuB,UAAD,CAAvB,EAAqC;AACjC9F,MAAAA,wBAAwB,CAAC8F,UAAU,CAACrG,IAAZ,CAAxB;AACH;AACJ,GAJD;AAKH;AAED;AACA;AACA;AACA;AACA;;;AACA,SAAS8R,4BAAT,CAAsCzL,UAAtC,EAAkD;AAC9C,MAAI0L,2BAA2B,CAAC1L,UAAD,CAA3B,IAA2C2L,sBAAsB,CAAC3L,UAAD,CAArE,EAAmF;AAC/E;AACH;;AACDnE,EAAAA,OAAO,CAACC,IAAR,CAAaxB,sCAAsC,CAAC0F,UAAU,CAACrG,IAAZ,CAAnD;AACH;;AACD,SAASgS,sBAAT,CAAgC3L,UAAhC,EAA4C;AACxC;AACA;AACA;AACA;AACA,SAAO,CAAC,CAACA,UAAU,CAACzE,KAApB;AACH;;AACD,SAASmQ,2BAAT,CAAqC1L,UAArC,EAAiD;AAC7C;AACA,QAAM4L,WAAW,GAAG5L,UAAU,CAAC6L,eAAX,IAA8B,EAAlD;AACA,SAAOD,WAAW,CAACE,IAAZ,CAAkBC,UAAD,IAAgB,CAACA,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACC,cAApE,MAAwF,YAAzH,CAAP;AACH;AAED;AACA;AACA;;;AACA,MAAMC,SAAN,CAAgB;;AAEhBA,SAAS,CAACrU,IAAV,GAAiB,QAAjB;AACA;AACA;AACA;;AACA,MAAMsU,WAAN,CAAkB;AACdvU,EAAAA,WAAW,CAACwU,WAAD,EAAc;AACrB,SAAKA,WAAL,GAAmBA,WAAnB;AACH;;AAHa;;AAKlBD,WAAW,CAACtU,IAAZ,GAAmB,gBAAnB;AAEA,MAAMwU,wBAAwB,GAAG,IAAInX,cAAJ,CAAmB,0BAAnB,EAA+C;AAC5EwG,EAAAA,UAAU,EAAE,MADgE;AAE5E+B,EAAAA,OAAO,EAAE,OAAO;AAAE6O,IAAAA,sBAAsB,EAAE;AAA1B,GAAP;AAFmE,CAA/C,CAAjC;;AAKA,MAAMC,0BAAN,CAAiC;AAC7B3U,EAAAA,WAAW,CAACmF,OAAD,EAAU;AACjB;AACR;AACA;AACA;AACQ,SAAKyP,eAAL,GAAuB,IAAIC,GAAJ,CAAQ,CAACP,SAAS,CAACrU,IAAX,EAAiBsU,WAAW,CAACtU,IAA7B,CAAR,CAAvB;;AACA,QAAI,OAAOkF,OAAO,CAACuP,sBAAf,KAA0C,QAA9C,EAAwD;AACpD,WAAKI,aAAL,CAAmB,GAAG3P,OAAO,CAACuP,sBAAR,CAA+BK,MAArD;AACH;AACJ;AACD;AACJ;AACA;;;AACID,EAAAA,aAAa,CAAC,GAAGzO,OAAJ,EAAa;AACtB,SAAK,MAAMtG,MAAX,IAAqBsG,OAArB,EAA8B;AAC1B,WAAKuO,eAAL,CAAqBhH,GAArB,CAAyB7N,MAAM,CAACE,IAAhC;AACH;AACJ;AACD;;;AACAkE,EAAAA,IAAI,CAACpE,MAAD,EAAS;AACT,UAAMiV,qBAAqB,GAAG7T,KAAK,CAACtC,IAAN,CAAW,KAAK+V,eAAhB,EAAiCT,IAAjC,CAAsClU,IAAI,IAAIA,IAAI,KAAKH,yBAAyB,CAACC,MAAD,CAAhF,CAA9B;;AACA,QAAIiV,qBAAJ,EAA2B;AACvB;AACH;;AACDjV,IAAAA,MAAM,GACFA,MAAM,CAACC,WAAP,IAAsBD,MAAM,CAACC,WAAP,CAAmBgC,IAAnB,KAA4B,QAAlD,GACMjC,MAAM,CAACC,WAAP,CAAmBgC,IADzB,GAEMjC,MAAM,CAACE,IAHjB;AAIAiE,IAAAA,OAAO,CAACC,IAAR,CAAc,OAAMpE,MAAO,6IAA3B;AACH;;AA9B4B;AAgCjC;;;AAAmB4U,0BAA0B,CAAChR,IAA3B;AAAA,mBAAwHgR,0BAAxH,EA/lC4H1X,EA+lC5H,UAAoKwX,wBAApK;AAAA;AACnB;;;AAAmBE,0BAA0B,CAAC/Q,KAA3B,kBAhmC4H3G,EAgmC5H;AAAA,SAA4H0X,0BAA5H;AAAA,WAA4HA,0BAA5H;AAAA;;AACnB;AAAA,qDAjmC+I1X,EAimC/I,mBAA4F0X,0BAA5F,EAAoI,CAAC;AACzH1U,IAAAA,IAAI,EAAE7C;AADmH,GAAD,CAApI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9D/D,QAAAA,IAAI,EAAE5C,MADwD;AAE9DwG,QAAAA,IAAI,EAAE,CAAC4Q,wBAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GALxB;AAAA;;AAOA,MAAMQ,WAAW,GAAG,OAAO/R,SAAP,KAAqB,WAArB,IAAoCA,SAAxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMgS,YAAN,CAAmB;AACflV,EAAAA,WAAW,CAAC0O,SAAD,EAAY8C,OAAZ,EAAqB2D,cAArB,EAAqCrF,QAArC,EAA+CC,cAA/C,EAA+DqF,oBAA/D,EAAqFC,aAArF,EAAoG;AAC3G,SAAK3G,SAAL,GAAiBA,SAAjB;AACA,SAAK8C,OAAL,GAAeA,OAAf;AACA,SAAK2D,cAAL,GAAsBA,cAAtB;AACA,SAAKrF,QAAL,GAAgBA,QAAhB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKqF,oBAAL,GAA4BA,oBAA5B;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,yBAAL,GAAiC1X,OAAO,CAAC,MAAM;AAC3C;AACA,YAAM2X,YAAY,GAAG,IAArB;;AACA,eAASC,aAAT,CAAuB9T,GAAvB,EAA4B;AACxB,cAAMyE,IAAI,GAAGoP,YAAY,CAACE,UAAb,CAAwB/T,GAAxB,CAAb;AACA,eAAOyE,IAAI,GAAGyB,UAAU,CAACzB,IAAI,CAAC3F,KAAL,CAAW,GAAX,CAAD,EAAkB+U,YAAY,CAACnE,OAA/B,CAAb,GAAuD,IAAlE;AACH;;AACD,YAAM/K,OAAO,GAAG,KAAK0O,cAAL,GACV,KAAKA,cAAL,CAAoBO,yBAApB,EADU,GAEV;AACEhP,QAAAA,cAAc,CAAC5E,GAAD,EAAM;AAChB,cAAIgU,MAAM,GAAGF,aAAa,CAAC9T,GAAD,CAA1B;;AACA,cAAIgU,MAAJ,EAAY;AACR,mBAAOA,MAAP;AACH;;AACD,iBAAO,CAAC,GAAGjS,IAAJ,KAAa;AAChB;AACA,gBAAI,CAACiS,MAAL,EAAa;AACTA,cAAAA,MAAM,GAAGF,aAAa,CAAC9T,GAAD,CAAtB;AACH;;AACD,mBAAOgU,MAAM,GAAGA,MAAM,CAAC,GAAGjS,IAAJ,CAAT,GAAqBE,SAAlC;AACH,WAND;AAOH,SAbH;;AAcEoD,QAAAA,kBAAkB,CAAC4O,YAAD,EAAe;AAC7B,gBAAMC,qBAAqB,GAAGL,YAAY,CAACnE,OAAb,CAAqB3M,eAAnD;AACA,iBAAOnE,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBqV,qBAAlB,CAAd,EAAyDD,YAAY,IAAI,EAAzE,CAAP;AACH;;AAjBH,OAFN;AAqBA,aAAOtP,OAAP;AACH,KA7BuC,CAAxC;AA8BH;;AACS,MAANkC,MAAM,GAAG;AACT,WAAO,KAAKwM,cAAL,GAAsB,KAAKA,cAAL,CAAoBxM,MAA1C,GAAmD,KAAK4M,OAA/D;AACH;;AACe,MAAZ3B,YAAY,GAAG;AACf,WAAO,KAAKuB,cAAL,GAAsB,KAAKA,cAAL,CAAoBvB,YAA1C,GAAyD,KAAK4B,aAArE;AACH;;AACa,MAAVK,UAAU,GAAG;AACb,WAAO,KAAKV,cAAL,GAAsB,KAAKA,cAAL,CAAoBU,UAA1C,GAAuD,KAAKJ,WAAnE;AACH;;AACoB,SAAdQ,cAAc,CAAC3P,QAAD,EAAW;AAC5B,QAAIO,KAAK,GAAGP,QAAZ;;AACA,QAAInF,KAAK,CAACC,OAAN,CAAckF,QAAd,CAAJ,EAA6B;AACzBO,MAAAA,KAAK,GAAGP,QAAQ,CAACjF,KAAT,EAAR;AACH,KAFD,MAGK,IAAI0I,QAAQ,CAACzD,QAAD,CAAZ,EAAwB;AACzBO,MAAAA,KAAK,GAAGnG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB2F,QAAlB,CAAR;AACH,KAFI,MAGA,IAAIA,QAAQ,KAAKvC,SAAjB,EAA4B;AAC7B8C,MAAAA,KAAK,GAAG,EAAR;AACH;;AACD,WAAOA,KAAP;AACH;;AACDwG,EAAAA,WAAW,GAAG;AACV,QAAIwB,EAAJ;;AACA,KAACA,EAAE,GAAG,KAAKyG,oBAAX,MAAqC,IAArC,IAA6CzG,EAAE,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,EAAE,CAACqH,WAAH,EAAtE;AACH;AACD;AACJ;AACA;;;AACItI,EAAAA,GAAG,CAACzF,YAAD,EAAe;AACd,QAAI8M,WAAJ,EAAiB;AACbpB,MAAAA,wBAAwB,CAAC1L,YAAD,CAAxB;AACH;;AACD,UAAM;AAAEgO,MAAAA;AAAF,QAAgB,KAAKC,cAAL,CAAoBjO,YAApB,CAAtB;AACA,QAAI,CAACgO,SAAS,CAACrV,MAAf,EACI,OAAO,EAAP;AACJ,UAAMuV,UAAU,GAAGnO,UAAU,CAACiO,SAAD,CAA7B;AACA,UAAMG,YAAY,GAAGnN,eAAe,CAACkN,UAAD,CAApC;AACA,UAAM/O,KAAK,GAAGsB,kBAAkB,CAACyN,UAAD,CAAhC;AACA,UAAME,SAAS,GAAG7N,WAAW,CAACyN,SAAD,CAA7B;AACA,UAAMK,kBAAkB,GAAG,EAA3B;;AACA,SAAK,MAAMxU,IAAX,IAAmBsU,YAAnB,EAAiC;AAC7B,YAAMjO,UAAU,GAAGkO,SAAS,CAACvU,IAAD,CAA5B;AACA,YAAMuE,IAAI,GAAGe,KAAK,CAACtF,IAAD,CAAlB;AACA,YAAMsG,IAAI,GAAGD,UAAU,CAAC7D,QAAD,CAAvB;AACA,WAAKiS,oBAAL,CAA0BnO,IAA1B,EAAgC/B,IAAhC,EAJ6B,CAK7B;AACA;AACA;AACA;;AACA,UAAI0O,WAAJ,EAAiB;AACbnB,QAAAA,4BAA4B,CAACzL,UAAD,CAA5B;AACH;;AACD,YAAMqO,QAAQ,GAAG;AACb1U,QAAAA,IADa;AAEbuE,QAAAA,IAFa;AAGboQ,QAAAA,aAAa,EAAE,KAHF;AAIbtQ,QAAAA,OAAO,EAAEiC,IAAI,CAACjC,OAJD;AAKbuQ,QAAAA,QAAQ,EAAE,KAAKlI,SAAL,CAAe3I,GAAf,CAAmBsC,UAAnB,CALG;AAMb/B,QAAAA,QAAQ,EAAE4O,YAAY,CAACe,cAAb,CAA4B3N,IAAI,CAAChC,QAAjC;AANG,OAAjB,CAZ6B,CAoB7B;AACA;AACA;;AACA,UAAI,CAAC,KAAKuQ,6BAAL,CAAmC7U,IAAnC,EAAyCuE,IAAzC,CAAL,EAAqD;AACjDiQ,QAAAA,kBAAkB,CAAChN,IAAnB,CAAwBkN,QAAxB;AACH;;AACD,WAAK/N,MAAL,CAAYa,IAAZ,CAAiBkN,QAAjB;AACH;;AACD,WAAOF,kBAAP;AACH;AACD;AACJ;AACA;;;AACIM,EAAAA,oBAAoB,CAAC3O,YAAD,EAAe;AAC/B,UAAM4O,OAAO,GAAG5O,YAAY,IAAI,EAAhC;AACA,UAAM6O,YAAY,GAAG,KAAKpJ,GAAL,CAASmJ,OAAT,CAArB;AACA,UAAMzQ,QAAQ,GAAG0Q,YAAY,CAACjW,MAAb,CAAoB,CAAC0H,MAAD,EAASkK,WAAT,KAAyBrS,QAAQ,CAACmI,MAAD,EAASkK,WAAW,CAACpM,IAArB,EAA2BoM,WAAW,CAACrM,QAAvC,CAArD,EAAuG,EAAvG,CAAjB;AACA,WAAO;AAAEA,MAAAA,QAAF;AAAYqC,MAAAA,MAAM,EAAEqO;AAApB,KAAP;AACH;;AACDC,EAAAA,qBAAqB,GAAG;AACpB;AACA;AACA;AACA,QAAI,KAAK9B,cAAL,IAAuB,KAAKG,oBAAL,KAA8B,IAAzD,EAA+D;AAC3D;AACH;;AACD,UAAM4B,WAAW,GAAG,IAAI3Y,OAAJ,EAApB;AACA,SAAK+W,oBAAL,GAA4B,KAAKxF,QAAL,CACvB5E,IADuB,CAClBnM,MAAM,CAAEqM,GAAD,IAASA,GAAG,CAACI,MAAJ,KAAe;AAAa;AAAtC,KADY,EAC6CnM,QAAQ,CAAC+L,GAAG,IAAI;AACrF8L,MAAAA,WAAW,CAAC/K,IAAZ,CAAiBf,GAAjB;AACA,YAAMrL,MAAM,GAAGqL,GAAG,CAACrL,MAAnB;AACA,aAAO,KAAKoX,aAAL,CAAmBD,WAAnB,EAAgCnX,MAAhC,EAAwCmL,IAAxC,CAA6ClM,GAAG,CAAC,OAAO;AAAEe,QAAAA,MAAF;AAAUyL,QAAAA,MAAM,EAAE;AAAa;;AAA/B,OAAP,CAAD,CAAhD,EAA6GlM,cAAc,CAAC;AAAES,QAAAA,MAAF;AAAUyL,QAAAA,MAAM,EAAE;AAAW;;AAA7B,OAAD,CAA3H,EAA4KjM,UAAU,CAACkM,KAAK,IAAIhN,EAAE,CAAC;AAAEsB,QAAAA,MAAF;AAAUyL,QAAAA,MAAM,EAAE;AAAU;AAA5B;AAA2CC,QAAAA;AAA3C,OAAD,CAAZ,CAAtL,CAAP;AACH,KAJgF,CADrD,EAMvBS,SANuB,CAMbd,GAAG,IAAI,KAAK2E,cAAL,CAAoB5D,IAApB,CAAyBf,GAAzB,CANM,CAA5B;AAOH;AACD;AACJ;AACA;;;AACI+L,EAAAA,aAAa,CAACD,WAAD,EAAcnX,MAAd,EAAsB;AAC/B,UAAME,IAAI,GAAGH,yBAAyB,CAACC,MAAD,CAAtC;AACA,UAAMkS,OAAO,GAAG,EAAhB,CAF+B,CAG/B;AACA;;AACA,QAAImF,oBAAoB,GAAG,KAA3B;;AACA,SAAK,MAAMC,QAAX,IAAuB,KAAK1O,MAA5B,EAAoC;AAChC,YAAM2O,WAAW,GAAGD,QAAQ,CAAChR,OAAT,CAAiBpG,IAAjB,CAApB;;AACA,UAAIqX,WAAJ,EAAiB;AACb,aAAK,MAAMC,UAAX,IAAyBD,WAAzB,EAAsC;AAClC,gBAAME,YAAY,GAAG,KAAKpC,oBAAL,CAA0B1C,kBAA1B,CAA6C2E,QAA7C,CAArB;;AACA,cAAI;AACA,gBAAI5O,MAAM,GAAG4O,QAAQ,CAACT,QAAT,CAAkBW,UAAU,CAACzP,EAA7B,EAAiC0P,YAAjC,EAA+CzX,MAA/C,CAAb;;AACA,gBAAI0I,MAAM,YAAY2F,OAAtB,EAA+B;AAC3B3F,cAAAA,MAAM,GAAG5J,IAAI,CAAC4J,MAAD,CAAb;AACH;;AACD,gBAAI3J,YAAY,CAAC2J,MAAD,CAAhB,EAA0B;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,cAAAA,MAAM,GAAGA,MAAM,CAACyC,IAAP,CAAY7L,QAAQ,CAAEwH,KAAD,IAAW;AACrC,oBAAIA,KAAK,YAAYuH,OAArB,EAA8B;AAC1B,yBAAOvP,IAAI,CAACgI,KAAD,CAAX;AACH;;AACD,oBAAI/H,YAAY,CAAC+H,KAAD,CAAhB,EAAyB;AACrB,yBAAOA,KAAP;AACH;;AACD,uBAAOpI,EAAE,CAACoI,KAAD,CAAT;AACH,eAR4B,CAApB,EAQLvH,cAAc,CAAC,EAAD,CART,CAAT;;AASA,kBAAIiY,UAAU,CAACpS,OAAX,CAAmBsS,iBAAvB,EAA0C;AACtC;AACAhP,gBAAAA,MAAM,GAAGA,MAAM,CAACyC,IAAP,CAAY1L,SAAS,CAAC0X,WAAW,CAAChM,IAAZ,CAAiBf,kBAAkB,CAACpK,MAAD,CAAnC,CAAD,CAArB,CAAT;AACH;AACJ,aAtBD,MAuBK;AACD0I,cAAAA,MAAM,GAAGhK,EAAE,CAAC,EAAD,CAAF,CAAOyM,IAAP,CAAYhM,WAAW,EAAvB,CAAT;AACH;;AACD+S,YAAAA,OAAO,CAACzI,IAAR,CAAaf,MAAb;AACH,WAhCD,CAiCA,OAAOiP,CAAP,EAAU;AACNzF,YAAAA,OAAO,CAACzI,IAAR,CAAa7K,UAAU,CAAC+Y,CAAD,CAAvB;AACH;;AACDN,UAAAA,oBAAoB,GAAG,IAAvB;AACH;AACJ;AACJ,KAlD8B,CAmD/B;AACA;;;AACA,QAAInC,WAAW,IAAI,CAACmC,oBAApB,EAA0C;AACtC,YAAMO,sBAAsB,GAAG,KAAKjJ,SAAL,CAAe3I,GAAf,CAAmB4O,0BAAnB,EAA+C,IAA/C,CAA/B,CADsC,CAEtC;AACA;AACA;;;AACA,UAAIgD,sBAAJ,EAA4B;AACxBA,QAAAA,sBAAsB,CAACxT,IAAvB,CAA4BpE,MAA5B;AACH;AACJ;;AACD,QAAI,CAACkS,OAAO,CAACnR,MAAb,EAAqB;AACjBmR,MAAAA,OAAO,CAACzI,IAAR,CAAa/K,EAAE,CAAC,EAAD,CAAf;AACH;;AACD,WAAOC,QAAQ,CAACuT,OAAD,CAAf;AACH;;AACDmE,EAAAA,cAAc,CAACjO,YAAD,EAAe;AACzB,UAAMgO,SAAS,GAAG,EAAlB;AACA,UAAMyB,SAAS,GAAG,KAAKhE,YAAvB;;AACA,SAAK,MAAMvL,UAAX,IAAyBF,YAAzB,EAAuC;AACnC,YAAMuL,SAAS,GAAG5M,kBAAkB,CAACuB,UAAD,CAAlB,CAA+BrG,IAAjD;;AACA,UAAIiT,WAAJ,EAAiB;AACbxB,QAAAA,uBAAuB,CAACC,SAAD,EAAYrL,UAAZ,EAAwBuP,SAAxB,CAAvB;AACH;;AACD,YAAMC,cAAc,GAAG,CAACD,SAAS,CAAClE,SAAD,CAAjC;;AACA,UAAImE,cAAJ,EAAoB;AAChB1B,QAAAA,SAAS,CAAC3M,IAAV,CAAenB,UAAf;AACAuP,QAAAA,SAAS,CAAClE,SAAD,CAAT,GAAuBrL,UAAvB;AACH;AACJ;;AACD,WAAO;AAAE8N,MAAAA;AAAF,KAAP;AACH;;AACDM,EAAAA,oBAAoB,CAACnO,IAAD,EAAO/B,IAAP,EAAa;AAC7B,SAAKsP,UAAL,CAAgBvN,IAAI,CAACtG,IAArB,IAA6BuE,IAA7B,CAD6B,CAE7B;AACA;AACA;;AACA+B,IAAAA,IAAI,CAAC/B,IAAL,GAAYA,IAAZ;AACH;;AACDsQ,EAAAA,6BAA6B,CAAC7U,IAAD,EAAOuE,IAAP,EAAa;AACtC,UAAMuR,iCAAiC,GAAGxW,QAAQ,CAAC,KAAK+T,aAAN,EAAqB9O,IAArB,CAAR,KAAuCxC,SAAjF,CADsC,CAEtC;AACA;;AACA,WAAO,KAAK6P,YAAL,CAAkB5R,IAAlB,KAA2B8V,iCAAlC;AACH;;AA9Oc;AAgPnB;;;AAAmB5C,YAAY,CAACvR,IAAb;AAAA,mBAA0GuR,YAA1G,EAt2C4HjY,EAs2C5H,UAAwIA,EAAE,CAAC6R,QAA3I,GAt2C4H7R,EAs2C5H,UAAgK0H,UAAhK,GAt2C4H1H,EAs2C5H,UAAuLiY,YAAvL,OAt2C4HjY,EAs2C5H,UAAgPmQ,eAAhP,GAt2C4HnQ,EAs2C5H,UAA4Q2S,+BAA5Q,GAt2C4H3S,EAs2C5H,UAAwTuV,mBAAxT,GAt2C4HvV,EAs2C5H,UAAwVgB,mBAAxV;AAAA;AACnB;;;AAAmBiX,YAAY,CAACtR,KAAb,kBAv2C4H3G,EAu2C5H;AAAA,SAA8GiY,YAA9G;AAAA,WAA8GA,YAA9G;AAAA;;AACnB;AAAA,qDAx2C+IjY,EAw2C/I,mBAA4FiY,YAA5F,EAAsH,CAAC;AAC3GjV,IAAAA,IAAI,EAAE7C;AADqG,GAAD,CAAtH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAEhD,EAAE,CAAC6R;AAAX,KAAD,EAAwB;AAAE7O,MAAAA,IAAI,EAAE0E;AAAR,KAAxB,EAA8C;AAAE1E,MAAAA,IAAI,EAAEiV,YAAR;AAAsBlR,MAAAA,UAAU,EAAE,CAAC;AAC9G/D,QAAAA,IAAI,EAAEtC;AADwG,OAAD,EAE9G;AACCsC,QAAAA,IAAI,EAAErC;AADP,OAF8G;AAAlC,KAA9C,EAI3B;AAAEqC,MAAAA,IAAI,EAAEmN;AAAR,KAJ2B,EAIA;AAAEnN,MAAAA,IAAI,EAAE2P;AAAR,KAJA,EAI2C;AAAE3P,MAAAA,IAAI,EAAEuS;AAAR,KAJ3C,EAI0E;AAAEvS,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACvI/D,QAAAA,IAAI,EAAEtC;AADiI,OAAD,EAEvI;AACCsC,QAAAA,IAAI,EAAE5C,MADP;AAECwG,QAAAA,IAAI,EAAE,CAAC5F,mBAAD;AAFP,OAFuI;AAA/B,KAJ1E,CAAP;AASlB,GAXxB;AAAA;;AAaA,SAAS8Z,yBAAT,CAAmCC,gBAAnC,EAAqDC,SAArD,EAAgEC,kBAAhE,EAAoF;AAChF,SAAQzR,OAAD,IAAa;AAChB,UAAM;AAAE0R,MAAAA,yBAAF;AAA6BtT,MAAAA;AAA7B,QAAiDuT,sBAAsB,CAAC3R,OAAD,EAAUuR,gBAAV,EAA4BC,SAA5B,CAA7E;AACA,WAAO,SAASI,cAAT,CAAwBC,SAAxB,EAAmC;AACtC;AACA,YAAMrG,OAAO,GAAGkG,yBAAyB,CAACnZ,GAA1B,CAA+BuZ,KAAD,IAAWA,KAAK,CAACD,SAAD,CAA9C,CAAhB,CAFsC,CAGtC;AACA;AACA;;AACA,UAAI;AACA,eAAOJ,kBAAkB,CAAC,GAAGjG,OAAJ,CAAzB;AACH,OAFD,CAGA,OAAOuG,EAAP,EAAW;AACP,YAAIA,EAAE,YAAYC,SAAd,IAA2B5T,eAAe,CAACE,cAA/C,EAA+D;AAC3D,iBAAOhB,SAAP;AACH;;AACD,cAAMyU,EAAN;AACH;AACJ,KAfD;AAgBH,GAlBD;AAmBH;;AACD,SAASE,wBAAT,CAAkC1R,UAAlC,EAA8C2R,gBAA9C,EAAgE;AAC5D,QAAM1R,cAAc,GAAG0R,gBAAgB,IAAIA,gBAAgB,CAAC1R,cAA5D;;AACA,QAAM2R,SAAS,GAAG,SAASC,iBAAT,CAA2B,GAAGhV,IAA9B,EAAoC;AAClD,UAAMiV,WAAW,GAAG9R,UAAU,CAAC+R,KAAX,CAAiB9R,cAAjB,EAAiCpD,IAAjC,CAApB;;AACA,QAAIiV,WAAW,YAAY/Q,QAA3B,EAAqC;AACjC,YAAMiR,eAAe,GAAGhb,OAAO,CAAC+a,KAAR,CAAc,IAAd,EAAoB,CAACD,WAAD,CAApB,CAAxB;AACA,aAAOE,eAAP;AACH;;AACD,WAAOF,WAAP;AACH,GAPD;;AAQA,QAAMG,UAAU,GAAGjb,OAAO,CAAC4a,SAAD,CAA1B;AACAlY,EAAAA,MAAM,CAACwY,cAAP,CAAsBD,UAAtB,EAAkCjS,UAAlC;AACA,SAAOiS,UAAP;AACH;;AACD,SAASb,sBAAT,CAAgC3R,OAAhC,EAAyCuR,gBAAzC,EAA2DC,SAAS,GAAG,EAAvE,EAA2E;AACvE,QAAMkB,oBAAoB,GAAGnB,gBAAgB,CAAC7Q,kBAAjB,EAA7B;AACA,QAAMtC,eAAe,GAAG4B,OAAO,CAACU,kBAAR,CAA2BgS,oBAA3B,CAAxB;AACA,QAAMC,gBAAgB,GAAGC,mBAAmB,CAACpB,SAAD,EAAYpT,eAAZ,EAA6BmT,gBAAgB,CAAC/Q,cAA9C,CAA5C;AACA,QAAMkR,yBAAyB,GAAGiB,gBAAgB,CAACpa,GAAjB,CAAsBsa,QAAD,IAAc;AACjE,UAAMzT,OAAO,GAAG0T,sBAAsB,CAACD,QAAD,CAAtC;AACA,WAAOzT,OAAO,CAACY,OAAD,CAAd;AACH,GAHiC,CAAlC;AAIA,SAAO;AACH5B,IAAAA,eADG;AAEHsT,IAAAA;AAFG,GAAP;AAIH;;AACD,SAASkB,mBAAT,CAA6BpB,SAAS,GAAG,EAAzC,EAA6CpT,eAA7C,EAA8DoC,cAA9D,EAA8E;AAC1E,QAAMmS,gBAAgB,GAAG,EAAzB;AACA,QAAMI,uBAAuB,GAAGvB,SAAS,CAACnX,MAAV,KAAqB,CAArB,IAA0B+D,eAAe,CAACC,oBAA1E;;AACA,MAAImC,cAAc,IAAIuS,uBAAtB,EAA+C;AAC3C;AACA,UAAMnC,QAAQ,GAAGvQ,kBAAkB,CAACG,cAAD,CAAnC;;AACA,QAAIoQ,QAAJ,EAAc;AACV+B,MAAAA,gBAAgB,CAAC5P,IAAjB,CAAsBvC,cAAtB;AACH;AACJ;;AACD,MAAIgR,SAAJ,EAAe;AACXmB,IAAAA,gBAAgB,CAAC5P,IAAjB,CAAsB,GAAGyO,SAAzB;AACH;;AACD,SAAOmB,gBAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASG,sBAAT,CAAgCD,QAAhC,EAA0C;AACtC,QAAMjC,QAAQ,GAAGjQ,qBAAqB,CAACkS,QAAD,CAArB,IAAmCxS,kBAAkB,CAACwS,QAAD,CAAtE;AACA,SAAQjC,QAAQ,IAAIA,QAAQ,CAAC7Q,gBAAtB,KAA4C,MAAM8S,QAAlD,CAAP;AACH,C,CAED;;;AACA,MAAMG,KAAN,CAAY;AACRzZ,EAAAA,WAAW,CAACiQ,YAAD,EAAewC,wBAAf,EAAyCjB,OAAzC,EAAkDkI,0BAAlD,EAA8EC,aAA9E,EAA6FC,iBAA7F,EAAgH;AACvH,SAAK3J,YAAL,GAAoBA,YAApB;AACA,SAAKwC,wBAAL,GAAgCA,wBAAhC;AACA,SAAKjB,OAAL,GAAeA,OAAf;AACA,SAAKkI,0BAAL,GAAkCA,0BAAlC;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKE,sBAAL,GAA8B,KAAK5J,YAAL,CAAkB/E,IAAlB,CAAuBa,SAAS,CAAC,KAAK2N,0BAAN,CAAhC,EAAmExa,WAAW,CAAC;AAAE4a,MAAAA,UAAU,EAAE,CAAd;AAAiBC,MAAAA,QAAQ,EAAE;AAA3B,KAAD,CAA9E,CAA9B;AACA,SAAKC,eAAL,CAAqBJ,iBAArB;AACH;AACD;AACJ;AACA;;;AACIxJ,EAAAA,QAAQ,CAACC,eAAD,EAAkB;AACtB,WAAO,KAAKoC,wBAAL,CAA8BhB,sBAA9B,GAAuDrB,QAAvD,CAAgEC,eAAhE,CAAP;AACH;;AACD4J,EAAAA,MAAM,CAACX,QAAD,EAAW;AACb,UAAMY,UAAU,GAAG,KAAKC,uBAAL,CAA6Bb,QAA7B,CAAnB;AACA,WAAO,KAAKO,sBAAL,CAA4B3O,IAA5B,CAAiClM,GAAG,CAACkb,UAAD,CAApC,EAAkD3a,UAAU,CAAE6a,GAAD,IAAS;AACzE;AACA,YAAM;AAAErV,QAAAA;AAAF,UAAqB,KAAKyM,OAAL,CAAa3M,eAAxC;;AACA,UAAIuV,GAAG,YAAY3B,SAAf,IAA4B1T,cAAhC,EAAgD;AAC5C,eAAOtG,EAAE,CAACsF,SAAD,CAAT;AACH,OALwE,CAMzE;;;AACA,aAAOpF,UAAU,CAACyb,GAAD,CAAjB;AACH,KARkE,CAA5D,EAQH3a,oBAAoB,EARjB,EAQqBsM,SAAS,CAAC,KAAK2N,0BAAN,CAR9B,CAAP;AASH;;AACDW,EAAAA,UAAU,CAACf,QAAD,EAAW;AACjB,WAAO,KAAKW,MAAL,CAAYX,QAAZ,EAAsBpO,IAAtB,CAA2B/L,IAAI,CAAC,CAAD,CAA/B,CAAP;AACH;;AACDmb,EAAAA,cAAc,CAAChB,QAAD,EAAW;AACrB,UAAMY,UAAU,GAAG,KAAKC,uBAAL,CAA6Bb,QAA7B,CAAnB;AACA,WAAOY,UAAU,CAAC,KAAKjK,YAAL,CAAkB3O,QAAlB,EAAD,CAAjB;AACH;AACD;AACJ;AACA;;;AACI4K,EAAAA,SAAS,CAACpE,EAAD,EAAK;AACV,WAAO,KAAK+R,sBAAL,CACF3O,IADE,CACGa,SAAS,CAAC,KAAK2N,0BAAN,CADZ,EAEFxN,SAFE,CAEQpE,EAFR,CAAP;AAGH;AACD;AACJ;AACA;;;AACIyS,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAK9H,wBAAL,CAA8BhB,sBAA9B,GAAuDE,QAAvD,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI6I,EAAAA,KAAK,CAAC7G,KAAD,EAAQ;AACT,WAAO,KAAKlB,wBAAL,CAA8BhB,sBAA9B,GAAuDG,QAAvD,CAAgE+B,KAAhE,CAAP;AACH;;AACDwG,EAAAA,uBAAuB,CAACb,QAAD,EAAW;AAC9B,UAAMmB,cAAc,GAAGlB,sBAAsB,CAACD,QAAD,CAA7C;;AACA,UAAMoB,cAAc,GAAG,KAAKf,aAAL,CAAmBjE,yBAAnB,EAAvB;;AACA,WAAO+E,cAAc,CAACC,cAAD,CAArB;AACH;;AACDV,EAAAA,eAAe,CAACJ,iBAAD,EAAoB;AAC/B,UAAM/S,KAAK,GAAG,KAAKoJ,YAAL,CAAkBpJ,KAAhC;AACA,UAAM8T,YAAY,GAAG,CAAC9T,KAAD,IAAUnG,MAAM,CAACkJ,IAAP,CAAY/C,KAAZ,EAAmB/F,MAAnB,KAA8B,CAA7D;;AACA,QAAI6Z,YAAJ,EAAkB;AACd,YAAMC,oBAAoB,GAAGla,MAAM,CAACkJ,IAAP,CAAY,KAAK4H,OAAL,CAAa5M,aAAzB,EAAwC9D,MAAxC,GAAiD,CAA9E;AACA,YAAM+Z,WAAW,GAAGD,oBAAoB,GAClCla,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAK6Q,OAAL,CAAa5M,aAA/B,CAAd,EAA6DgV,iBAA7D,CADkC,GACgDA,iBADxF;;AAEA,WAAK3J,YAAL,CAAkB9D,IAAlB,CAAuB0O,WAAvB;AACH;AACJ;;AA3EO;AA6EZ;;;AAAmBpB,KAAK,CAAC9V,IAAN;AAAA,mBAAmG8V,KAAnG,EA3gD4Hxc,EA2gD5H,UAA0H8R,WAA1H,GA3gD4H9R,EA2gD5H,UAAkJqU,uBAAlJ,GA3gD4HrU,EA2gD5H,UAAsL0H,UAAtL,GA3gD4H1H,EA2gD5H,UAA6MoP,6BAA7M,GA3gD4HpP,EA2gD5H,UAAuPiY,YAAvP,GA3gD4HjY,EA2gD5H,UAAgRgB,mBAAhR;AAAA;AACnB;;;AAAmBwb,KAAK,CAAC7V,KAAN,kBA5gD4H3G,EA4gD5H;AAAA,SAAuGwc,KAAvG;AAAA,WAAuGA,KAAvG;AAAA,cAA0H;AAA1H;;AACnB;AAAA,qDA7gD+Ixc,EA6gD/I,mBAA4Fwc,KAA5F,EAA+G,CAAC;AACpGxZ,IAAAA,IAAI,EAAE7C,UAD8F;AAEpGyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF8F,GAAD,CAA/G,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAE8O;AAAR,KAAD,EAAwB;AAAE9O,MAAAA,IAAI,EAAEqR;AAAR,KAAxB,EAA2D;AAAErR,MAAAA,IAAI,EAAE0E;AAAR,KAA3D,EAAiF;AAAE1E,MAAAA,IAAI,EAAEoM;AAAR,KAAjF,EAA0H;AAAEpM,MAAAA,IAAI,EAAEiV;AAAR,KAA1H,EAAkJ;AAAEjV,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC/M/D,QAAAA,IAAI,EAAEtC;AADyM,OAAD,EAE/M;AACCsC,QAAAA,IAAI,EAAE5C,MADP;AAECwG,QAAAA,IAAI,EAAE,CAAC5F,mBAAD;AAFP,OAF+M;AAA/B,KAAlJ,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;;;AACA,MAAM6c,aAAN,CAAoB;AAChB9a,EAAAA,WAAW,CAAC+a,KAAD,EAAQ9S,MAAR,EAAgB;AACvB6S,IAAAA,aAAa,CAACC,KAAd,GAAsBA,KAAtB;AACAD,IAAAA,aAAa,CAAC7S,MAAd,GAAuBA,MAAvB;AACH;;AACDoF,EAAAA,WAAW,GAAG;AACVyN,IAAAA,aAAa,CAACC,KAAd,GAAsB,IAAtB;AACAD,IAAAA,aAAa,CAAC7S,MAAd,GAAuB,IAAvB;AACH;;AARe;;AAUpB6S,aAAa,CAACC,KAAd,GAAsB,IAAtB;AACAD,aAAa,CAAC7S,MAAd,GAAuB,IAAvB;AACA;;AAAmB6S,aAAa,CAACnX,IAAd;AAAA,mBAA2GmX,aAA3G,EAviD4H7d,EAuiD5H,UAA0Iwc,KAA1I,GAviD4Hxc,EAuiD5H,UAA4J0H,UAA5J;AAAA;AACnB;;;AAAmBmW,aAAa,CAAClX,KAAd,kBAxiD4H3G,EAwiD5H;AAAA,SAA+G6d,aAA/G;AAAA,WAA+GA,aAA/G;AAAA,cAA0I;AAA1I;;AACnB;AAAA,qDAziD+I7d,EAyiD/I,mBAA4F6d,aAA5F,EAAuH,CAAC;AAC5G7a,IAAAA,IAAI,EAAE7C,UADsG;AAE5GyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFsG,GAAD,CAAvH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEwZ;AAAR,KAAD,EAAkB;AAAExZ,MAAAA,IAAI,EAAE0E;AAAR,KAAlB,CAAP;AAAiD,GAH3F;AAAA;;AAKA,MAAMqW,qBAAN,CAA4B;AACxBhb,EAAAA,WAAW,CAACib,MAAD,EAAS9K,sBAAT,EAAiCsC,wBAAjC,EAA2D2C,oBAA3D,EAAiF8F,aAAjF,EAAgG;AACvG,SAAKD,MAAL,GAAcA,MAAd;AACA,SAAK9K,sBAAL,GAA8BA,sBAA9B;AACA,SAAKsC,wBAAL,GAAgCA,wBAAhC;AACA,SAAK2C,oBAAL,GAA4BA,oBAA5B;AACA,SAAK8F,aAAL,GAAqBA,aAArB;AACA,SAAKC,SAAL,GAAiB,IAAI5c,OAAJ,EAAjB;AACH;;AACD8O,EAAAA,WAAW,GAAG;AACV,SAAK8N,SAAL,CAAehP,IAAf;AACH;;AACDiP,EAAAA,aAAa,CAACrb,MAAD,EAASkS,OAAT,EAAkB;AAC3B,SAAKQ,wBAAL,CACKhB,sBADL,GAEKrB,QAFL,CAEcrQ,MAFd,EAGKmL,IAHL,CAGUnM,MAAM,CAAC,MAAM,CAAC,CAACkT,OAAT,CAHhB,EAGmCvS,GAAG,CAAC,MAAM,KAAK2b,mBAAL,CAAyBpJ,OAAO,CAACtJ,MAAjC,CAAP,CAHtC,EAGwFtJ,QAAQ,CAAC,MAAM,KAAK6b,aAAL,CAAmBI,gBAA1B,CAHhG,EAG6Ivc,MAAM,CAACwc,eAAe,IAAI,CAAC,CAACA,eAAtB,CAHnJ,EAG2Lhc,UAAU,CAACkM,KAAK,IAAI;AAC3M;AACA;AACA;AACA;AACA,WAAK0E,sBAAL,CAA4B5B,iBAA5B,CAA8C9C,KAA9C;;AACA,aAAO7M,KAAP;AACH,KAPoM,CAHrM,EAUIY,SAAS,CAAC,KAAK2b,SAAN,CAVb,EAWKjP,SAXL,CAWe,MAAM,KAAKsP,wBAAL,CAA8BvJ,OAAO,CAACtJ,MAAtC,CAXrB;AAYH;;AACD0S,EAAAA,mBAAmB,CAACrE,YAAD,EAAe;AAC9B,SAAK,MAAMrE,WAAX,IAA0BqE,YAA1B,EAAwC;AACpC,YAAMJ,QAAQ,GAAGjE,WAAW,CAACiE,QAA7B;;AACA,UAAIA,QAAQ,CAAC6E,aAAb,EAA4B;AACxB,aAAKR,MAAL,CACKhB,MADL,CACYtG,KAAK,IAAIrS,QAAQ,CAACqS,KAAD,EAAQhB,WAAW,CAACpM,IAApB,CAD7B,EAEK2E,IAFL,CAEUvL,SAAS,CAACoE,SAAD,CAFnB,EAEgCnE,QAAQ,EAFxC,EAE4CJ,SAAS,CAAC,KAAK2b,SAAN,CAFrD,EAGKjP,SAHL,CAGe,CAAC,CAAC3G,aAAD,EAAgBC,YAAhB,CAAD,KAAmC;AAC9C,gBAAMkW,MAAM,GAAG,IAAIpW,gBAAJ,CAAqBC,aAArB,EAAoCC,YAApC,EAAkD,CAACmN,WAAW,CAACgE,aAA/D,CAAf;AACAC,UAAAA,QAAQ,CAAC6E,aAAT,CAAuBC,MAAvB;AACH,SAND;AAOH;;AACD,UAAI9E,QAAQ,CAAC+E,UAAb,EAAyB;AACrB/E,QAAAA,QAAQ,CAAC+E,UAAT,CAAoB,KAAKC,gBAAL,CAAsBjJ,WAAtB,CAApB;AACH;;AACDA,MAAAA,WAAW,CAACgE,aAAZ,GAA4B,IAA5B;AACH;AACJ;;AACD6E,EAAAA,wBAAwB,CAACxE,YAAD,EAAe;AACnC,SAAK,MAAMrE,WAAX,IAA0BqE,YAA1B,EAAwC;AACpC,YAAMJ,QAAQ,GAAGjE,WAAW,CAACiE,QAA7B;;AACA,UAAIA,QAAQ,CAACiF,kBAAb,EAAiC;AAC7BjF,QAAAA,QAAQ,CAACiF,kBAAT,CAA4B,KAAKD,gBAAL,CAAsBjJ,WAAtB,CAA5B;AACH;AACJ;AACJ;;AACDiJ,EAAAA,gBAAgB,CAACjJ,WAAD,EAAc;AAC1B,WAAO,KAAKyC,oBAAL,CAA0B1C,kBAA1B,CAA6CC,WAA7C,CAAP;AACH;;AAtDuB;AAwD5B;;;AAAmBqI,qBAAqB,CAACrX,IAAtB;AAAA,mBAAmHqX,qBAAnH,EAtmD4H/d,EAsmD5H,UAA0Jwc,KAA1J,GAtmD4Hxc,EAsmD5H,UAA4KwR,qBAA5K,GAtmD4HxR,EAsmD5H,UAA8MqU,uBAA9M,GAtmD4HrU,EAsmD5H,UAAkPuV,mBAAlP,GAtmD4HvV,EAsmD5H,UAAkRc,EAAE,CAACG,gBAArR;AAAA;AACnB;;;AAAmB8c,qBAAqB,CAACpX,KAAtB,kBAvmD4H3G,EAumD5H;AAAA,SAAuH+d,qBAAvH;AAAA,WAAuHA,qBAAvH;AAAA,cAA0J;AAA1J;;AACnB;AAAA,qDAxmD+I/d,EAwmD/I,mBAA4F+d,qBAA5F,EAA+H,CAAC;AACpH/a,IAAAA,IAAI,EAAE7C,UAD8G;AAEpHyG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF8G,GAAD,CAA/H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE7D,MAAAA,IAAI,EAAEwZ;AAAR,KAAD,EAAkB;AAAExZ,MAAAA,IAAI,EAAEwO;AAAR,KAAlB,EAAmD;AAAExO,MAAAA,IAAI,EAAEqR;AAAR,KAAnD,EAAsF;AAAErR,MAAAA,IAAI,EAAEuS;AAAR,KAAtF,EAAqH;AAAEvS,MAAAA,IAAI,EAAElC,EAAE,CAACG;AAAX,KAArH,CAAP;AAA6J,GAHvM;AAAA;AAKA;AACA;AACA;AACA;;;AACA,MAAM4d,cAAN,CAAqB;AACjB9b,EAAAA,WAAW,CAAC6F,OAAD,EAAUkW,uBAAV,EAAmCd,MAAnC,EAA2Ce,OAA3C,EAAoDrT,MAAM,GAAG,EAA7D,EAAiEsT,qBAAjE,EAAwF;AAC/F;AACA,UAAMhK,OAAO,GAAGpM,OAAO,CAACiR,oBAAR,CAA6BnO,MAA7B,CAAhB;AACAoT,IAAAA,uBAAuB,CAAC/J,2BAAxB,CAAoDC,OAApD,EAH+F,CAI/F;;AACApM,IAAAA,OAAO,CAACoR,qBAAR,GAL+F,CAM/F;;AACAgF,IAAAA,qBAAqB,CAACb,aAAtB,CAAoC,IAAI9G,SAAJ,EAApC,EAAqDrC,OAArD;AACH;;AATgB;AAWrB;;;AAAmB6J,cAAc,CAACnY,IAAf;AAAA,mBAA4GmY,cAA5G,EA5nD4H7e,EA4nD5H,UAA4IiY,YAA5I,GA5nD4HjY,EA4nD5H,UAAqKqU,uBAArK,GA5nD4HrU,EA4nD5H,UAAyMwc,KAAzM,GA5nD4Hxc,EA4nD5H,UAA2N6d,aAA3N,GA5nD4H7d,EA4nD5H,UAAqPoH,gBAArP,MA5nD4HpH,EA4nD5H,UAAkS+d,qBAAlS;AAAA;AACnB;;;AAAmBc,cAAc,CAACI,IAAf,kBA7nD4Hjf,EA6nD5H;AAAA,QAA6G6e;AAA7G;AACnB;;AAAmBA,cAAc,CAACK,IAAf,kBA9nD4Hlf,EA8nD5H;;AACnB;AAAA,qDA/nD+IA,EA+nD/I,mBAA4F6e,cAA5F,EAAwH,CAAC;AAC7G7b,IAAAA,IAAI,EAAEpC;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEoC,MAAAA,IAAI,EAAEiV;AAAR,KAAD,EAAyB;AAAEjV,MAAAA,IAAI,EAAEqR;AAAR,KAAzB,EAA4D;AAAErR,MAAAA,IAAI,EAAEwZ;AAAR,KAA5D,EAA6E;AAAExZ,MAAAA,IAAI,EAAE6a;AAAR,KAA7E,EAAsG;AAAE7a,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACnK/D,QAAAA,IAAI,EAAEtC;AAD6J,OAAD,EAEnK;AACCsC,QAAAA,IAAI,EAAE5C,MADP;AAECwG,QAAAA,IAAI,EAAE,CAACQ,gBAAD;AAFP,OAFmK;AAA/B,KAAtG,EAK3B;AAAEpE,MAAAA,IAAI,EAAE+a;AAAR,KAL2B,CAAP;AAKe,GAPzD;AAAA;AASA;AACA;AACA;AACA;;;AACA,MAAMoB,iBAAN,CAAwB;AACpBpc,EAAAA,WAAW,CAACib,MAAD,EAASc,uBAAT,EAAkClW,OAAlC,EAA2C8C,MAAM,GAAG,EAApD,EAAwDsT,qBAAxD,EAA+E;AACtF;AACA;AACA,UAAMI,eAAe,GAAGD,iBAAiB,CAACE,aAAlB,CAAgC3T,MAAhC,CAAxB,CAHsF,CAItF;;AACA,UAAMsJ,OAAO,GAAGpM,OAAO,CAACiR,oBAAR,CAA6BuF,eAA7B,CAAhB;;AACA,QAAIpK,OAAO,CAACtJ,MAAR,CAAe7H,MAAnB,EAA2B;AACvBib,MAAAA,uBAAuB,CAAC/J,2BAAxB,CAAoDC,OAApD,EADuB,CAEvB;;AACAgK,MAAAA,qBAAqB,CAACb,aAAtB,CAAoC,IAAI7G,WAAJ,CAAgBtC,OAAO,CAAC3L,QAAxB,CAApC,EAAuE2L,OAAvE;AACH;AACJ;;AACmB,SAAbqK,aAAa,CAAC3T,MAAM,GAAG,EAAV,EAAc;AAC9B,WAAOA,MAAM,CAAC5H,MAAP,CAAc,CAACwb,KAAD,EAAQC,MAAR,KAAmBD,KAAK,CAACE,MAAN,CAAaD,MAAb,CAAjC,EAAuD,EAAvD,CAAP;AACH;;AAfmB;AAiBxB;;;AAAmBJ,iBAAiB,CAACzY,IAAlB;AAAA,mBAA+GyY,iBAA/G,EA7pD4Hnf,EA6pD5H,UAAkJwc,KAAlJ,GA7pD4Hxc,EA6pD5H,UAAoKqU,uBAApK,GA7pD4HrU,EA6pD5H,UAAwMiY,YAAxM,GA7pD4HjY,EA6pD5H,UAAiOqH,mBAAjO,MA7pD4HrH,EA6pD5H,UAAiR+d,qBAAjR;AAAA;AACnB;;;AAAmBoB,iBAAiB,CAACF,IAAlB,kBA9pD4Hjf,EA8pD5H;AAAA,QAAgHmf;AAAhH;AACnB;;AAAmBA,iBAAiB,CAACD,IAAlB,kBA/pD4Hlf,EA+pD5H;;AACnB;AAAA,qDAhqD+IA,EAgqD/I,mBAA4Fmf,iBAA5F,EAA2H,CAAC;AAChHnc,IAAAA,IAAI,EAAEpC;AAD0G,GAAD,CAA3H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEoC,MAAAA,IAAI,EAAEwZ;AAAR,KAAD,EAAkB;AAAExZ,MAAAA,IAAI,EAAEqR;AAAR,KAAlB,EAAqD;AAAErR,MAAAA,IAAI,EAAEiV;AAAR,KAArD,EAA6E;AAAEjV,MAAAA,IAAI,EAAE8D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC1I/D,QAAAA,IAAI,EAAEtC;AADoI,OAAD,EAE1I;AACCsC,QAAAA,IAAI,EAAE5C,MADP;AAECwG,QAAAA,IAAI,EAAE,CAACS,mBAAD;AAFP,OAF0I;AAA/B,KAA7E,EAK3B;AAAErE,MAAAA,IAAI,EAAE+a;AAAR,KAL2B,CAAP;AAKe,GAPzD;AAAA;AASA;AACA;AACA;;;AACA,MAAM0B,UAAN,CAAiB;AACb;AACJ;AACA;AACkB,SAAPC,OAAO,CAAChU,MAAM,GAAG,EAAV,EAAcxD,OAAO,GAAG,EAAxB,EAA4B;AACtC,WAAO;AACHyX,MAAAA,QAAQ,EAAEd,cADP;AAEHe,MAAAA,SAAS,EAAE,CACP3H,YADO,EAEPlG,aAFO,EAGP,GAAGrG,MAHI,EAIP,GAAG+T,UAAU,CAACI,kBAAX,CAA8BnU,MAA9B,EAAsCxD,OAAtC,CAJI;AAFR,KAAP;AASH;AACD;AACJ;AACA;;;AACqB,SAAV4X,UAAU,CAACpU,MAAM,GAAG,EAAV,EAAc;AAC3B,WAAO;AACHiU,MAAAA,QAAQ,EAAER,iBADP;AAEHS,MAAAA,SAAS,EAAE,CACP;AACA3H,MAAAA,YAFO,EAGPlG,aAHO,EAIP,GAAGrG,MAJI,EAKP;AACIqU,QAAAA,OAAO,EAAE1Y,mBADb;AAEI2Y,QAAAA,KAAK,EAAE,IAFX;AAGIC,QAAAA,QAAQ,EAAEvU;AAHd,OALO;AAFR,KAAP;AAcH;;AACwB,SAAlBmU,kBAAkB,CAACnU,MAAD,EAASxD,OAAT,EAAkB;AACvC,WAAO,CACH;AACI6X,MAAAA,OAAO,EAAErX,qCADb;AAEIuX,MAAAA,QAAQ,EAAE/X,OAAO,CAACD;AAFtB,KADG,EAKH;AACI8X,MAAAA,OAAO,EAAE3Y,gBADb;AAEI6Y,MAAAA,QAAQ,EAAEvU;AAFd,KALG,EASH;AACIqU,MAAAA,OAAO,EAAE5Y,YADb;AAEI8Y,MAAAA,QAAQ,EAAE/X;AAFd,KATG,EAaH;AACI6X,MAAAA,OAAO,EAAElf,sBADb;AAEIsH,MAAAA,UAAU,EAAEsX,UAAU,CAACS,2BAF3B;AAGIF,MAAAA,KAAK,EAAE,IAHX;AAII5X,MAAAA,IAAI,EAAE,CAACnH,gBAAD;AAJV,KAbG,EAmBH;AACI8e,MAAAA,OAAO,EAAE7e,2BADb;AAEIif,MAAAA,WAAW,EAAE5K;AAFjB,KAnBG,EAuBH;AACIwK,MAAAA,OAAO,EAAE5e,mBADb;AAEIgf,MAAAA,WAAW,EAAElI;AAFjB,KAvBG,CAAP;AA4BH;;AACiC,SAA3BiI,2BAA2B,CAACE,YAAD,EAAe;AAC7C,WAAO,MAAMA,YAAY,CAACC,SAAb,EAAb;AACH;;AAlEY;AAoEjB;;;AAAmBZ,UAAU,CAAC/Y,IAAX;AAAA,mBAAwG+Y,UAAxG;AAAA;AACnB;;;AAAmBA,UAAU,CAACR,IAAX,kBAjvD4Hjf,EAivD5H;AAAA,QAAyGyf;AAAzG;AACnB;;AAAmBA,UAAU,CAACP,IAAX,kBAlvD4Hlf,EAkvD5H;;AACnB;AAAA,qDAnvD+IA,EAmvD/I,mBAA4Fyf,UAA5F,EAAoH,CAAC;AACzGzc,IAAAA,IAAI,EAAEpC;AADmG,GAAD,CAApH;AAAA;AAIA;AACA;AACA;;;AACA,SAAS0f,MAAT,CAAgBlX,OAAhB,EAAyBlB,OAAzB,EAAkC;AAC9B,SAAO,CAACe,MAAD,EAASlE,IAAT,KAAkB;AACrB;AACA;AACA,QAAI,OAAOkB,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,YAAMsa,cAAc,GAAGtX,MAAM,CAACC,cAAP,CAAsB,WAAtB,CAAvB;;AACA,UAAIqX,cAAJ,EAAoB;AAChBhb,QAAAA,yBAAyB;AAC5B;AACJ;;AACD,UAAM8F,IAAI,GAAGrC,qBAAqB,CAACC,MAAM,CAAClG,WAAR,CAAlC;;AACA,QAAI,CAACmB,KAAK,CAACC,OAAN,CAAciF,OAAd,CAAL,EAA6B;AACzBA,MAAAA,OAAO,GAAG,CAACA,OAAD,CAAV;AACH;;AACD,SAAK,MAAMtG,MAAX,IAAqBsG,OAArB,EAA8B;AAC1B,YAAMpG,IAAI,GAAGF,MAAM,CAACE,IAApB;;AACA,UAAI,CAACqI,IAAI,CAACjC,OAAL,CAAapG,IAAb,CAAL,EAAyB;AACrBqI,QAAAA,IAAI,CAACjC,OAAL,CAAapG,IAAb,IAAqB,EAArB;AACH;;AACDqI,MAAAA,IAAI,CAACjC,OAAL,CAAapG,IAAb,EAAmBuJ,IAAnB,CAAwB;AACpB1B,QAAAA,EAAE,EAAE9F,IADgB;AAEpBmD,QAAAA,OAAO,EAAEA,OAAO,IAAI,EAFA;AAGpBlF,QAAAA;AAHoB,OAAxB;AAKH;AACJ,GAxBD;AAyBH;AAED;AACA;AACA;;;AACA,SAASwd,KAAT,CAAetY,OAAf,EAAwB;AACpB,SAAQe,MAAD,IAAY;AACf,UAAMmC,UAAU,GAAGnC,MAAnB;AACA,UAAMoC,IAAI,GAAGrC,qBAAqB,CAACoC,UAAD,CAAlC;AACA,UAAMqV,mBAAmB,GAAGhd,MAAM,CAACid,cAAP,CAAsBtV,UAAtB,CAA5B;AACA,UAAMuV,sBAAsB,GAAGC,eAAe,CAACH,mBAAD,EAAsBvY,OAAtB,CAA9C;AACA2Y,IAAAA,cAAc,CAAC;AAAExV,MAAAA,IAAF;AAAQoV,MAAAA,mBAAR;AAA6BE,MAAAA;AAA7B,KAAD,CAAd;AACAvV,IAAAA,UAAU,CAAC5D,gBAAD,CAAV,GAA+BmZ,sBAA/B;AACH,GAPD;AAQH;;AACD,SAASC,eAAT,CAAyBH,mBAAzB,EAA8CvY,OAA9C,EAAuD;AACnD,QAAM4Y,kBAAkB,GAAGL,mBAAmB,CAACjZ,gBAAD,CAAnB,IAAyC,EAApE;AACA,SAAO/D,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBod,kBAAlB,CAAd,EAAqD5Y,OAArD,CAAP;AACH;;AACD,SAAS2Y,cAAT,CAAwBE,MAAxB,EAAgC;AAC5B,QAAM;AAAE1V,IAAAA,IAAF;AAAQoV,IAAAA,mBAAR;AAA6BE,IAAAA;AAA7B,MAAwDI,MAA9D;AACA,QAAM;AAAErX,IAAAA,QAAF;AAAYL,IAAAA,QAAZ;AAAsBtE,IAAAA;AAAtB,MAA+B4b,sBAArC;AACA,QAAMlK,SAAS,GAAG,OAAO1R,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAmCA,IAAI,IAAIA,IAAI,CAACic,OAAL,EAAT,IAA4B,IAAhF;;AACA,MAAI,OAAO/a,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/CqQ,IAAAA,sBAAsB,CAACG,SAAD,CAAtB;AACH;;AACD,MAAIgK,mBAAmB,CAACvX,cAApB,CAAmC3B,QAAnC,CAAJ,EAAkD;AAC9C,UAAM0Z,aAAa,GAAGR,mBAAmB,CAAClZ,QAAD,CAAnB,IAAiC,EAAvD;AACA8D,IAAAA,IAAI,CAACjC,OAAL,GAAe3F,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB2H,IAAI,CAACjC,OAAvB,CAAd,EAA+C6X,aAAa,CAAC7X,OAA7D,CAAf;AACH;;AACDiC,EAAAA,IAAI,CAAC3B,QAAL,GAAgBA,QAAhB;AACA2B,EAAAA,IAAI,CAAChC,QAAL,GAAgBA,QAAhB;AACAgC,EAAAA,IAAI,CAACtG,IAAL,GAAY0R,SAAZ;AACH;;AAED,MAAMyK,gBAAgB,GAAG,EAAzB;;AACA,SAASC,sBAAT,CAAgC9E,QAAhC,EAA0C;AACtC,MAAI,CAACwB,aAAa,CAACC,KAAnB,EAA0B;AACtBnY,IAAAA,mCAAmC;AACtC;;AACD,SAAOkY,aAAa,CAACC,KAAd,CAAoBd,MAApB,CAA2BX,QAA3B,CAAP;AACH;;AACD,SAAS+E,gBAAT,CAA0Brc,IAA1B,EAAgCsc,WAAhC,EAA6ChX,KAAK,GAAG,EAArD,EAAyD;AACrDgX,EAAAA,WAAW,GAAG,CAACA,WAAD,GAAeC,oBAAoB,CAACvc,IAAD,CAAnC,GAA4Csc,WAA1D;;AACA,MAAI,OAAOA,WAAP,KAAuB,QAA3B,EAAqC;AACjC,UAAME,UAAU,GAAGlX,KAAK,CAACxG,MAAN,GACb,CAACwd,WAAD,EAAc,GAAGhX,KAAjB,CADa,GAEbgX,WAAW,CAAC1d,KAAZ,CAAkB,GAAlB,CAFN;AAGA,WAAOoH,UAAU,CAACwW,UAAD,EAAa1D,aAAa,CAAC7S,MAA3B,CAAjB;AACH;;AACD,SAAOqW,WAAP;AACH;AACD;AACA;AACA;;;AACA,SAASC,oBAAT,CAA8Bvc,IAA9B,EAAoC;AAChC,QAAMyc,aAAa,GAAGzc,IAAI,CAAClB,MAAL,GAAc,CAApC;AACA,QAAM4d,cAAc,GAAG1c,IAAI,CAAC2c,UAAL,CAAgBF,aAAhB,MAAmCN,gBAA1D;AACA,SAAOO,cAAc,GAAG1c,IAAI,CAACX,KAAL,CAAW,CAAX,EAAcod,aAAd,CAAH,GAAkCzc,IAAvD;AACH;AAED;AACA;AACA;;;AACA,SAAS4c,MAAT,CAAgBN,WAAhB,EAA6B,GAAGhX,KAAhC,EAAuC;AACnC,SAAO,UAAUpB,MAAV,EAAkBpE,GAAlB,EAAuB;AAC1B,UAAME,IAAI,GAAGF,GAAG,CAAC+c,QAAJ,EAAb;AACA,UAAMC,UAAU,GAAI,KAAI9c,IAAK,YAA7B;AACA,UAAMsX,QAAQ,GAAG+E,gBAAgB,CAACrc,IAAD,EAAOsc,WAAP,EAAoBhX,KAApB,CAAjC;AACA5G,IAAAA,MAAM,CAACqe,gBAAP,CAAwB7Y,MAAxB,EAAgC;AAC5B,OAAC4Y,UAAD,GAAc;AACVE,QAAAA,QAAQ,EAAE,IADA;AAEVC,QAAAA,UAAU,EAAE,KAFF;AAGVC,QAAAA,YAAY,EAAE;AAHJ,OADc;AAM5B,OAACld,IAAD,GAAQ;AACJid,QAAAA,UAAU,EAAE,IADR;AAEJC,QAAAA,YAAY,EAAE,IAFV;;AAGJnZ,QAAAA,GAAG,GAAG;AACF,iBAAO,KAAK+Y,UAAL,MAAqB,KAAKA,UAAL,IAAmBV,sBAAsB,CAAC9E,QAAD,CAA9D,CAAP;AACH;;AALG;AANoB,KAAhC;AAcH,GAlBD;AAmBH;;AAED,MAAM6F,yBAAyB,GAAG,4BAAlC;AACA,MAAMC,2BAA2B,GAAG;AAChCC,EAAAA,UAAU,EAAGnZ,MAAD,IAAY;AACpB,WAAQA,MAAM,IAAIA,MAAM,CAACiZ,yBAAD,CAAjB,IAAiD,EAAxD;AACH,GAH+B;AAIhCG,EAAAA,aAAa,EAAE,CAACpZ,MAAD,EAASf,OAAT,KAAqB;AAChC,QAAI,CAACe,MAAL,EACI;AACJA,IAAAA,MAAM,CAACiZ,yBAAD,CAAN,GAAoCha,OAApC;AACH;AAR+B,CAApC;;AAUA,SAASoa,qBAAT,CAA+BvY,UAA/B,EAA2C2R,gBAA3C,EAA6D;AACzD,QAAMX,gBAAgB,GAAGjR,wBAAwB,CAACC,UAAD,CAAjD;AACAgR,EAAAA,gBAAgB,CAAChR,UAAjB,GAA8BA,UAA9B;;AACA,MAAIwY,0BAA0B,GAAG,OAAO,EAAP,CAAjC;;AACA,MAAI7G,gBAAJ,EAAsB;AAClBX,IAAAA,gBAAgB,CAAC/Q,cAAjB,GAAkC0R,gBAAgB,CAAC1R,cAAnD;AACA+Q,IAAAA,gBAAgB,CAAC9Q,YAAjB,GAAgCyR,gBAAgB,CAACzR,YAAjB,IAAiC,IAAjE;AACAsY,IAAAA,0BAA0B,GACtB7G,gBAAgB,CAACxR,kBAAjB,IAAuCqY,0BAD3C;AAEH;;AACD,QAAMC,qBAAqB,GAAG/e,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBqX,gBAAlB,CAA9B;;AACAA,EAAAA,gBAAgB,CAAC7Q,kBAAjB,GAAsC,MAAMuY,uBAAuB,CAACD,qBAAD,EAAwBD,0BAA0B,EAAlD,CAAnE;;AACA,SAAOxH,gBAAP;AACH;;AACD,SAAS0H,uBAAT,CAAiC1H,gBAAjC,EAAmD2H,eAAnD,EAAoE;AAChE,SAAOjf,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAmBye,2BAA2B,CAACC,UAA5B,CAAuCrH,gBAAgB,CAAC/Q,cAAxD,KAA2E,EAA9F,CAAd,EAAmHmY,2BAA2B,CAACC,UAA5B,CAAuCrH,gBAAgB,CAAChR,UAAxD,KAAuE,EAA1L,CAAd,EAA+MgR,gBAAgB,CAAC7Q,kBAAjB,MAAyC,EAAxP,CAAd,EAA4QwY,eAA5Q,CAAP;AACH;AAED;AACA;AACA;;;AACA,SAASC,eAAT,CAAyBza,OAAzB,EAAkC;AAC9B,SAAQ,SAAS0a,QAAT,CAAkB3Z,MAAlB,EAA0B4Z,UAA1B,EAAsCC,UAAtC,EAAkD;AACtD,QAAID,UAAJ,EAAgB;AACZC,MAAAA,UAAU,KAAKA,UAAU,GAAGrf,MAAM,CAACsf,wBAAP,CAAgC9Z,MAAhC,EAAwC4Z,UAAxC,CAAlB,CAAV,CADY,CAEZ;;AACA,YAAM9Y,UAAU,GAAG+Y,UAAU,CAAClZ,KAAX,IAAoBkZ,UAAU,CAAC/Y,UAAlD;;AACA,UAAIA,UAAJ,EAAgB;AACZoY,QAAAA,2BAA2B,CAACE,aAA5B,CAA0CtY,UAA1C,EAAsD7B,OAAtD;AACH;AACJ,KAPD,MAQK;AACD;AACAia,MAAAA,2BAA2B,CAACE,aAA5B,CAA0CpZ,MAA1C,EAAkDf,OAAlD;AACH;AACJ,GAbD;AAcH;;AAED,SAAS8a,mBAAT,CAA6B/Z,MAA7B,EAAqC;AACjC,SAAOD,qBAAqB,CAACC,MAAD,CAA5B;AACH;;AACD,SAASga,gBAAT,CAA0Bha,MAA1B,EAAkC;AAC9B,SAAOY,kBAAkB,CAACZ,MAAD,CAAzB;AACH;;AACD,SAASia,sBAAT,CAAgCja,MAAhC,EAAwC;AACpC,SAAOa,wBAAwB,CAACb,MAAD,CAA/B;AACH;;AACD,SAASka,mBAAT,CAA6Bla,MAA7B,EAAqC;AACjC,SAAOkB,qBAAqB,CAAClB,MAAD,CAA5B;AACH;;AAED,SAASma,cAAT,CAAwBpI,SAAxB,EAAmCqI,SAAnC,EAA8C3H,gBAA9C,EAAgE;AAC5D,QAAMM,UAAU,GAAGP,wBAAwB,CAAC4H,SAAD,EAAY3H,gBAAZ,CAA3C;AACA,QAAMX,gBAAgB,GAAGuH,qBAAqB,CAACe,SAAD,EAAY3H,gBAAZ,CAA9C;AACAX,EAAAA,gBAAgB,CAACxR,gBAAjB,GAAoCuR,yBAAyB,CAACC,gBAAD,EAAmBC,SAAnB,EAA8BgB,UAA9B,CAA7D;AACA,SAAOA,UAAP;AACH;;AAED,SAASsH,QAAT,CAAkBtI,SAAlB,EAA6B;AACzB,SAAO,CAAC/R,MAAD,EAASpE,GAAT,EAAcie,UAAd,KAA6B;AAChCA,IAAAA,UAAU,KAAKA,UAAU,GAAGrf,MAAM,CAACsf,wBAAP,CAAgC9Z,MAAhC,EAAwCpE,GAAxC,CAAlB,CAAV;AACA,UAAMkF,UAAU,GAAG+Y,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAAClZ,KAAtF,CAFgC,CAGhC;AACA;;AACA,QAAI,OAAO3D,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,UAAI8D,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAChDvE,QAAAA,2BAA2B;AAC9B;AACJ;;AACD,UAAMwW,UAAU,GAAGoH,cAAc,CAACpI,SAAD,EAAYjR,UAAZ,EAAwB;AACrDC,MAAAA,cAAc,EAAEf,MADqC;AAErDgB,MAAAA,YAAY,EAAEpF,GAAG,CAAC+c,QAAJ,EAFuC;;AAGrD1X,MAAAA,kBAAkB,GAAG;AACjB,eAAO,EAAP;AACH;;AALoD,KAAxB,CAAjC;AAOA,UAAMqZ,aAAa,GAAG;AAClBtB,MAAAA,YAAY,EAAE,IADI;;AAElBnZ,MAAAA,GAAG,GAAG;AACF,eAAOkT,UAAP;AACH;;AAJiB,KAAtB,CAjBgC,CAuBhC;;AACAuH,IAAAA,aAAa,CAAC,YAAD,CAAb,GAA8BxZ,UAA9B;AACA,WAAOwZ,aAAP;AACH,GA1BD;AA2BH;;AAED,MAAMC,UAAN,CAAiB;AACbzgB,EAAAA,WAAW,CAACgC,IAAD,EAAO;AACd,SAAKA,IAAL,GAAYA,IAAZ;AACA,UAAM0e,gBAAgB,GAAG3Z,wBAAwB,CAAC,IAAD,CAAjD;;AACA2Z,IAAAA,gBAAgB,CAACla,gBAAjB,GAAqCkU,cAAD,IAAoB;AACpD,aAAOA,cAAc,CAAChU,cAAf,CAA8B,KAAK1E,IAAnC,CAAP;AACH,KAFD;AAGH;;AACDic,EAAAA,OAAO,GAAG;AACN,WAAO,KAAKjc,IAAZ;AACH;;AACD6c,EAAAA,QAAQ,GAAG;AACP,WAAQ,cAAa,KAAK7c,IAAK,GAA/B;AACH;;AAbY;;AAgBjB,MAAM2e,qBAAN,CAA4B;AACV,SAAPhE,OAAO,CAACxX,OAAD,EAAU;AACpB,WAAO;AACHyX,MAAAA,QAAQ,EAAE+D,qBADP;AAEH9D,MAAAA,SAAS,EAAE,CACPlI,0BADO,EAEP;AAAEqI,QAAAA,OAAO,EAAEvI,wBAAX;AAAqCyI,QAAAA,QAAQ,EAAE/X;AAA/C,OAFO;AAFR,KAAP;AAOH;;AATuB;AAW5B;;;AAAmBwb,qBAAqB,CAAChd,IAAtB;AAAA,mBAAmHgd,qBAAnH;AAAA;AACnB;;;AAAmBA,qBAAqB,CAACzE,IAAtB,kBAz+D4Hjf,EAy+D5H;AAAA,QAAoH0jB;AAApH;AACnB;;AAAmBA,qBAAqB,CAACxE,IAAtB,kBA1+D4Hlf,EA0+D5H;;AACnB;AAAA,qDA3+D+IA,EA2+D/I,mBAA4F0jB,qBAA5F,EAA+H,CAAC;AACpH1gB,IAAAA,IAAI,EAAEpC;AAD8G,GAAD,CAA/H;AAAA;;AAIA,SAAS+iB,mBAAT,CAA6BtH,QAA7B,EAAuC7S,OAAO,GAAG,EAAjD,EAAqD;AACjD,QAAMoa,IAAI,GAAGpa,OAAO,CAACoa,IAAR,IAAgB,UAA7B;AACA,QAAMC,MAAM,GAAGra,OAAO,CAACqa,MAAR,GAAiBra,OAAO,CAACqa,MAAR,GAAiB,IAAlC,GAAyC,EAAxD;AACAC,EAAAA,mBAAmB,CAACzH,QAAD,EAAW;AAAEuH,IAAAA,IAAF;AAAQC,IAAAA,MAAM,EAAEra,OAAO,CAACqa;AAAxB,GAAX,CAAnB;AACA,QAAMzJ,QAAQ,GAAGjQ,qBAAqB,CAACkS,QAAD,CAArB,IAAmCxS,kBAAkB,CAACwS,QAAD,CAAtE;;AACA,MAAI,CAACjC,QAAL,EAAe;AACX,UAAM,IAAIpV,KAAJ,CAAW,GAAE6e,MAAO,6BAA4BD,IAAK,2BAArD,CAAN;AACH;AACJ;;AACD,SAASE,mBAAT,CAA6Bla,KAA7B,EAAoCJ,OAAO,GAAG,EAA9C,EAAkD;AAC9C,QAAMoa,IAAI,GAAGpa,OAAO,CAACoa,IAAR,IAAgB,OAA7B;AACA,QAAMC,MAAM,GAAGra,OAAO,CAACqa,MAAR,GAAiBra,OAAO,CAACqa,MAAR,GAAiB,IAAlC,GAAyC,EAAxD;;AACA,MAAI,CAACja,KAAL,EAAY;AACR,UAAM,IAAI5E,KAAJ,CAAW,GAAE6e,MAAO,KAAID,IAAK,oBAA7B,CAAN;AACH;AACJ;;AAED,SAASG,mBAAT,CAA6BC,WAA7B,EAA0C;AACtC,QAAMC,YAAY,GAAGxgB,MAAM,CAACkJ,IAAP,CAAYqX,WAAZ,CAArB;AACA,QAAMhJ,SAAS,GAAGvX,MAAM,CAAC8b,MAAP,CAAcyE,WAAd,CAAlB;;AACA,MAAI,OAAO/d,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/Cie,IAAAA,sBAAsB,CAAC;AACnBL,MAAAA,MAAM,EAAE,uBADW;AAEnBG,MAAAA,WAFmB;AAGnBC,MAAAA,YAHmB;AAInBjJ,MAAAA;AAJmB,KAAD,CAAtB;AAMH;;AACD,SAAOoI,cAAc,CAACpI,SAAD,EAAY,CAAC,GAAGpU,IAAJ,KAAa;AAC1C,WAAOqd,YAAY,CAACngB,MAAb,CAAoB,CAACR,GAAD,EAAMuB,GAAN,EAAWZ,KAAX,KAAqB;AAC5CX,MAAAA,GAAG,CAACuB,GAAD,CAAH,GAAW+B,IAAI,CAAC3C,KAAD,CAAf;AACA,aAAOX,GAAP;AACH,KAHM,EAGJ,EAHI,CAAP;AAIH,GALoB,CAArB;AAMH;;AACD,SAAS4gB,sBAAT,CAAgC;AAAEL,EAAAA,MAAF;AAAUG,EAAAA,WAAV;AAAuBC,EAAAA,YAAvB;AAAqCjJ,EAAAA;AAArC,CAAhC,EAAkF;AAC9E8I,EAAAA,mBAAmB,CAACE,WAAD,EAAc;AAAEH,IAAAA,MAAF;AAAUD,IAAAA,IAAI,EAAE;AAAhB,GAAd,CAAnB;AACAE,EAAAA,mBAAmB,CAAC,OAAOE,WAAP,KAAuB,QAAxB,EAAkC;AAAEH,IAAAA,MAAF;AAAUD,IAAAA,IAAI,EAAE;AAAhB,GAAlC,CAAnB;AACAE,EAAAA,mBAAmB,CAACG,YAAY,CAACpgB,MAAd,EAAsB;AAAEggB,IAAAA,MAAF;AAAUD,IAAAA,IAAI,EAAE;AAAhB,GAAtB,CAAnB;AACA5I,EAAAA,SAAS,CAACxO,OAAV,CAAkB,CAAC6P,QAAD,EAAWpY,KAAX,KAAqB0f,mBAAmB,CAACtH,QAAD,EAAW;AACjEwH,IAAAA,MADiE;AAEjED,IAAAA,IAAI,EAAG,qBAAoBK,YAAY,CAAChgB,KAAD,CAAQ;AAFkB,GAAX,CAA1D;AAIH;;AAED,SAASkgB,kBAAT,CAA4B9H,QAA5B,EAAsC1P,IAAtC,EAA4C;AACxC,MAAI,OAAO1G,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C0d,IAAAA,mBAAmB,CAACtH,QAAD,EAAW;AAAEwH,MAAAA,MAAM,EAAE;AAAV,KAAX,CAAnB;AACH;;AACD,QAAMO,SAAS,GAAGzX,IAAI,CAAC7K,MAAL,CAAYuiB,OAAZ,CAAlB;AACA,QAAMrJ,SAAS,GAAGoJ,SAAS,CAACriB,GAAV,CAAc8C,GAAG,IAAIue,cAAc,CAAC,CAAC/G,QAAD,CAAD,EAAciI,CAAD,IAAOA,CAAC,CAACzf,GAAD,CAArB,CAAnC,CAAlB;AACA,SAAOue,cAAc,CAAC,CAAC,GAAGpI,SAAJ,CAAD,EAAiB,CAAC,GAAGuJ,KAAJ,KAAc;AAChD,WAAOH,SAAS,CAACtgB,MAAV,CAAiB,CAACC,GAAD,EAAMc,GAAN,EAAWZ,KAAX,KAAqB;AACzCF,MAAAA,GAAG,CAACc,GAAD,CAAH,GAAW0f,KAAK,CAACtgB,KAAD,CAAhB;AACA,aAAOF,GAAP;AACH,KAHM,EAGJ,EAHI,CAAP;AAIH,GALoB,CAArB;AAMH;;AAED,SAASygB,uBAAT,CAAiCC,cAAjC,EAAiD;AAC7C,MAAI,OAAOxe,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C0d,IAAAA,mBAAmB,CAACc,cAAD,EAAiB;AAChCZ,MAAAA,MAAM,EAAE,2BADwB;AAEhCD,MAAAA,IAAI,EAAE;AAF0B,KAAjB,CAAnB;AAIH;;AACD,QAAMc,KAAK,GAAG,EAAd;AACA,SAAO,IAAIC,KAAJ,CAAU,EAAV,EAAc;AACjB7b,IAAAA,GAAG,CAAC8b,OAAD,EAAUrhB,IAAV,EAAgB;AACf,YAAM8Y,QAAQ,GAAGqI,KAAK,CAACnhB,IAAD,CAAL,IACb6f,cAAc,CAAC,CAACqB,cAAD,CAAD,EAAoBH,CAAD,IAAOA,CAAC,KAAK,IAAN,IAAcA,CAAC,KAAK,KAAK,CAAzB,GAA6B,KAAK,CAAlC,GAAsCA,CAAC,CAAC/gB,IAAD,CAAjE,CADlB;AAEAmhB,MAAAA,KAAK,CAACnhB,IAAD,CAAL,GAAc8Y,QAAd;AACA,aAAOA,QAAP;AACH;;AANgB,GAAd,CAAP;AAQH;AAED;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASiE,MAAT,EAAiBjQ,OAAjB,EAA0BgH,SAA1B,EAAqC/P,YAArC,EAAmDoc,qBAAnD,EAA0EjE,UAA1E,EAAsFpX,gBAAtF,EAAwGqP,0BAAxG,EAAoIjP,yBAApI,EAA+JkZ,MAA/J,EAAuK2B,QAAvK,EAAiLX,eAAjL,EAAkMnC,KAAlM,EAAyM1O,WAAzM,EAAsN0R,UAAtN,EAAkOhH,KAAlO,EAAyOlF,WAAzO,EAAsPrU,aAAtP,EAAqQ8gB,mBAArQ,EAA0RI,kBAA1R,EAA8SK,uBAA9S,EAAuUpB,cAAvU,EAAuVF,sBAAvV,EAA+WF,mBAA/W,EAAoYngB,yBAApY,EAA+ZsgB,mBAA/Z,EAAobF,gBAApb,EAAsc5e,QAAtc,EAAgd0I,QAAhd,EAA0dK,gBAA1d,EAA4eC,iBAA5e,EAA+fH,kBAA/f,EAAmhBM,eAAnhB,EAAoiBL,kBAApiB,EAAwjB9J,QAAxjB,EAAkkB8b,iBAAiB,IAAI0F,kBAAvlB,EAA2mBhG,cAAc,IAAIiG,eAA7nB", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { <PERSON><PERSON><PERSON>, PLA<PERSON>ORM_ID, Injectable, Inject, InjectionToken, inject, INJECTO<PERSON>, ɵglobal, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Optional, SkipSelf, NgModule, APP_BOOTSTRAP_LISTENER } from '@angular/core';\nimport * as i5 from '@ngxs/store/internals';\nimport { memoize, INITIAL_STATE_TOKEN, NgxsBootstrapper, ɵNGXS_STATE_CONTEXT_FACTORY, ɵNGXS_STATE_FACTORY } from '@ngxs/store/internals';\nimport { isPlatformServer } from '@angular/common';\nimport { Observable, Subject, BehaviorSubject, of, forkJoin, throwError, EMPTY, from, isObservable } from 'rxjs';\nimport { filter, map, share, shareReplay, take, exhaustMap, mergeMap, defaultIfEmpty, catchError, takeUntil, distinctUntilChanged, tap, startWith, pairwise } from 'rxjs/operators';\nimport { isStateOperator } from '@ngxs/store/operators';\n\n/**\n * Returns the type from an action instance/class.\n * @ignore\n */\nfunction getActionTypeFromInstance(action) {\n    if (action.constructor && action.constructor.type) {\n        return action.constructor.type;\n    }\n    else {\n        return action.type;\n    }\n}\n/**\n * Matches a action\n * @ignore\n */\nfunction actionMatcher(action1) {\n    const type1 = getActionTypeFromInstance(action1);\n    return function (action2) {\n        return type1 === getActionTypeFromInstance(action2);\n    };\n}\n/**\n * Set a deeply nested value. Example:\n *\n *   setValue({ foo: { bar: { eat: false } } },\n *      'foo.bar.eat', true) //=> { foo: { bar: { eat: true } } }\n *\n * While it traverses it also creates new objects from top down.\n *\n * @ignore\n */\nconst setValue = (obj, prop, val) => {\n    obj = Object.assign({}, obj);\n    const split = prop.split('.');\n    const lastIndex = split.length - 1;\n    split.reduce((acc, part, index) => {\n        if (index === lastIndex) {\n            acc[part] = val;\n        }\n        else {\n            acc[part] = Array.isArray(acc[part]) ? acc[part].slice() : Object.assign({}, acc[part]);\n        }\n        return acc && acc[part];\n    }, obj);\n    return obj;\n};\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * @ignore\n */\nconst getValue = (obj, prop) => prop.split('.').reduce((acc, part) => acc && acc[part], obj);\n/**\n * Simple object check.\n *\n *    isObject({a:1}) //=> true\n *    isObject(1) //=> false\n *\n * @ignore\n */\nconst isObject$1 = (item) => {\n    return item && typeof item === 'object' && !Array.isArray(item);\n};\n/**\n * Deep merge two objects.\n *\n *    mergeDeep({a:1, b:{x: 1, y:2}}, {b:{x: 3}, c:4}) //=> {a:1, b:{x:3, y:2}, c:4}\n *\n * @param base base object onto which `sources` will be applied\n */\nconst mergeDeep = (base, ...sources) => {\n    if (!sources.length)\n        return base;\n    const source = sources.shift();\n    if (isObject$1(base) && isObject$1(source)) {\n        for (const key in source) {\n            if (isObject$1(source[key])) {\n                if (!base[key])\n                    Object.assign(base, { [key]: {} });\n                mergeDeep(base[key], source[key]);\n            }\n            else {\n                Object.assign(base, { [key]: source[key] });\n            }\n        }\n    }\n    return mergeDeep(base, ...sources);\n};\n\nfunction throwStateNameError(name) {\n    throw new Error(`${name} is not a valid state name. It needs to be a valid object property name.`);\n}\nfunction throwStateNamePropertyError() {\n    throw new Error(`States must register a 'name' property.`);\n}\nfunction throwStateUniqueError(current, newName, oldName) {\n    throw new Error(`State name '${current}' from ${newName} already exists in ${oldName}.`);\n}\nfunction throwStateDecoratorError(name) {\n    throw new Error(`States must be decorated with @State() decorator, but \"${name}\" isn't.`);\n}\nfunction throwActionDecoratorError() {\n    throw new Error('@Action() decorator cannot be used with static methods.');\n}\nfunction throwSelectorDecoratorError() {\n    throw new Error('Selectors only work on methods.');\n}\nfunction getZoneWarningMessage() {\n    return ('Your application was bootstrapped with nooped zone and your execution strategy requires an actual NgZone!\\n' +\n        'Please set the value of the executionStrategy property to NoopNgxsExecutionStrategy.\\n' +\n        'NgxsModule.forRoot(states, { executionStrategy: NoopNgxsExecutionStrategy })');\n}\nfunction getUndecoratedStateInIvyWarningMessage(name) {\n    return `'${name}' class should be decorated with @Injectable() right after the @State() decorator`;\n}\nfunction throwSelectFactoryNotConnectedError() {\n    throw new Error('You have forgotten to import the NGXS module!');\n}\nfunction throwPatchingArrayError() {\n    throw new Error('Patching arrays is not supported.');\n}\nfunction throwPatchingPrimitiveError() {\n    throw new Error('Patching primitives is not supported.');\n}\n\nclass DispatchOutsideZoneNgxsExecutionStrategy {\n    constructor(_ngZone, _platformId) {\n        this._ngZone = _ngZone;\n        this._platformId = _platformId;\n        // Caretaker note: we have still left the `typeof` condition in order to avoid\n        // creating a breaking change for projects that still use the View Engine.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            verifyZoneIsNotNooped(_ngZone);\n        }\n    }\n    enter(func) {\n        if (isPlatformServer(this._platformId)) {\n            return this.runInsideAngular(func);\n        }\n        return this.runOutsideAngular(func);\n    }\n    leave(func) {\n        return this.runInsideAngular(func);\n    }\n    runInsideAngular(func) {\n        if (NgZone.isInAngularZone()) {\n            return func();\n        }\n        return this._ngZone.run(func);\n    }\n    runOutsideAngular(func) {\n        if (NgZone.isInAngularZone()) {\n            return this._ngZone.runOutsideAngular(func);\n        }\n        return func();\n    }\n}\n/** @nocollapse */ DispatchOutsideZoneNgxsExecutionStrategy.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DispatchOutsideZoneNgxsExecutionStrategy, deps: [{ token: i0.NgZone }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ DispatchOutsideZoneNgxsExecutionStrategy.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DispatchOutsideZoneNgxsExecutionStrategy, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: DispatchOutsideZoneNgxsExecutionStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n// Caretaker note: this should exist as a separate function and not a class method,\n// since class methods are not tree-shakable.\nfunction verifyZoneIsNotNooped(ngZone) {\n    // `NoopNgZone` is not exposed publicly as it doesn't expect\n    // to be used outside of the core Angular code, thus we just have\n    // to check if the zone doesn't extend or instanceof `NgZone`.\n    if (ngZone instanceof NgZone) {\n        return;\n    }\n    console.warn(getZoneWarningMessage());\n}\n\nconst ROOT_OPTIONS = new InjectionToken('ROOT_OPTIONS');\nconst ROOT_STATE_TOKEN = new InjectionToken('ROOT_STATE_TOKEN');\nconst FEATURE_STATE_TOKEN = new InjectionToken('FEATURE_STATE_TOKEN');\nconst NGXS_PLUGINS = new InjectionToken('NGXS_PLUGINS');\nconst META_KEY = 'NGXS_META';\nconst META_OPTIONS_KEY = 'NGXS_OPTIONS_META';\nconst SELECTOR_META_KEY = 'NGXS_SELECTOR_META';\n/**\n * The NGXS config settings.\n */\nclass NgxsConfig {\n    constructor() {\n        /**\n         * Defining the default state before module initialization\n         * This is convenient if we need to create a define our own set of states.\n         * @deprecated will be removed after v4\n         * (default: {})\n         */\n        this.defaultsState = {};\n        /**\n         * Defining shared selector options\n         */\n        this.selectorOptions = {\n            injectContainerState: true,\n            suppressErrors: true // TODO: default is true in v3, will change in v4\n        };\n        this.compatibility = {\n            strictContentSecurityPolicy: false\n        };\n        this.executionStrategy = DispatchOutsideZoneNgxsExecutionStrategy;\n    }\n}\n/** @nocollapse */ NgxsConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NgxsConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsConfig, providedIn: 'root', useFactory: (options) => mergeDeep(new NgxsConfig(), options), deps: [{ token: ROOT_OPTIONS }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: (options) => mergeDeep(new NgxsConfig(), options),\n                    deps: [ROOT_OPTIONS]\n                }]\n        }], ctorParameters: function () { return []; } });\n/**\n * Represents a basic change from a previous to a new value for a single state instance.\n * Passed as a value in a NgxsSimpleChanges object to the ngxsOnChanges hook.\n */\nclass NgxsSimpleChange {\n    constructor(previousValue, currentValue, firstChange) {\n        this.previousValue = previousValue;\n        this.currentValue = currentValue;\n        this.firstChange = firstChange;\n    }\n}\n\nclass NoopNgxsExecutionStrategy {\n    enter(func) {\n        return func();\n    }\n    leave(func) {\n        return func();\n    }\n}\n/** @nocollapse */ NoopNgxsExecutionStrategy.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NoopNgxsExecutionStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NoopNgxsExecutionStrategy.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NoopNgxsExecutionStrategy, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NoopNgxsExecutionStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * The strategy that might be provided by users through `options.executionStrategy`.\n */\nconst USER_PROVIDED_NGXS_EXECUTION_STRATEGY = new InjectionToken('USER_PROVIDED_NGXS_EXECUTION_STRATEGY');\n/*\n * Internal execution strategy injection token\n */\nconst NGXS_EXECUTION_STRATEGY = new InjectionToken('NGXS_EXECUTION_STRATEGY', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(INJECTOR);\n        const executionStrategy = injector.get(USER_PROVIDED_NGXS_EXECUTION_STRATEGY);\n        return executionStrategy\n            ? injector.get(executionStrategy)\n            : injector.get(typeof ɵglobal.Zone !== 'undefined'\n                ? DispatchOutsideZoneNgxsExecutionStrategy\n                : NoopNgxsExecutionStrategy);\n    }\n});\n\n/**\n * Ensures metadata is attached to the class and returns it.\n *\n * @ignore\n */\nfunction ensureStoreMetadata$1(target) {\n    if (!target.hasOwnProperty(META_KEY)) {\n        const defaultMetadata = {\n            name: null,\n            actions: {},\n            defaults: {},\n            path: null,\n            makeRootSelector(context) {\n                return context.getStateGetter(defaultMetadata.name);\n            },\n            children: []\n        };\n        Object.defineProperty(target, META_KEY, { value: defaultMetadata });\n    }\n    return getStoreMetadata$1(target);\n}\n/**\n * Get the metadata attached to the state class if it exists.\n *\n * @ignore\n */\nfunction getStoreMetadata$1(target) {\n    return target[META_KEY];\n}\n/**\n * Ensures metadata is attached to the selector and returns it.\n *\n * @ignore\n */\nfunction ensureSelectorMetadata$1(target) {\n    if (!target.hasOwnProperty(SELECTOR_META_KEY)) {\n        const defaultMetadata = {\n            makeRootSelector: null,\n            originalFn: null,\n            containerClass: null,\n            selectorName: null,\n            getSelectorOptions: () => ({})\n        };\n        Object.defineProperty(target, SELECTOR_META_KEY, { value: defaultMetadata });\n    }\n    return getSelectorMetadata$1(target);\n}\n/**\n * Get the metadata attached to the selector if it exists.\n *\n * @ignore\n */\nfunction getSelectorMetadata$1(target) {\n    return target[SELECTOR_META_KEY];\n}\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * Note: This is not as fast as the `fastPropGetter` but is strict Content Security Policy compliant.\n * See perf hit: https://jsperf.com/fast-value-getter-given-path/1\n *\n * @ignore\n */\nfunction compliantPropGetter(paths) {\n    const copyOfPaths = paths.slice();\n    return obj => copyOfPaths.reduce((acc, part) => acc && acc[part], obj);\n}\n/**\n * The generated function is faster than:\n * - pluck (Observable operator)\n * - memoize\n *\n * @ignore\n */\nfunction fastPropGetter(paths) {\n    const segments = paths;\n    let seg = 'store.' + segments[0];\n    let i = 0;\n    const l = segments.length;\n    let expr = seg;\n    while (++i < l) {\n        expr = expr + ' && ' + (seg = seg + '.' + segments[i]);\n    }\n    const fn = new Function('store', 'return ' + expr + ';');\n    return fn;\n}\n/**\n * Get a deeply nested value. Example:\n *\n *    getValue({ foo: bar: [] }, 'foo.bar') //=> []\n *\n * @ignore\n */\nfunction propGetter(paths, config) {\n    if (config && config.compatibility && config.compatibility.strictContentSecurityPolicy) {\n        return compliantPropGetter(paths);\n    }\n    else {\n        return fastPropGetter(paths);\n    }\n}\n/**\n * Given an array of states, it will return a object graph. Example:\n *    const states = [\n *      Cart,\n *      CartSaved,\n *      CartSavedItems\n *    ]\n *\n * would return:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * @ignore\n */\nfunction buildGraph(stateClasses) {\n    const findName = (stateClass) => {\n        const meta = stateClasses.find(g => g === stateClass);\n        // Caretaker note: we have still left the `typeof` condition in order to avoid\n        // creating a breaking change for projects that still use the View Engine.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !meta) {\n            throw new Error(`Child state not found: ${stateClass}. \\r\\nYou may have forgotten to add states to module`);\n        }\n        return meta[META_KEY].name;\n    };\n    return stateClasses.reduce((result, stateClass) => {\n        const { name, children } = stateClass[META_KEY];\n        result[name] = (children || []).map(findName);\n        return result;\n    }, {});\n}\n/**\n * Given a states array, returns object graph\n * returning the name and state metadata. Example:\n *\n *  const graph = {\n *    cart: { metadata }\n *  };\n *\n * @ignore\n */\nfunction nameToState(states) {\n    return states.reduce((result, stateClass) => {\n        const meta = stateClass[META_KEY];\n        result[meta.name] = stateClass;\n        return result;\n    }, {});\n}\n/**\n * Given a object relationship graph will return the full path\n * for the child items. Example:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * would return:\n *\n *  const r = {\n *    cart: 'cart',\n *    saved: 'cart.saved',\n *    items: 'cart.saved.items'\n *  };\n *\n * @ignore\n */\nfunction findFullParentPath(obj, newObj = {}) {\n    const visit = (child, keyToFind) => {\n        for (const key in child) {\n            if (child.hasOwnProperty(key) && child[key].indexOf(keyToFind) >= 0) {\n                const parent = visit(child, key);\n                return parent !== null ? `${parent}.${key}` : key;\n            }\n        }\n        return null;\n    };\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            const parent = visit(obj, key);\n            newObj[key] = parent ? `${parent}.${key}` : key;\n        }\n    }\n    return newObj;\n}\n/**\n * Given a object graph, it will return the items topologically sorted Example:\n *\n *  const graph = {\n *    cart: ['saved'],\n *    saved: ['items'],\n *    items: []\n *  };\n *\n * would return:\n *\n *  const results = [\n *    'items',\n *    'saved',\n *    'cart'\n *  ];\n *\n * @ignore\n */\nfunction topologicalSort(graph) {\n    const sorted = [];\n    const visited = {};\n    const visit = (name, ancestors = []) => {\n        if (!Array.isArray(ancestors)) {\n            ancestors = [];\n        }\n        ancestors.push(name);\n        visited[name] = true;\n        graph[name].forEach((dep) => {\n            // Caretaker note: we have still left the `typeof` condition in order to avoid\n            // creating a breaking change for projects that still use the View Engine.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && ancestors.indexOf(dep) >= 0) {\n                throw new Error(`Circular dependency '${dep}' is required by '${name}': ${ancestors.join(' -> ')}`);\n            }\n            if (visited[dep]) {\n                return;\n            }\n            visit(dep, ancestors.slice(0));\n        });\n        if (sorted.indexOf(name) < 0) {\n            sorted.push(name);\n        }\n    };\n    Object.keys(graph).forEach(k => visit(k));\n    return sorted.reverse();\n}\n/**\n * Returns if the parameter is a object or not.\n *\n * @ignore\n */\nfunction isObject(obj) {\n    return (typeof obj === 'object' && obj !== null) || typeof obj === 'function';\n}\n\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will grab actions that have just been dispatched as well as actions that have completed\n */\nfunction ofAction(...allowedTypes) {\n    return ofActionOperator(allowedTypes);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been dispatched\n */\nfunction ofActionDispatched(...allowedTypes) {\n    return ofActionOperator(allowedTypes, [\"DISPATCHED\" /* Dispatched */]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been successfully completed\n */\nfunction ofActionSuccessful(...allowedTypes) {\n    return ofActionOperator(allowedTypes, [\"SUCCESSFUL\" /* Successful */]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been canceled\n */\nfunction ofActionCanceled(...allowedTypes) {\n    return ofActionOperator(allowedTypes, [\"CANCELED\" /* Canceled */]);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just been completed\n */\nfunction ofActionCompleted(...allowedTypes) {\n    const allowedStatuses = [\n        \"SUCCESSFUL\" /* Successful */,\n        \"CANCELED\" /* Canceled */,\n        \"ERRORED\" /* Errored */\n    ];\n    return ofActionOperator(allowedTypes, allowedStatuses, mapActionResult);\n}\n/**\n * RxJS operator for selecting out specific actions.\n *\n * This will ONLY grab actions that have just thrown an error\n */\nfunction ofActionErrored(...allowedTypes) {\n    return ofActionOperator(allowedTypes, [\"ERRORED\" /* Errored */]);\n}\nfunction ofActionOperator(allowedTypes, statuses, \n// This actually could've been `OperatorFunction<ActionContext, ActionCompletion | any>`,\n// since it maps either to `ctx.action` OR to `ActionCompletion`. But `ActionCompleteion | any`\n// defaults to `any`, thus there is no sense from union type.\nmapOperator = mapAction) {\n    const allowedMap = createAllowedActionTypesMap(allowedTypes);\n    const allowedStatusMap = statuses && createAllowedStatusesMap(statuses);\n    return function (o) {\n        return o.pipe(filterStatus(allowedMap, allowedStatusMap), mapOperator());\n    };\n}\nfunction filterStatus(allowedTypes, allowedStatuses) {\n    return filter((ctx) => {\n        const actionType = getActionTypeFromInstance(ctx.action);\n        const typeMatch = allowedTypes[actionType];\n        const statusMatch = allowedStatuses ? allowedStatuses[ctx.status] : true;\n        return typeMatch && statusMatch;\n    });\n}\nfunction mapActionResult() {\n    return map(({ action, status, error }) => {\n        return {\n            action,\n            result: {\n                successful: \"SUCCESSFUL\" /* Successful */ === status,\n                canceled: \"CANCELED\" /* Canceled */ === status,\n                error\n            }\n        };\n    });\n}\nfunction mapAction() {\n    return map((ctx) => ctx.action);\n}\nfunction createAllowedActionTypesMap(types) {\n    return types.reduce((filterMap, klass) => {\n        filterMap[getActionTypeFromInstance(klass)] = true;\n        return filterMap;\n    }, {});\n}\nfunction createAllowedStatusesMap(statuses) {\n    return statuses.reduce((filterMap, status) => {\n        filterMap[status] = true;\n        return filterMap;\n    }, {});\n}\n\n/**\n * Returns operator that will run\n * `subscribe` outside of the ngxs execution context\n */\nfunction leaveNgxs(ngxsExecutionStrategy) {\n    return (source) => {\n        return new Observable((sink) => {\n            return source.subscribe({\n                next(value) {\n                    ngxsExecutionStrategy.leave(() => sink.next(value));\n                },\n                error(error) {\n                    ngxsExecutionStrategy.leave(() => sink.error(error));\n                },\n                complete() {\n                    ngxsExecutionStrategy.leave(() => sink.complete());\n                }\n            });\n        });\n    };\n}\n\nclass InternalNgxsExecutionStrategy {\n    constructor(_executionStrategy) {\n        this._executionStrategy = _executionStrategy;\n    }\n    enter(func) {\n        return this._executionStrategy.enter(func);\n    }\n    leave(func) {\n        return this._executionStrategy.leave(func);\n    }\n}\n/** @nocollapse */ InternalNgxsExecutionStrategy.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalNgxsExecutionStrategy, deps: [{ token: NGXS_EXECUTION_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalNgxsExecutionStrategy.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalNgxsExecutionStrategy, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalNgxsExecutionStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGXS_EXECUTION_STRATEGY]\n                }] }]; } });\n\n/**\n * This wraps the provided function, and will enforce the following:\n * - The calls will execute in the order that they are made\n * - A call will only be initiated when the previous call has completed\n * - If there is a call currently executing then the new call will be added\n *   to the queue and the function will return immediately\n *\n * NOTE: The following assumptions about the operation must hold true:\n * - The operation is synchronous in nature\n * - If any asynchronous side effects of the call exist, it should not\n *   have any bearing on the correctness of the next call in the queue\n * - The operation has a void return\n * - The caller should not assume that the call has completed upon\n *   return of the function\n * - The caller can assume that all the queued calls will complete\n *   within the current microtask\n * - The only way that a call will encounter another call in the queue\n *   would be if the call at the front of the queue initiated this call\n *   as part of its synchronous execution\n */\nfunction orderedQueueOperation(operation) {\n    const callsQueue = [];\n    let busyPushingNext = false;\n    return function callOperation(...args) {\n        if (busyPushingNext) {\n            callsQueue.unshift(args);\n            return;\n        }\n        busyPushingNext = true;\n        operation(...args);\n        while (callsQueue.length > 0) {\n            const nextCallArgs = callsQueue.pop();\n            nextCallArgs && operation(...nextCallArgs);\n        }\n        busyPushingNext = false;\n    };\n}\n/**\n * Custom Subject that ensures that subscribers are notified of values in the order that they arrived.\n * A standard Subject does not have this guarantee.\n * For example, given the following code:\n * ```typescript\n *   const subject = new Subject<string>();\n     subject.subscribe(value => {\n       if (value === 'start') subject.next('end');\n     });\n     subject.subscribe(value => { });\n     subject.next('start');\n * ```\n * When `subject` is a standard `Subject<T>` the second subscriber would recieve `end` and then `start`.\n * When `subject` is a `OrderedSubject<T>` the second subscriber would recieve `start` and then `end`.\n */\nclass OrderedSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this._orderedNext = orderedQueueOperation((value) => super.next(value));\n    }\n    next(value) {\n        this._orderedNext(value);\n    }\n}\n/**\n * Custom BehaviorSubject that ensures that subscribers are notified of values in the order that they arrived.\n * A standard BehaviorSubject does not have this guarantee.\n * For example, given the following code:\n * ```typescript\n *   const subject = new BehaviorSubject<string>();\n     subject.subscribe(value => {\n       if (value === 'start') subject.next('end');\n     });\n     subject.subscribe(value => { });\n     subject.next('start');\n * ```\n * When `subject` is a standard `BehaviorSubject<T>` the second subscriber would recieve `end` and then `start`.\n * When `subject` is a `OrderedBehaviorSubject<T>` the second subscriber would recieve `start` and then `end`.\n */\nclass OrderedBehaviorSubject extends BehaviorSubject {\n    constructor(value) {\n        super(value);\n        this._orderedNext = orderedQueueOperation((value) => super.next(value));\n        this._currentValue = value;\n    }\n    getValue() {\n        return this._currentValue;\n    }\n    next(value) {\n        this._currentValue = value;\n        this._orderedNext(value);\n    }\n}\n\n/**\n * Internal Action stream that is emitted anytime an action is dispatched.\n */\nclass InternalActions extends OrderedSubject {\n    ngOnDestroy() {\n        this.complete();\n    }\n}\n/** @nocollapse */ InternalActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalActions, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalActions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalActions, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalActions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * Action stream that is emitted anytime an action is dispatched.\n *\n * You can listen to this in services to react without stores.\n */\nclass Actions extends Observable {\n    constructor(internalActions$, internalExecutionStrategy) {\n        const sharedInternalActions$ = internalActions$.pipe(leaveNgxs(internalExecutionStrategy), \n        // The `InternalActions` subject emits outside of the Angular zone.\n        // We have to re-enter the Angular zone for any incoming consumer.\n        // The `share()` operator reduces the number of change detections.\n        // This would call leave only once for any stream emission across all active subscribers.\n        share());\n        super(observer => {\n            const childSubscription = sharedInternalActions$.subscribe({\n                next: ctx => observer.next(ctx),\n                error: error => observer.error(error),\n                complete: () => observer.complete()\n            });\n            observer.add(childSubscription);\n        });\n    }\n}\n/** @nocollapse */ Actions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Actions, deps: [{ token: InternalActions }, { token: InternalNgxsExecutionStrategy }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ Actions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Actions, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Actions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InternalActions }, { type: InternalNgxsExecutionStrategy }]; } });\n\n/**\n * Composes a array of functions from left to right. Example:\n *\n *      compose([fn, final])(state, action);\n *\n * then the funcs have a signature like:\n *\n *      function fn (state, action, next) {\n *          console.log('here', state, action, next);\n *          return next(state, action);\n *      }\n *\n *      function final (state, action) {\n *          console.log('here', state, action);\n *          return state;\n *      }\n *\n * the last function should not call `next`.\n *\n * @ignore\n */\nconst compose = (funcs) => (...args) => {\n    const curr = funcs.shift();\n    return curr(...args, (...nextArgs) => compose(funcs)(...nextArgs));\n};\n\n/**\n * This operator is used for piping the observable result\n * from the `dispatch()`. It has a \"smart\" error handling\n * strategy that allows us to decide whether we propagate\n * errors to Angular's `ErrorHandler` or enable users to\n * handle them manually. We consider following cases:\n * 1) `store.dispatch()` (no subscribe) -> call `handleError()`\n * 2) `store.dispatch().subscribe()` (no error callback) -> call `handleError()`\n * 3) `store.dispatch().subscribe({ error: ... })` -> don't call `handleError()`\n * 4) `toPromise()` without `catch` -> do `handleError()`\n * 5) `toPromise()` with `catch` -> don't `handleError()`\n */\nfunction ngxsErrorHandler(internalErrorReporter, ngxsExecutionStrategy) {\n    return (source) => {\n        let subscribed = false;\n        source.subscribe({\n            error: error => {\n                // Do not trigger change detection for a microtask. This depends on the execution\n                // strategy being used, but the default `DispatchOutsideZoneNgxsExecutionStrategy`\n                // leaves the Angular zone.\n                ngxsExecutionStrategy.enter(() => Promise.resolve().then(() => {\n                    if (!subscribed) {\n                        ngxsExecutionStrategy.leave(() => internalErrorReporter.reportErrorSafely(error));\n                    }\n                }));\n            }\n        });\n        return new Observable(subscriber => {\n            subscribed = true;\n            return source.pipe(leaveNgxs(ngxsExecutionStrategy)).subscribe(subscriber);\n        });\n    };\n}\nclass InternalErrorReporter {\n    constructor(_injector) {\n        this._injector = _injector;\n        /** Will be set lazily to be backward compatible. */\n        this._errorHandler = null;\n    }\n    reportErrorSafely(error) {\n        if (this._errorHandler === null) {\n            this._errorHandler = this._injector.get(ErrorHandler);\n        }\n        // The `try-catch` is used to avoid handling the error twice. Suppose we call\n        // `handleError` which re-throws the error internally. The re-thrown error will\n        // be caught by zone.js which will then get to the `zone.onError.emit()` and the\n        // `onError` subscriber will call `handleError` again.\n        try {\n            this._errorHandler.handleError(error);\n        }\n        catch (_a) { }\n    }\n}\n/** @nocollapse */ InternalErrorReporter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalErrorReporter, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalErrorReporter.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalErrorReporter, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalErrorReporter, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.Injector }]; } });\n\n/**\n * BehaviorSubject of the entire state.\n * @ignore\n */\nclass StateStream extends OrderedBehaviorSubject {\n    constructor() {\n        super({});\n    }\n    ngOnDestroy() {\n        // The `StateStream` should never emit values once the root view is removed, e.g. when the `NgModuleRef.destroy()` is called.\n        // This will eliminate memory leaks in server-side rendered apps where the `StateStream` is created per each HTTP request, users\n        // might forget to unsubscribe from `store.select` or `store.subscribe`, thus this will lead to huge memory leaks in SSR apps.\n        this.complete();\n    }\n}\n/** @nocollapse */ StateStream.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateStream, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ StateStream.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateStream, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateStream, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return []; } });\n\nclass PluginManager {\n    constructor(_parentManager, _pluginHandlers) {\n        this._parentManager = _parentManager;\n        this._pluginHandlers = _pluginHandlers;\n        this.plugins = [];\n        this.registerHandlers();\n    }\n    get rootPlugins() {\n        return (this._parentManager && this._parentManager.plugins) || this.plugins;\n    }\n    registerHandlers() {\n        const pluginHandlers = this.getPluginHandlers();\n        this.rootPlugins.push(...pluginHandlers);\n    }\n    getPluginHandlers() {\n        const handlers = this._pluginHandlers || [];\n        return handlers.map((plugin) => (plugin.handle ? plugin.handle.bind(plugin) : plugin));\n    }\n}\n/** @nocollapse */ PluginManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: PluginManager, deps: [{ token: PluginManager, optional: true, skipSelf: true }, { token: NGXS_PLUGINS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ PluginManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: PluginManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: PluginManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: PluginManager, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGXS_PLUGINS]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * Internal Action result stream that is emitted when an action is completed.\n * This is used as a method of returning the action result to the dispatcher\n * for the observable returned by the dispatch(...) call.\n * The dispatcher then asynchronously pushes the result from this stream onto the main action stream as a result.\n */\nclass InternalDispatchedActionResults extends Subject {\n}\n/** @nocollapse */ InternalDispatchedActionResults.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatchedActionResults, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalDispatchedActionResults.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatchedActionResults, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatchedActionResults, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass InternalDispatcher {\n    constructor(_actions, _actionResults, _pluginManager, _stateStream, _ngxsExecutionStrategy, _internalErrorReporter) {\n        this._actions = _actions;\n        this._actionResults = _actionResults;\n        this._pluginManager = _pluginManager;\n        this._stateStream = _stateStream;\n        this._ngxsExecutionStrategy = _ngxsExecutionStrategy;\n        this._internalErrorReporter = _internalErrorReporter;\n    }\n    /**\n     * Dispatches event(s).\n     */\n    dispatch(actionOrActions) {\n        const result = this._ngxsExecutionStrategy.enter(() => this.dispatchByEvents(actionOrActions));\n        return result.pipe(ngxsErrorHandler(this._internalErrorReporter, this._ngxsExecutionStrategy));\n    }\n    dispatchByEvents(actionOrActions) {\n        if (Array.isArray(actionOrActions)) {\n            if (actionOrActions.length === 0)\n                return of(this._stateStream.getValue());\n            return forkJoin(actionOrActions.map(action => this.dispatchSingle(action)));\n        }\n        else {\n            return this.dispatchSingle(actionOrActions);\n        }\n    }\n    dispatchSingle(action) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const type = getActionTypeFromInstance(action);\n            if (!type) {\n                const error = new Error(`This action doesn't have a type property: ${action.constructor.name}`);\n                return throwError(error);\n            }\n        }\n        const prevState = this._stateStream.getValue();\n        const plugins = this._pluginManager.plugins;\n        return compose([\n            ...plugins,\n            (nextState, nextAction) => {\n                if (nextState !== prevState) {\n                    this._stateStream.next(nextState);\n                }\n                const actionResult$ = this.getActionResultStream(nextAction);\n                actionResult$.subscribe(ctx => this._actions.next(ctx));\n                this._actions.next({ action: nextAction, status: \"DISPATCHED\" /* Dispatched */ });\n                return this.createDispatchObservable(actionResult$);\n            }\n        ])(prevState, action).pipe(shareReplay());\n    }\n    getActionResultStream(action) {\n        return this._actionResults.pipe(filter((ctx) => ctx.action === action && ctx.status !== \"DISPATCHED\" /* Dispatched */), take(1), shareReplay());\n    }\n    createDispatchObservable(actionResult$) {\n        return actionResult$\n            .pipe(exhaustMap((ctx) => {\n            switch (ctx.status) {\n                case \"SUCCESSFUL\" /* Successful */:\n                    return of(this._stateStream.getValue());\n                case \"ERRORED\" /* Errored */:\n                    return throwError(ctx.error);\n                default:\n                    return EMPTY;\n            }\n        }))\n            .pipe(shareReplay());\n    }\n}\n/** @nocollapse */ InternalDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatcher, deps: [{ token: InternalActions }, { token: InternalDispatchedActionResults }, { token: PluginManager }, { token: StateStream }, { token: InternalNgxsExecutionStrategy }, { token: InternalErrorReporter }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InternalActions }, { type: InternalDispatchedActionResults }, { type: PluginManager }, { type: StateStream }, { type: InternalNgxsExecutionStrategy }, { type: InternalErrorReporter }]; } });\n\n/**\n * Object freeze code\n * https://github.com/jsdf/deep-freeze\n */\nconst deepFreeze = (o) => {\n    Object.freeze(o);\n    const oIsFunction = typeof o === 'function';\n    const hasOwnProp = Object.prototype.hasOwnProperty;\n    Object.getOwnPropertyNames(o).forEach(function (prop) {\n        if (hasOwnProp.call(o, prop) &&\n            (oIsFunction ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments' : true) &&\n            o[prop] !== null &&\n            (typeof o[prop] === 'object' || typeof o[prop] === 'function') &&\n            !Object.isFrozen(o[prop])) {\n            deepFreeze(o[prop]);\n        }\n    });\n    return o;\n};\n\n/**\n * @ignore\n */\nclass InternalStateOperations {\n    constructor(_stateStream, _dispatcher, _config) {\n        this._stateStream = _stateStream;\n        this._dispatcher = _dispatcher;\n        this._config = _config;\n    }\n    /**\n     * Returns the root state operators.\n     */\n    getRootStateOperations() {\n        const rootStateOperations = {\n            getState: () => this._stateStream.getValue(),\n            setState: (newState) => this._stateStream.next(newState),\n            dispatch: (actionOrActions) => this._dispatcher.dispatch(actionOrActions)\n        };\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            return this._config.developmentMode\n                ? ensureStateAndActionsAreImmutable(rootStateOperations)\n                : rootStateOperations;\n        }\n        else {\n            return rootStateOperations;\n        }\n    }\n    setStateToTheCurrentWithNew(results) {\n        const stateOperations = this.getRootStateOperations();\n        // Get our current stream\n        const currentState = stateOperations.getState();\n        // Set the state to the current + new\n        stateOperations.setState(Object.assign(Object.assign({}, currentState), results.defaults));\n    }\n}\n/** @nocollapse */ InternalStateOperations.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalStateOperations, deps: [{ token: StateStream }, { token: InternalDispatcher }, { token: NgxsConfig }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ InternalStateOperations.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalStateOperations, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: InternalStateOperations, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: StateStream }, { type: InternalDispatcher }, { type: NgxsConfig }]; } });\nfunction ensureStateAndActionsAreImmutable(root) {\n    return {\n        getState: () => root.getState(),\n        setState: value => {\n            const frozenValue = deepFreeze(value);\n            return root.setState(frozenValue);\n        },\n        dispatch: actions => {\n            return root.dispatch(actions);\n        }\n    };\n}\n\nfunction simplePatch(value) {\n    return (existingState) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (Array.isArray(value)) {\n                throwPatchingArrayError();\n            }\n            else if (typeof value !== 'object') {\n                throwPatchingPrimitiveError();\n            }\n        }\n        const newState = Object.assign({}, existingState);\n        for (const key in value) {\n            // deep clone for patch compatibility\n            newState[key] = value[key];\n        }\n        return newState;\n    };\n}\n\n/**\n * State Context factory class\n * @ignore\n */\nclass StateContextFactory {\n    constructor(_internalStateOperations) {\n        this._internalStateOperations = _internalStateOperations;\n    }\n    /**\n     * Create the state context\n     */\n    createStateContext(mappedStore) {\n        const root = this._internalStateOperations.getRootStateOperations();\n        return {\n            getState() {\n                const currentAppState = root.getState();\n                return getState(currentAppState, mappedStore.path);\n            },\n            patchState(val) {\n                const currentAppState = root.getState();\n                const patchOperator = simplePatch(val);\n                return setStateFromOperator(root, currentAppState, patchOperator, mappedStore.path);\n            },\n            setState(val) {\n                const currentAppState = root.getState();\n                return isStateOperator(val)\n                    ? setStateFromOperator(root, currentAppState, val, mappedStore.path)\n                    : setStateValue(root, currentAppState, val, mappedStore.path);\n            },\n            dispatch(actions) {\n                return root.dispatch(actions);\n            }\n        };\n    }\n}\n/** @nocollapse */ StateContextFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateContextFactory, deps: [{ token: InternalStateOperations }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ StateContextFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateContextFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateContextFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InternalStateOperations }]; } });\nfunction setStateValue(root, currentAppState, newValue, path) {\n    const newAppState = setValue(currentAppState, path, newValue);\n    root.setState(newAppState);\n    return newAppState;\n    // In doing this refactoring I noticed that there is a 'bug' where the\n    // application state is returned instead of this state slice.\n    // This has worked this way since the beginning see:\n    // https://github.com/ngxs/store/blame/324c667b4b7debd8eb979006c67ca0ae347d88cd/src/state-factory.ts\n    // This needs to be fixed, but is a 'breaking' change.\n    // I will do this fix in a subsequent PR and we can decide how to handle it.\n}\nfunction setStateFromOperator(root, currentAppState, stateOperator, path) {\n    const local = getState(currentAppState, path);\n    const newValue = stateOperator(local);\n    return setStateValue(root, currentAppState, newValue, path);\n}\nfunction getState(currentAppState, path) {\n    return getValue(currentAppState, path);\n}\n\nconst stateNameRegex = new RegExp('^[a-zA-Z0-9_]+$');\nfunction ensureStateNameIsValid(name) {\n    if (!name) {\n        throwStateNamePropertyError();\n    }\n    else if (!stateNameRegex.test(name)) {\n        throwStateNameError(name);\n    }\n}\nfunction ensureStateNameIsUnique(stateName, state, statesByName) {\n    const existingState = statesByName[stateName];\n    if (existingState && existingState !== state) {\n        throwStateUniqueError(stateName, state.name, existingState.name);\n    }\n}\nfunction ensureStatesAreDecorated(stateClasses) {\n    stateClasses.forEach((stateClass) => {\n        if (!getStoreMetadata$1(stateClass)) {\n            throwStateDecoratorError(stateClass.name);\n        }\n    });\n}\n\n/**\n * All provided or injected tokens must have `@Injectable` decorator\n * (previously, injected tokens without `@Injectable` were allowed\n * if another decorator was used, e.g. pipes).\n */\nfunction ensureStateClassIsInjectable(stateClass) {\n    if (jit_hasInjectableAnnotation(stateClass) || aot_hasNgInjectableDef(stateClass)) {\n        return;\n    }\n    console.warn(getUndecoratedStateInIvyWarningMessage(stateClass.name));\n}\nfunction aot_hasNgInjectableDef(stateClass) {\n    // `ɵprov` is a static property added by the NGCC compiler. It always exists in\n    // AOT mode because this property is added before runtime. If an application is running in\n    // JIT mode then this property can be added by the `@Injectable()` decorator. The `@Injectable()`\n    // decorator has to go after the `@State()` decorator, thus we prevent users from unwanted DI errors.\n    return !!stateClass.ɵprov;\n}\nfunction jit_hasInjectableAnnotation(stateClass) {\n    // `ɵprov` doesn't exist in JIT mode (for instance when running unit tests with Jest).\n    const annotations = stateClass.__annotations__ || [];\n    return annotations.some((annotation) => (annotation === null || annotation === void 0 ? void 0 : annotation.ngMetadataName) === 'Injectable');\n}\n\n/**\n * Init action\n */\nclass InitState {\n}\nInitState.type = '@@INIT';\n/**\n * Update action\n */\nclass UpdateState {\n    constructor(addedStates) {\n        this.addedStates = addedStates;\n    }\n}\nUpdateState.type = '@@UPDATE_STATE';\n\nconst NGXS_DEVELOPMENT_OPTIONS = new InjectionToken('NGXS_DEVELOPMENT_OPTIONS', {\n    providedIn: 'root',\n    factory: () => ({ warnOnUnhandledActions: true })\n});\n\nclass NgxsUnhandledActionsLogger {\n    constructor(options) {\n        /**\n         * These actions should be ignored by default; the user can increase this\n         * list in the future via the `ignoreActions` method.\n         */\n        this._ignoredActions = new Set([InitState.type, UpdateState.type]);\n        if (typeof options.warnOnUnhandledActions === 'object') {\n            this.ignoreActions(...options.warnOnUnhandledActions.ignore);\n        }\n    }\n    /**\n     * Adds actions to the internal list of actions that should be ignored.\n     */\n    ignoreActions(...actions) {\n        for (const action of actions) {\n            this._ignoredActions.add(action.type);\n        }\n    }\n    /** @internal */\n    warn(action) {\n        const actionShouldBeIgnored = Array.from(this._ignoredActions).some(type => type === getActionTypeFromInstance(action));\n        if (actionShouldBeIgnored) {\n            return;\n        }\n        action =\n            action.constructor && action.constructor.name !== 'Object'\n                ? action.constructor.name\n                : action.type;\n        console.warn(`The ${action} action has been dispatched but hasn't been handled. This may happen if the state with an action handler for this action is not registered.`);\n    }\n}\n/** @nocollapse */ NgxsUnhandledActionsLogger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsUnhandledActionsLogger, deps: [{ token: NGXS_DEVELOPMENT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NgxsUnhandledActionsLogger.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsUnhandledActionsLogger });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsUnhandledActionsLogger, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGXS_DEVELOPMENT_OPTIONS]\n                }] }]; } });\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\n/**\n * The `StateFactory` class adds root and feature states to the graph.\n * This extracts state names from state classes, checks if they already\n * exist in the global graph, throws errors if their names are invalid, etc.\n * See its constructor, state factories inject state factories that are\n * parent-level providers. This is required to get feature states from the\n * injector on the same level.\n *\n * The `NgxsModule.forFeature(...)` returns `providers: [StateFactory, ...states]`.\n * The `StateFactory` is initialized on the feature level and goes through `...states`\n * to get them from the injector through `injector.get(state)`.\n * @ignore\n */\nclass StateFactory {\n    constructor(_injector, _config, _parentFactory, _actions, _actionResults, _stateContextFactory, _initialState) {\n        this._injector = _injector;\n        this._config = _config;\n        this._parentFactory = _parentFactory;\n        this._actions = _actions;\n        this._actionResults = _actionResults;\n        this._stateContextFactory = _stateContextFactory;\n        this._initialState = _initialState;\n        this._actionsSubscription = null;\n        this._states = [];\n        this._statesByName = {};\n        this._statePaths = {};\n        this.getRuntimeSelectorContext = memoize(() => {\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            const stateFactory = this;\n            function resolveGetter(key) {\n                const path = stateFactory.statePaths[key];\n                return path ? propGetter(path.split('.'), stateFactory._config) : null;\n            }\n            const context = this._parentFactory\n                ? this._parentFactory.getRuntimeSelectorContext()\n                : {\n                    getStateGetter(key) {\n                        let getter = resolveGetter(key);\n                        if (getter) {\n                            return getter;\n                        }\n                        return (...args) => {\n                            // Late loaded getter\n                            if (!getter) {\n                                getter = resolveGetter(key);\n                            }\n                            return getter ? getter(...args) : undefined;\n                        };\n                    },\n                    getSelectorOptions(localOptions) {\n                        const globalSelectorOptions = stateFactory._config.selectorOptions;\n                        return Object.assign(Object.assign({}, globalSelectorOptions), (localOptions || {}));\n                    }\n                };\n            return context;\n        });\n    }\n    get states() {\n        return this._parentFactory ? this._parentFactory.states : this._states;\n    }\n    get statesByName() {\n        return this._parentFactory ? this._parentFactory.statesByName : this._statesByName;\n    }\n    get statePaths() {\n        return this._parentFactory ? this._parentFactory.statePaths : this._statePaths;\n    }\n    static _cloneDefaults(defaults) {\n        let value = defaults;\n        if (Array.isArray(defaults)) {\n            value = defaults.slice();\n        }\n        else if (isObject(defaults)) {\n            value = Object.assign({}, defaults);\n        }\n        else if (defaults === undefined) {\n            value = {};\n        }\n        return value;\n    }\n    ngOnDestroy() {\n        var _a;\n        (_a = this._actionsSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n    }\n    /**\n     * Add a new state to the global defs.\n     */\n    add(stateClasses) {\n        if (NG_DEV_MODE) {\n            ensureStatesAreDecorated(stateClasses);\n        }\n        const { newStates } = this.addToStatesMap(stateClasses);\n        if (!newStates.length)\n            return [];\n        const stateGraph = buildGraph(newStates);\n        const sortedStates = topologicalSort(stateGraph);\n        const paths = findFullParentPath(stateGraph);\n        const nameGraph = nameToState(newStates);\n        const bootstrappedStores = [];\n        for (const name of sortedStates) {\n            const stateClass = nameGraph[name];\n            const path = paths[name];\n            const meta = stateClass[META_KEY];\n            this.addRuntimeInfoToMeta(meta, path);\n            // Note: previously we called `ensureStateClassIsInjectable` within the\n            // `State` decorator. This check is moved here because the `ɵprov` property\n            // will not exist on the class in JIT mode (because it's set asynchronously\n            // during JIT compilation through `Object.defineProperty`).\n            if (NG_DEV_MODE) {\n                ensureStateClassIsInjectable(stateClass);\n            }\n            const stateMap = {\n                name,\n                path,\n                isInitialised: false,\n                actions: meta.actions,\n                instance: this._injector.get(stateClass),\n                defaults: StateFactory._cloneDefaults(meta.defaults)\n            };\n            // ensure our store hasn't already been added\n            // but don't throw since it could be lazy\n            // loaded from different paths\n            if (!this.hasBeenMountedAndBootstrapped(name, path)) {\n                bootstrappedStores.push(stateMap);\n            }\n            this.states.push(stateMap);\n        }\n        return bootstrappedStores;\n    }\n    /**\n     * Add a set of states to the store and return the defaults\n     */\n    addAndReturnDefaults(stateClasses) {\n        const classes = stateClasses || [];\n        const mappedStores = this.add(classes);\n        const defaults = mappedStores.reduce((result, mappedStore) => setValue(result, mappedStore.path, mappedStore.defaults), {});\n        return { defaults, states: mappedStores };\n    }\n    connectActionHandlers() {\n        // Note: We have to connect actions only once when the `StateFactory`\n        //       is being created for the first time. This checks if we're in\n        //       a child state factory and the parent state factory already exists.\n        if (this._parentFactory || this._actionsSubscription !== null) {\n            return;\n        }\n        const dispatched$ = new Subject();\n        this._actionsSubscription = this._actions\n            .pipe(filter((ctx) => ctx.status === \"DISPATCHED\" /* Dispatched */), mergeMap(ctx => {\n            dispatched$.next(ctx);\n            const action = ctx.action;\n            return this.invokeActions(dispatched$, action).pipe(map(() => ({ action, status: \"SUCCESSFUL\" /* Successful */ })), defaultIfEmpty({ action, status: \"CANCELED\" /* Canceled */ }), catchError(error => of({ action, status: \"ERRORED\" /* Errored */, error })));\n        }))\n            .subscribe(ctx => this._actionResults.next(ctx));\n    }\n    /**\n     * Invoke actions on the states.\n     */\n    invokeActions(dispatched$, action) {\n        const type = getActionTypeFromInstance(action);\n        const results = [];\n        // Determines whether the dispatched action has been handled, this is assigned\n        // to `true` within the below `for` loop if any `actionMetas` has been found.\n        let actionHasBeenHandled = false;\n        for (const metadata of this.states) {\n            const actionMetas = metadata.actions[type];\n            if (actionMetas) {\n                for (const actionMeta of actionMetas) {\n                    const stateContext = this._stateContextFactory.createStateContext(metadata);\n                    try {\n                        let result = metadata.instance[actionMeta.fn](stateContext, action);\n                        if (result instanceof Promise) {\n                            result = from(result);\n                        }\n                        if (isObservable(result)) {\n                            // If this observable has been completed w/o emitting\n                            // any value then we wouldn't want to complete the whole chain\n                            // of actions. Since if any observable completes then\n                            // action will be canceled.\n                            // For instance if any action handler would've had such statement:\n                            // `handler(ctx) { return EMPTY; }`\n                            // then the action will be canceled.\n                            // See https://github.com/ngxs/store/issues/1568\n                            result = result.pipe(mergeMap((value) => {\n                                if (value instanceof Promise) {\n                                    return from(value);\n                                }\n                                if (isObservable(value)) {\n                                    return value;\n                                }\n                                return of(value);\n                            }), defaultIfEmpty({}));\n                            if (actionMeta.options.cancelUncompleted) {\n                                // todo: ofActionDispatched should be used with action class\n                                result = result.pipe(takeUntil(dispatched$.pipe(ofActionDispatched(action))));\n                            }\n                        }\n                        else {\n                            result = of({}).pipe(shareReplay());\n                        }\n                        results.push(result);\n                    }\n                    catch (e) {\n                        results.push(throwError(e));\n                    }\n                    actionHasBeenHandled = true;\n                }\n            }\n        }\n        // The `NgxsUnhandledActionsLogger` is a tree-shakable class which functions\n        // only during development.\n        if (NG_DEV_MODE && !actionHasBeenHandled) {\n            const unhandledActionsLogger = this._injector.get(NgxsUnhandledActionsLogger, null);\n            // The `NgxsUnhandledActionsLogger` will not be resolved by the injector if the\n            // `NgxsDevelopmentModule` is not provided. It's enough to check whether the `injector.get`\n            // didn't return `null` so we may ensure the module has been imported.\n            if (unhandledActionsLogger) {\n                unhandledActionsLogger.warn(action);\n            }\n        }\n        if (!results.length) {\n            results.push(of({}));\n        }\n        return forkJoin(results);\n    }\n    addToStatesMap(stateClasses) {\n        const newStates = [];\n        const statesMap = this.statesByName;\n        for (const stateClass of stateClasses) {\n            const stateName = getStoreMetadata$1(stateClass).name;\n            if (NG_DEV_MODE) {\n                ensureStateNameIsUnique(stateName, stateClass, statesMap);\n            }\n            const unmountedState = !statesMap[stateName];\n            if (unmountedState) {\n                newStates.push(stateClass);\n                statesMap[stateName] = stateClass;\n            }\n        }\n        return { newStates };\n    }\n    addRuntimeInfoToMeta(meta, path) {\n        this.statePaths[meta.name] = path;\n        // TODO: v4 - we plan to get rid of the path property because it is non-deterministic\n        // we can do this when we get rid of the incorrectly exposed getStoreMetadata\n        // We will need to come up with an alternative in v4 because this is used by many plugins\n        meta.path = path;\n    }\n    hasBeenMountedAndBootstrapped(name, path) {\n        const valueIsBootstrappedInInitialState = getValue(this._initialState, path) !== undefined;\n        // This checks whether a state has been already added to the global graph and\n        // its lifecycle is in 'bootstrapped' state.\n        return this.statesByName[name] && valueIsBootstrappedInInitialState;\n    }\n}\n/** @nocollapse */ StateFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateFactory, deps: [{ token: i0.Injector }, { token: NgxsConfig }, { token: StateFactory, optional: true, skipSelf: true }, { token: InternalActions }, { token: InternalDispatchedActionResults }, { token: StateContextFactory }, { token: INITIAL_STATE_TOKEN, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ StateFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateFactory });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: StateFactory, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: NgxsConfig }, { type: StateFactory, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: InternalActions }, { type: InternalDispatchedActionResults }, { type: StateContextFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INITIAL_STATE_TOKEN]\n                }] }]; } });\n\nfunction createRootSelectorFactory(selectorMetaData, selectors, memoizedSelectorFn) {\n    return (context) => {\n        const { argumentSelectorFunctions, selectorOptions } = getRuntimeSelectorInfo(context, selectorMetaData, selectors);\n        return function selectFromRoot(rootState) {\n            // Determine arguments from the app state using the selectors\n            const results = argumentSelectorFunctions.map((argFn) => argFn(rootState));\n            // if the lambda tries to access a something on the\n            // state that doesn't exist, it will throw a TypeError.\n            // since this is quite usual behaviour, we simply return undefined if so.\n            try {\n                return memoizedSelectorFn(...results);\n            }\n            catch (ex) {\n                if (ex instanceof TypeError && selectorOptions.suppressErrors) {\n                    return undefined;\n                }\n                throw ex;\n            }\n        };\n    };\n}\nfunction createMemoizedSelectorFn(originalFn, creationMetadata) {\n    const containerClass = creationMetadata && creationMetadata.containerClass;\n    const wrappedFn = function wrappedSelectorFn(...args) {\n        const returnValue = originalFn.apply(containerClass, args);\n        if (returnValue instanceof Function) {\n            const innerMemoizedFn = memoize.apply(null, [returnValue]);\n            return innerMemoizedFn;\n        }\n        return returnValue;\n    };\n    const memoizedFn = memoize(wrappedFn);\n    Object.setPrototypeOf(memoizedFn, originalFn);\n    return memoizedFn;\n}\nfunction getRuntimeSelectorInfo(context, selectorMetaData, selectors = []) {\n    const localSelectorOptions = selectorMetaData.getSelectorOptions();\n    const selectorOptions = context.getSelectorOptions(localSelectorOptions);\n    const selectorsToApply = getSelectorsToApply(selectors, selectorOptions, selectorMetaData.containerClass);\n    const argumentSelectorFunctions = selectorsToApply.map((selector) => {\n        const factory = getRootSelectorFactory(selector);\n        return factory(context);\n    });\n    return {\n        selectorOptions,\n        argumentSelectorFunctions,\n    };\n}\nfunction getSelectorsToApply(selectors = [], selectorOptions, containerClass) {\n    const selectorsToApply = [];\n    const canInjectContainerState = selectors.length === 0 || selectorOptions.injectContainerState;\n    if (containerClass && canInjectContainerState) {\n        // If we are on a state class, add it as the first selector parameter\n        const metadata = getStoreMetadata$1(containerClass);\n        if (metadata) {\n            selectorsToApply.push(containerClass);\n        }\n    }\n    if (selectors) {\n        selectorsToApply.push(...selectors);\n    }\n    return selectorsToApply;\n}\n/**\n * This function gets the factory function to create the selector to get the selected slice from the app state\n * @ignore\n */\nfunction getRootSelectorFactory(selector) {\n    const metadata = getSelectorMetadata$1(selector) || getStoreMetadata$1(selector);\n    return (metadata && metadata.makeRootSelector) || (() => selector);\n}\n\n// tslint:disable:unified-signatures\nclass Store {\n    constructor(_stateStream, _internalStateOperations, _config, _internalExecutionStrategy, _stateFactory, initialStateValue) {\n        this._stateStream = _stateStream;\n        this._internalStateOperations = _internalStateOperations;\n        this._config = _config;\n        this._internalExecutionStrategy = _internalExecutionStrategy;\n        this._stateFactory = _stateFactory;\n        /**\n         * This is a derived state stream that leaves NGXS execution strategy to emit state changes within the Angular zone,\n         * because state is being changed actually within the `<root>` zone, see `InternalDispatcher#dispatchSingle`.\n         * All selects would use this stream, and it would call leave only once for any state change across all active selectors.\n         */\n        this._selectableStateStream = this._stateStream.pipe(leaveNgxs(this._internalExecutionStrategy), shareReplay({ bufferSize: 1, refCount: true }));\n        this.initStateStream(initialStateValue);\n    }\n    /**\n     * Dispatches event(s).\n     */\n    dispatch(actionOrActions) {\n        return this._internalStateOperations.getRootStateOperations().dispatch(actionOrActions);\n    }\n    select(selector) {\n        const selectorFn = this.getStoreBoundSelectorFn(selector);\n        return this._selectableStateStream.pipe(map(selectorFn), catchError((err) => {\n            // if error is TypeError we swallow it to prevent usual errors with property access\n            const { suppressErrors } = this._config.selectorOptions;\n            if (err instanceof TypeError && suppressErrors) {\n                return of(undefined);\n            }\n            // rethrow other errors\n            return throwError(err);\n        }), distinctUntilChanged(), leaveNgxs(this._internalExecutionStrategy));\n    }\n    selectOnce(selector) {\n        return this.select(selector).pipe(take(1));\n    }\n    selectSnapshot(selector) {\n        const selectorFn = this.getStoreBoundSelectorFn(selector);\n        return selectorFn(this._stateStream.getValue());\n    }\n    /**\n     * Allow the user to subscribe to the root of the state\n     */\n    subscribe(fn) {\n        return this._selectableStateStream\n            .pipe(leaveNgxs(this._internalExecutionStrategy))\n            .subscribe(fn);\n    }\n    /**\n     * Return the raw value of the state.\n     */\n    snapshot() {\n        return this._internalStateOperations.getRootStateOperations().getState();\n    }\n    /**\n     * Reset the state to a specific point in time. This method is useful\n     * for plugin's who need to modify the state directly or unit testing.\n     */\n    reset(state) {\n        return this._internalStateOperations.getRootStateOperations().setState(state);\n    }\n    getStoreBoundSelectorFn(selector) {\n        const makeSelectorFn = getRootSelectorFactory(selector);\n        const runtimeContext = this._stateFactory.getRuntimeSelectorContext();\n        return makeSelectorFn(runtimeContext);\n    }\n    initStateStream(initialStateValue) {\n        const value = this._stateStream.value;\n        const storeIsEmpty = !value || Object.keys(value).length === 0;\n        if (storeIsEmpty) {\n            const defaultStateNotEmpty = Object.keys(this._config.defaultsState).length > 0;\n            const storeValues = defaultStateNotEmpty\n                ? Object.assign(Object.assign({}, this._config.defaultsState), initialStateValue) : initialStateValue;\n            this._stateStream.next(storeValues);\n        }\n    }\n}\n/** @nocollapse */ Store.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Store, deps: [{ token: StateStream }, { token: InternalStateOperations }, { token: NgxsConfig }, { token: InternalNgxsExecutionStrategy }, { token: StateFactory }, { token: INITIAL_STATE_TOKEN, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ Store.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Store, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: Store, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: StateStream }, { type: InternalStateOperations }, { type: NgxsConfig }, { type: InternalNgxsExecutionStrategy }, { type: StateFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INITIAL_STATE_TOKEN]\n                }] }]; } });\n\n/**\n * Allows the select decorator to get access to the DI store, this is used internally\n * in `@Select` decorator.\n */\nclass SelectFactory {\n    constructor(store, config) {\n        SelectFactory.store = store;\n        SelectFactory.config = config;\n    }\n    ngOnDestroy() {\n        SelectFactory.store = null;\n        SelectFactory.config = null;\n    }\n}\nSelectFactory.store = null;\nSelectFactory.config = null;\n/** @nocollapse */ SelectFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: SelectFactory, deps: [{ token: Store }, { token: NgxsConfig }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ SelectFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: SelectFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: SelectFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Store }, { type: NgxsConfig }]; } });\n\nclass LifecycleStateManager {\n    constructor(_store, _internalErrorReporter, _internalStateOperations, _stateContextFactory, _bootstrapper) {\n        this._store = _store;\n        this._internalErrorReporter = _internalErrorReporter;\n        this._internalStateOperations = _internalStateOperations;\n        this._stateContextFactory = _stateContextFactory;\n        this._bootstrapper = _bootstrapper;\n        this._destroy$ = new Subject();\n    }\n    ngOnDestroy() {\n        this._destroy$.next();\n    }\n    ngxsBootstrap(action, results) {\n        this._internalStateOperations\n            .getRootStateOperations()\n            .dispatch(action)\n            .pipe(filter(() => !!results), tap(() => this._invokeInitOnStates(results.states)), mergeMap(() => this._bootstrapper.appBootstrapped$), filter(appBootstrapped => !!appBootstrapped), catchError(error => {\n            // The `SafeSubscriber` (which is used by most RxJS operators) re-throws\n            // errors asynchronously (`setTimeout(() => { throw error })`). This might\n            // break existing user's code or unit tests. We catch the error manually to\n            // be backward compatible with the old behavior.\n            this._internalErrorReporter.reportErrorSafely(error);\n            return EMPTY;\n        }), takeUntil(this._destroy$))\n            .subscribe(() => this._invokeBootstrapOnStates(results.states));\n    }\n    _invokeInitOnStates(mappedStores) {\n        for (const mappedStore of mappedStores) {\n            const instance = mappedStore.instance;\n            if (instance.ngxsOnChanges) {\n                this._store\n                    .select(state => getValue(state, mappedStore.path))\n                    .pipe(startWith(undefined), pairwise(), takeUntil(this._destroy$))\n                    .subscribe(([previousValue, currentValue]) => {\n                    const change = new NgxsSimpleChange(previousValue, currentValue, !mappedStore.isInitialised);\n                    instance.ngxsOnChanges(change);\n                });\n            }\n            if (instance.ngxsOnInit) {\n                instance.ngxsOnInit(this._getStateContext(mappedStore));\n            }\n            mappedStore.isInitialised = true;\n        }\n    }\n    _invokeBootstrapOnStates(mappedStores) {\n        for (const mappedStore of mappedStores) {\n            const instance = mappedStore.instance;\n            if (instance.ngxsAfterBootstrap) {\n                instance.ngxsAfterBootstrap(this._getStateContext(mappedStore));\n            }\n        }\n    }\n    _getStateContext(mappedStore) {\n        return this._stateContextFactory.createStateContext(mappedStore);\n    }\n}\n/** @nocollapse */ LifecycleStateManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: LifecycleStateManager, deps: [{ token: Store }, { token: InternalErrorReporter }, { token: InternalStateOperations }, { token: StateContextFactory }, { token: i5.NgxsBootstrapper }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ LifecycleStateManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: LifecycleStateManager, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: LifecycleStateManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Store }, { type: InternalErrorReporter }, { type: InternalStateOperations }, { type: StateContextFactory }, { type: i5.NgxsBootstrapper }]; } });\n\n/**\n * Root module\n * @ignore\n */\nclass NgxsRootModule {\n    constructor(factory, internalStateOperations, _store, _select, states = [], lifecycleStateManager) {\n        // Add stores to the state graph and return their defaults\n        const results = factory.addAndReturnDefaults(states);\n        internalStateOperations.setStateToTheCurrentWithNew(results);\n        // Connect our actions stream\n        factory.connectActionHandlers();\n        // Dispatch the init action and invoke init and bootstrap functions after\n        lifecycleStateManager.ngxsBootstrap(new InitState(), results);\n    }\n}\n/** @nocollapse */ NgxsRootModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRootModule, deps: [{ token: StateFactory }, { token: InternalStateOperations }, { token: Store }, { token: SelectFactory }, { token: ROOT_STATE_TOKEN, optional: true }, { token: LifecycleStateManager }], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsRootModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRootModule });\n/** @nocollapse */ NgxsRootModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRootModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsRootModule, decorators: [{\n            type: NgModule\n        }], ctorParameters: function () { return [{ type: StateFactory }, { type: InternalStateOperations }, { type: Store }, { type: SelectFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ROOT_STATE_TOKEN]\n                }] }, { type: LifecycleStateManager }]; } });\n\n/**\n * Feature module\n * @ignore\n */\nclass NgxsFeatureModule {\n    constructor(_store, internalStateOperations, factory, states = [], lifecycleStateManager) {\n        // Since FEATURE_STATE_TOKEN is a multi token, we need to\n        // flatten it [[Feature1State, Feature2State], [Feature3State]]\n        const flattenedStates = NgxsFeatureModule.flattenStates(states);\n        // add stores to the state graph and return their defaults\n        const results = factory.addAndReturnDefaults(flattenedStates);\n        if (results.states.length) {\n            internalStateOperations.setStateToTheCurrentWithNew(results);\n            // dispatch the update action and invoke init and bootstrap functions after\n            lifecycleStateManager.ngxsBootstrap(new UpdateState(results.defaults), results);\n        }\n    }\n    static flattenStates(states = []) {\n        return states.reduce((total, values) => total.concat(values), []);\n    }\n}\n/** @nocollapse */ NgxsFeatureModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsFeatureModule, deps: [{ token: Store }, { token: InternalStateOperations }, { token: StateFactory }, { token: FEATURE_STATE_TOKEN, optional: true }, { token: LifecycleStateManager }], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsFeatureModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsFeatureModule });\n/** @nocollapse */ NgxsFeatureModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsFeatureModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsFeatureModule, decorators: [{\n            type: NgModule\n        }], ctorParameters: function () { return [{ type: Store }, { type: InternalStateOperations }, { type: StateFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FEATURE_STATE_TOKEN]\n                }] }, { type: LifecycleStateManager }]; } });\n\n/**\n * Ngxs Module\n */\nclass NgxsModule {\n    /**\n     * Root module factory\n     */\n    static forRoot(states = [], options = {}) {\n        return {\n            ngModule: NgxsRootModule,\n            providers: [\n                StateFactory,\n                PluginManager,\n                ...states,\n                ...NgxsModule.ngxsTokenProviders(states, options)\n            ]\n        };\n    }\n    /**\n     * Feature module factory\n     */\n    static forFeature(states = []) {\n        return {\n            ngModule: NgxsFeatureModule,\n            providers: [\n                // This is required on the feature level, see comments in `state-factory.ts`.\n                StateFactory,\n                PluginManager,\n                ...states,\n                {\n                    provide: FEATURE_STATE_TOKEN,\n                    multi: true,\n                    useValue: states\n                }\n            ]\n        };\n    }\n    static ngxsTokenProviders(states, options) {\n        return [\n            {\n                provide: USER_PROVIDED_NGXS_EXECUTION_STRATEGY,\n                useValue: options.executionStrategy\n            },\n            {\n                provide: ROOT_STATE_TOKEN,\n                useValue: states\n            },\n            {\n                provide: ROOT_OPTIONS,\n                useValue: options\n            },\n            {\n                provide: APP_BOOTSTRAP_LISTENER,\n                useFactory: NgxsModule.appBootstrapListenerFactory,\n                multi: true,\n                deps: [NgxsBootstrapper]\n            },\n            {\n                provide: ɵNGXS_STATE_CONTEXT_FACTORY,\n                useExisting: StateContextFactory\n            },\n            {\n                provide: ɵNGXS_STATE_FACTORY,\n                useExisting: StateFactory\n            }\n        ];\n    }\n    static appBootstrapListenerFactory(bootstrapper) {\n        return () => bootstrapper.bootstrap();\n    }\n}\n/** @nocollapse */ NgxsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsModule });\n/** @nocollapse */ NgxsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsModule, decorators: [{\n            type: NgModule\n        }] });\n\n/**\n * Decorates a method with a action information.\n */\nfunction Action(actions, options) {\n    return (target, name) => {\n        // Caretaker note: we have still left the `typeof` condition in order to avoid\n        // creating a breaking change for projects that still use the View Engine.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const isStaticMethod = target.hasOwnProperty('prototype');\n            if (isStaticMethod) {\n                throwActionDecoratorError();\n            }\n        }\n        const meta = ensureStoreMetadata$1(target.constructor);\n        if (!Array.isArray(actions)) {\n            actions = [actions];\n        }\n        for (const action of actions) {\n            const type = action.type;\n            if (!meta.actions[type]) {\n                meta.actions[type] = [];\n            }\n            meta.actions[type].push({\n                fn: name,\n                options: options || {},\n                type\n            });\n        }\n    };\n}\n\n/**\n * Decorates a class with ngxs state information.\n */\nfunction State(options) {\n    return (target) => {\n        const stateClass = target;\n        const meta = ensureStoreMetadata$1(stateClass);\n        const inheritedStateClass = Object.getPrototypeOf(stateClass);\n        const optionsWithInheritance = getStateOptions(inheritedStateClass, options);\n        mutateMetaData({ meta, inheritedStateClass, optionsWithInheritance });\n        stateClass[META_OPTIONS_KEY] = optionsWithInheritance;\n    };\n}\nfunction getStateOptions(inheritedStateClass, options) {\n    const inheritanceOptions = inheritedStateClass[META_OPTIONS_KEY] || {};\n    return Object.assign(Object.assign({}, inheritanceOptions), options);\n}\nfunction mutateMetaData(params) {\n    const { meta, inheritedStateClass, optionsWithInheritance } = params;\n    const { children, defaults, name } = optionsWithInheritance;\n    const stateName = typeof name === 'string' ? name : (name && name.getName()) || null;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        ensureStateNameIsValid(stateName);\n    }\n    if (inheritedStateClass.hasOwnProperty(META_KEY)) {\n        const inheritedMeta = inheritedStateClass[META_KEY] || {};\n        meta.actions = Object.assign(Object.assign({}, meta.actions), inheritedMeta.actions);\n    }\n    meta.children = children;\n    meta.defaults = defaults;\n    meta.name = stateName;\n}\n\nconst DOLLAR_CHAR_CODE = 36;\nfunction createSelectObservable(selector) {\n    if (!SelectFactory.store) {\n        throwSelectFactoryNotConnectedError();\n    }\n    return SelectFactory.store.select(selector);\n}\nfunction createSelectorFn(name, rawSelector, paths = []) {\n    rawSelector = !rawSelector ? removeDollarAtTheEnd(name) : rawSelector;\n    if (typeof rawSelector === 'string') {\n        const propsArray = paths.length\n            ? [rawSelector, ...paths]\n            : rawSelector.split('.');\n        return propGetter(propsArray, SelectFactory.config);\n    }\n    return rawSelector;\n}\n/**\n * @example If `foo$` => make it just `foo`\n */\nfunction removeDollarAtTheEnd(name) {\n    const lastCharIndex = name.length - 1;\n    const dollarAtTheEnd = name.charCodeAt(lastCharIndex) === DOLLAR_CHAR_CODE;\n    return dollarAtTheEnd ? name.slice(0, lastCharIndex) : name;\n}\n\n/**\n * Decorator for selecting a slice of state from the store.\n */\nfunction Select(rawSelector, ...paths) {\n    return function (target, key) {\n        const name = key.toString();\n        const selectorId = `__${name}__selector`;\n        const selector = createSelectorFn(name, rawSelector, paths);\n        Object.defineProperties(target, {\n            [selectorId]: {\n                writable: true,\n                enumerable: false,\n                configurable: true\n            },\n            [name]: {\n                enumerable: true,\n                configurable: true,\n                get() {\n                    return this[selectorId] || (this[selectorId] = createSelectObservable(selector));\n                }\n            }\n        });\n    };\n}\n\nconst SELECTOR_OPTIONS_META_KEY = 'NGXS_SELECTOR_OPTIONS_META';\nconst selectorOptionsMetaAccessor = {\n    getOptions: (target) => {\n        return (target && target[SELECTOR_OPTIONS_META_KEY]) || {};\n    },\n    defineOptions: (target, options) => {\n        if (!target)\n            return;\n        target[SELECTOR_OPTIONS_META_KEY] = options;\n    },\n};\nfunction setupSelectorMetadata(originalFn, creationMetadata) {\n    const selectorMetaData = ensureSelectorMetadata$1(originalFn);\n    selectorMetaData.originalFn = originalFn;\n    let getExplicitSelectorOptions = () => ({});\n    if (creationMetadata) {\n        selectorMetaData.containerClass = creationMetadata.containerClass;\n        selectorMetaData.selectorName = creationMetadata.selectorName || null;\n        getExplicitSelectorOptions =\n            creationMetadata.getSelectorOptions || getExplicitSelectorOptions;\n    }\n    const selectorMetaDataClone = Object.assign({}, selectorMetaData);\n    selectorMetaData.getSelectorOptions = () => getLocalSelectorOptions(selectorMetaDataClone, getExplicitSelectorOptions());\n    return selectorMetaData;\n}\nfunction getLocalSelectorOptions(selectorMetaData, explicitOptions) {\n    return Object.assign(Object.assign(Object.assign(Object.assign({}, (selectorOptionsMetaAccessor.getOptions(selectorMetaData.containerClass) || {})), (selectorOptionsMetaAccessor.getOptions(selectorMetaData.originalFn) || {})), (selectorMetaData.getSelectorOptions() || {})), explicitOptions);\n}\n\n/**\n * Decorator for setting selector options at a method or class level.\n */\nfunction SelectorOptions(options) {\n    return (function decorate(target, methodName, descriptor) {\n        if (methodName) {\n            descriptor || (descriptor = Object.getOwnPropertyDescriptor(target, methodName));\n            // Method Decorator\n            const originalFn = descriptor.value || descriptor.originalFn;\n            if (originalFn) {\n                selectorOptionsMetaAccessor.defineOptions(originalFn, options);\n            }\n        }\n        else {\n            // Class Decorator\n            selectorOptionsMetaAccessor.defineOptions(target, options);\n        }\n    });\n}\n\nfunction ensureStoreMetadata(target) {\n    return ensureStoreMetadata$1(target);\n}\nfunction getStoreMetadata(target) {\n    return getStoreMetadata$1(target);\n}\nfunction ensureSelectorMetadata(target) {\n    return ensureSelectorMetadata$1(target);\n}\nfunction getSelectorMetadata(target) {\n    return getSelectorMetadata$1(target);\n}\n\nfunction createSelector(selectors, projector, creationMetadata) {\n    const memoizedFn = createMemoizedSelectorFn(projector, creationMetadata);\n    const selectorMetaData = setupSelectorMetadata(projector, creationMetadata);\n    selectorMetaData.makeRootSelector = createRootSelectorFactory(selectorMetaData, selectors, memoizedFn);\n    return memoizedFn;\n}\n\nfunction Selector(selectors) {\n    return (target, key, descriptor) => {\n        descriptor || (descriptor = Object.getOwnPropertyDescriptor(target, key));\n        const originalFn = descriptor === null || descriptor === void 0 ? void 0 : descriptor.value;\n        // Caretaker note: we have still left the `typeof` condition in order to avoid\n        // creating a breaking change for projects that still use the View Engine.\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (originalFn && typeof originalFn !== 'function') {\n                throwSelectorDecoratorError();\n            }\n        }\n        const memoizedFn = createSelector(selectors, originalFn, {\n            containerClass: target,\n            selectorName: key.toString(),\n            getSelectorOptions() {\n                return {};\n            },\n        });\n        const newDescriptor = {\n            configurable: true,\n            get() {\n                return memoizedFn;\n            },\n        };\n        // Add hidden property to descriptor\n        newDescriptor['originalFn'] = originalFn;\n        return newDescriptor;\n    };\n}\n\nclass StateToken {\n    constructor(name) {\n        this.name = name;\n        const selectorMetadata = ensureSelectorMetadata$1(this);\n        selectorMetadata.makeRootSelector = (runtimeContext) => {\n            return runtimeContext.getStateGetter(this.name);\n        };\n    }\n    getName() {\n        return this.name;\n    }\n    toString() {\n        return `StateToken[${this.name}]`;\n    }\n}\n\nclass NgxsDevelopmentModule {\n    static forRoot(options) {\n        return {\n            ngModule: NgxsDevelopmentModule,\n            providers: [\n                NgxsUnhandledActionsLogger,\n                { provide: NGXS_DEVELOPMENT_OPTIONS, useValue: options }\n            ]\n        };\n    }\n}\n/** @nocollapse */ NgxsDevelopmentModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsDevelopmentModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsDevelopmentModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsDevelopmentModule });\n/** @nocollapse */ NgxsDevelopmentModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsDevelopmentModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsDevelopmentModule, decorators: [{\n            type: NgModule\n        }] });\n\nfunction ensureValidSelector(selector, context = {}) {\n    const noun = context.noun || 'selector';\n    const prefix = context.prefix ? context.prefix + ': ' : '';\n    ensureValueProvided(selector, { noun, prefix: context.prefix });\n    const metadata = getSelectorMetadata$1(selector) || getStoreMetadata$1(selector);\n    if (!metadata) {\n        throw new Error(`${prefix}The value provided as the ${noun} is not a valid selector.`);\n    }\n}\nfunction ensureValueProvided(value, context = {}) {\n    const noun = context.noun || 'value';\n    const prefix = context.prefix ? context.prefix + ': ' : '';\n    if (!value) {\n        throw new Error(`${prefix}A ${noun} must be provided.`);\n    }\n}\n\nfunction createModelSelector(selectorMap) {\n    const selectorKeys = Object.keys(selectorMap);\n    const selectors = Object.values(selectorMap);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        ensureValidSelectorMap({\n            prefix: '[createModelSelector]',\n            selectorMap,\n            selectorKeys,\n            selectors\n        });\n    }\n    return createSelector(selectors, (...args) => {\n        return selectorKeys.reduce((obj, key, index) => {\n            obj[key] = args[index];\n            return obj;\n        }, {});\n    });\n}\nfunction ensureValidSelectorMap({ prefix, selectorMap, selectorKeys, selectors }) {\n    ensureValueProvided(selectorMap, { prefix, noun: 'selector map' });\n    ensureValueProvided(typeof selectorMap === 'object', { prefix, noun: 'valid selector map' });\n    ensureValueProvided(selectorKeys.length, { prefix, noun: 'non-empty selector map' });\n    selectors.forEach((selector, index) => ensureValidSelector(selector, {\n        prefix,\n        noun: `selector for the '${selectorKeys[index]}' property`\n    }));\n}\n\nfunction createPickSelector(selector, keys) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        ensureValidSelector(selector, { prefix: '[createPickSelector]' });\n    }\n    const validKeys = keys.filter(Boolean);\n    const selectors = validKeys.map(key => createSelector([selector], (s) => s[key]));\n    return createSelector([...selectors], (...props) => {\n        return validKeys.reduce((acc, key, index) => {\n            acc[key] = props[index];\n            return acc;\n        }, {});\n    });\n}\n\nfunction createPropertySelectors(parentSelector) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        ensureValidSelector(parentSelector, {\n            prefix: '[createPropertySelectors]',\n            noun: 'parent selector'\n        });\n    }\n    const cache = {};\n    return new Proxy({}, {\n        get(_target, prop) {\n            const selector = cache[prop] ||\n                createSelector([parentSelector], (s) => s === null || s === void 0 ? void 0 : s[prop]);\n            cache[prop] = selector;\n            return selector;\n        }\n    });\n}\n\n/**\n * The public api for consumers of @ngxs/store\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Action, Actions, InitState, NGXS_PLUGINS, NgxsDevelopmentModule, NgxsModule, NgxsSimpleChange, NgxsUnhandledActionsLogger, NoopNgxsExecutionStrategy, Select, Selector, SelectorOptions, State, StateStream, StateToken, Store, UpdateState, actionMatcher, createModelSelector, createPickSelector, createPropertySelectors, createSelector, ensureSelectorMetadata, ensureStoreMetadata, getActionTypeFromInstance, getSelectorMetadata, getStoreMetadata, getValue, ofAction, ofActionCanceled, ofActionCompleted, ofActionDispatched, ofActionErrored, ofActionSuccessful, setValue, NgxsFeatureModule as ɵNgxsFeatureModule, NgxsRootModule as ɵNgxsRootModule };\n"]}, "metadata": {}, "sourceType": "module"}