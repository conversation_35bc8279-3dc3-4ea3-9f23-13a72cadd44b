{"ast": null, "code": "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@popperjs/core/lib/createPopper.js"], "names": ["getCompositeRect", "getLayoutRect", "listScrollParents", "getOffsetParent", "orderModifiers", "debounce", "mergeByName", "detectOverflow", "isElement", "DEFAULT_OPTIONS", "placement", "modifiers", "strategy", "areValidElements", "_len", "arguments", "length", "args", "Array", "_key", "some", "element", "getBoundingClientRect", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "createPopper", "reference", "popper", "options", "state", "orderedModifiers", "Object", "assign", "modifiersData", "elements", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "contextElement", "concat", "filter", "m", "enabled", "runModifierEffects", "update", "forceUpdate", "_state$elements", "rects", "reset", "for<PERSON>ach", "modifier", "name", "data", "index", "_state$orderedModifie", "fn", "_state$orderedModifie2", "_options", "Promise", "resolve", "destroy", "then", "onFirstUpdate", "_ref", "_ref$options", "effect", "cleanupFn", "noopFn", "push"], "mappings": "AAAA,OAAOA,gBAAP,MAA6B,iCAA7B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,eAAP,MAA4B,gCAA5B;AACA,OAAOC,cAAP,MAA2B,2BAA3B;AACA,OAAOC,QAAP,MAAqB,qBAArB;AACA,OAAOC,WAAP,MAAwB,wBAAxB;AACA,OAAOC,cAAP,MAA2B,2BAA3B;AACA,SAASC,SAAT,QAA0B,2BAA1B;AACA,IAAIC,eAAe,GAAG;AACpBC,EAAAA,SAAS,EAAE,QADS;AAEpBC,EAAAA,SAAS,EAAE,EAFS;AAGpBC,EAAAA,QAAQ,EAAE;AAHU,CAAtB;;AAMA,SAASC,gBAAT,GAA4B;AAC1B,OAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAArB,EAA6BC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,IAAV,CAApC,EAAqDK,IAAI,GAAG,CAAjE,EAAoEA,IAAI,GAAGL,IAA3E,EAAiFK,IAAI,EAArF,EAAyF;AACvFF,IAAAA,IAAI,CAACE,IAAD,CAAJ,GAAaJ,SAAS,CAACI,IAAD,CAAtB;AACD;;AAED,SAAO,CAACF,IAAI,CAACG,IAAL,CAAU,UAAUC,OAAV,EAAmB;AACnC,WAAO,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACC,qBAAf,KAAyC,UAAtD,CAAP;AACD,GAFO,CAAR;AAGD;;AAED,OAAO,SAASC,eAAT,CAAyBC,gBAAzB,EAA2C;AAChD,MAAIA,gBAAgB,KAAK,KAAK,CAA9B,EAAiC;AAC/BA,IAAAA,gBAAgB,GAAG,EAAnB;AACD;;AAED,MAAIC,iBAAiB,GAAGD,gBAAxB;AAAA,MACIE,qBAAqB,GAAGD,iBAAiB,CAACE,gBAD9C;AAAA,MAEIA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAA/B,GAAmC,EAAnC,GAAwCA,qBAF/D;AAAA,MAGIE,sBAAsB,GAAGH,iBAAiB,CAACI,cAH/C;AAAA,MAIIA,cAAc,GAAGD,sBAAsB,KAAK,KAAK,CAAhC,GAAoCnB,eAApC,GAAsDmB,sBAJ3E;AAKA,SAAO,SAASE,YAAT,CAAsBC,SAAtB,EAAiCC,MAAjC,EAAyCC,OAAzC,EAAkD;AACvD,QAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;AACtBA,MAAAA,OAAO,GAAGJ,cAAV;AACD;;AAED,QAAIK,KAAK,GAAG;AACVxB,MAAAA,SAAS,EAAE,QADD;AAEVyB,MAAAA,gBAAgB,EAAE,EAFR;AAGVF,MAAAA,OAAO,EAAEG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB5B,eAAlB,EAAmCoB,cAAnC,CAHC;AAIVS,MAAAA,aAAa,EAAE,EAJL;AAKVC,MAAAA,QAAQ,EAAE;AACRR,QAAAA,SAAS,EAAEA,SADH;AAERC,QAAAA,MAAM,EAAEA;AAFA,OALA;AASVQ,MAAAA,UAAU,EAAE,EATF;AAUVC,MAAAA,MAAM,EAAE;AAVE,KAAZ;AAYA,QAAIC,gBAAgB,GAAG,EAAvB;AACA,QAAIC,WAAW,GAAG,KAAlB;AACA,QAAIC,QAAQ,GAAG;AACbV,MAAAA,KAAK,EAAEA,KADM;AAEbW,MAAAA,UAAU,EAAE,SAASA,UAAT,CAAoBC,gBAApB,EAAsC;AAChD,YAAIb,OAAO,GAAG,OAAOa,gBAAP,KAA4B,UAA5B,GAAyCA,gBAAgB,CAACZ,KAAK,CAACD,OAAP,CAAzD,GAA2Ea,gBAAzF;AACAC,QAAAA,sBAAsB;AACtBb,QAAAA,KAAK,CAACD,OAAN,GAAgBG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBR,cAAlB,EAAkCK,KAAK,CAACD,OAAxC,EAAiDA,OAAjD,CAAhB;AACAC,QAAAA,KAAK,CAACc,aAAN,GAAsB;AACpBjB,UAAAA,SAAS,EAAEvB,SAAS,CAACuB,SAAD,CAAT,GAAuB7B,iBAAiB,CAAC6B,SAAD,CAAxC,GAAsDA,SAAS,CAACkB,cAAV,GAA2B/C,iBAAiB,CAAC6B,SAAS,CAACkB,cAAX,CAA5C,GAAyE,EADtH;AAEpBjB,UAAAA,MAAM,EAAE9B,iBAAiB,CAAC8B,MAAD;AAFL,SAAtB,CAJgD,CAO7C;AACH;;AAEA,YAAIG,gBAAgB,GAAG/B,cAAc,CAACE,WAAW,CAAC,GAAG4C,MAAH,CAAUvB,gBAAV,EAA4BO,KAAK,CAACD,OAAN,CAActB,SAA1C,CAAD,CAAZ,CAArC,CAVgD,CAU0D;;AAE1GuB,QAAAA,KAAK,CAACC,gBAAN,GAAyBA,gBAAgB,CAACgB,MAAjB,CAAwB,UAAUC,CAAV,EAAa;AAC5D,iBAAOA,CAAC,CAACC,OAAT;AACD,SAFwB,CAAzB;AAGAC,QAAAA,kBAAkB;AAClB,eAAOV,QAAQ,CAACW,MAAT,EAAP;AACD,OAnBY;AAoBb;AACA;AACA;AACA;AACA;AACAC,MAAAA,WAAW,EAAE,SAASA,WAAT,GAAuB;AAClC,YAAIb,WAAJ,EAAiB;AACf;AACD;;AAED,YAAIc,eAAe,GAAGvB,KAAK,CAACK,QAA5B;AAAA,YACIR,SAAS,GAAG0B,eAAe,CAAC1B,SADhC;AAAA,YAEIC,MAAM,GAAGyB,eAAe,CAACzB,MAF7B,CALkC,CAOG;AACrC;;AAEA,YAAI,CAACnB,gBAAgB,CAACkB,SAAD,EAAYC,MAAZ,CAArB,EAA0C;AACxC;AACD,SAZiC,CAYhC;;;AAGFE,QAAAA,KAAK,CAACwB,KAAN,GAAc;AACZ3B,UAAAA,SAAS,EAAE/B,gBAAgB,CAAC+B,SAAD,EAAY5B,eAAe,CAAC6B,MAAD,CAA3B,EAAqCE,KAAK,CAACD,OAAN,CAAcrB,QAAd,KAA2B,OAAhE,CADf;AAEZoB,UAAAA,MAAM,EAAE/B,aAAa,CAAC+B,MAAD;AAFT,SAAd,CAfkC,CAkB/B;AACH;AACA;AACA;AACA;;AAEAE,QAAAA,KAAK,CAACyB,KAAN,GAAc,KAAd;AACAzB,QAAAA,KAAK,CAACxB,SAAN,GAAkBwB,KAAK,CAACD,OAAN,CAAcvB,SAAhC,CAzBkC,CAyBS;AAC3C;AACA;AACA;;AAEAwB,QAAAA,KAAK,CAACC,gBAAN,CAAuByB,OAAvB,CAA+B,UAAUC,QAAV,EAAoB;AACjD,iBAAO3B,KAAK,CAACI,aAAN,CAAoBuB,QAAQ,CAACC,IAA7B,IAAqC1B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBwB,QAAQ,CAACE,IAA3B,CAA5C;AACD,SAFD;;AAIA,aAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG9B,KAAK,CAACC,gBAAN,CAAuBnB,MAAnD,EAA2DgD,KAAK,EAAhE,EAAoE;AAClE,cAAI9B,KAAK,CAACyB,KAAN,KAAgB,IAApB,EAA0B;AACxBzB,YAAAA,KAAK,CAACyB,KAAN,GAAc,KAAd;AACAK,YAAAA,KAAK,GAAG,CAAC,CAAT;AACA;AACD;;AAED,cAAIC,qBAAqB,GAAG/B,KAAK,CAACC,gBAAN,CAAuB6B,KAAvB,CAA5B;AAAA,cACIE,EAAE,GAAGD,qBAAqB,CAACC,EAD/B;AAAA,cAEIC,sBAAsB,GAAGF,qBAAqB,CAAChC,OAFnD;AAAA,cAGImC,QAAQ,GAAGD,sBAAsB,KAAK,KAAK,CAAhC,GAAoC,EAApC,GAAyCA,sBAHxD;AAAA,cAIIL,IAAI,GAAGG,qBAAqB,CAACH,IAJjC;;AAMA,cAAI,OAAOI,EAAP,KAAc,UAAlB,EAA8B;AAC5BhC,YAAAA,KAAK,GAAGgC,EAAE,CAAC;AACThC,cAAAA,KAAK,EAAEA,KADE;AAETD,cAAAA,OAAO,EAAEmC,QAFA;AAGTN,cAAAA,IAAI,EAAEA,IAHG;AAITlB,cAAAA,QAAQ,EAAEA;AAJD,aAAD,CAAF,IAKFV,KALN;AAMD;AACF;AACF,OAjFY;AAkFb;AACA;AACAqB,MAAAA,MAAM,EAAElD,QAAQ,CAAC,YAAY;AAC3B,eAAO,IAAIgE,OAAJ,CAAY,UAAUC,OAAV,EAAmB;AACpC1B,UAAAA,QAAQ,CAACY,WAAT;AACAc,UAAAA,OAAO,CAACpC,KAAD,CAAP;AACD,SAHM,CAAP;AAID,OALe,CApFH;AA0FbqC,MAAAA,OAAO,EAAE,SAASA,OAAT,GAAmB;AAC1BxB,QAAAA,sBAAsB;AACtBJ,QAAAA,WAAW,GAAG,IAAd;AACD;AA7FY,KAAf;;AAgGA,QAAI,CAAC9B,gBAAgB,CAACkB,SAAD,EAAYC,MAAZ,CAArB,EAA0C;AACxC,aAAOY,QAAP;AACD;;AAEDA,IAAAA,QAAQ,CAACC,UAAT,CAAoBZ,OAApB,EAA6BuC,IAA7B,CAAkC,UAAUtC,KAAV,EAAiB;AACjD,UAAI,CAACS,WAAD,IAAgBV,OAAO,CAACwC,aAA5B,EAA2C;AACzCxC,QAAAA,OAAO,CAACwC,aAAR,CAAsBvC,KAAtB;AACD;AACF,KAJD,EAvHuD,CA2HnD;AACJ;AACA;AACA;AACA;;AAEA,aAASoB,kBAAT,GAA8B;AAC5BpB,MAAAA,KAAK,CAACC,gBAAN,CAAuByB,OAAvB,CAA+B,UAAUc,IAAV,EAAgB;AAC7C,YAAIZ,IAAI,GAAGY,IAAI,CAACZ,IAAhB;AAAA,YACIa,YAAY,GAAGD,IAAI,CAACzC,OADxB;AAAA,YAEIA,OAAO,GAAG0C,YAAY,KAAK,KAAK,CAAtB,GAA0B,EAA1B,GAA+BA,YAF7C;AAAA,YAGIC,MAAM,GAAGF,IAAI,CAACE,MAHlB;;AAKA,YAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChC,cAAIC,SAAS,GAAGD,MAAM,CAAC;AACrB1C,YAAAA,KAAK,EAAEA,KADc;AAErB4B,YAAAA,IAAI,EAAEA,IAFe;AAGrBlB,YAAAA,QAAQ,EAAEA,QAHW;AAIrBX,YAAAA,OAAO,EAAEA;AAJY,WAAD,CAAtB;;AAOA,cAAI6C,MAAM,GAAG,SAASA,MAAT,GAAkB,CAAE,CAAjC;;AAEApC,UAAAA,gBAAgB,CAACqC,IAAjB,CAAsBF,SAAS,IAAIC,MAAnC;AACD;AACF,OAlBD;AAmBD;;AAED,aAAS/B,sBAAT,GAAkC;AAChCL,MAAAA,gBAAgB,CAACkB,OAAjB,CAAyB,UAAUM,EAAV,EAAc;AACrC,eAAOA,EAAE,EAAT;AACD,OAFD;AAGAxB,MAAAA,gBAAgB,GAAG,EAAnB;AACD;;AAED,WAAOE,QAAP;AACD,GA/JD;AAgKD;AACD,OAAO,IAAId,YAAY,GAAG,aAAaP,eAAe,EAA/C,C,CAAmD;;AAE1D,SAAShB,cAAT", "sourcesContent": ["import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };"]}, "metadata": {}, "sourceType": "module"}