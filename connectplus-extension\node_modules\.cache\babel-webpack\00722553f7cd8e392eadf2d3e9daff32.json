{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Injector, NgModule, inject } from '@angular/core';\nimport { StateToken, actionMatcher, InitState, UpdateState, getValue, setValue, NGXS_PLUGINS } from '@ngxs/store';\nimport { isPlatformServer, isPlatformBrowser } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nconst NG_DEV_MODE$4 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst NGXS_STORAGE_PLUGIN_OPTIONS = new InjectionToken(NG_DEV_MODE$4 ? 'NGXS_STORAGE_PLUGIN_OPTIONS' : '');\nconst STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE$4 ? 'STORAGE_ENGINE' : '');\n/**\n * The following key is used to store the entire serialized\n * state when there's no specific state provided.\n */\n\nconst DEFAULT_STATE_KEY = '@@STATE';\n\nfunction storageOptionsFactory(options) {\n  return Object.assign({\n    key: [DEFAULT_STATE_KEY],\n    storage: 0\n    /* LocalStorage */\n    ,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    beforeSerialize: obj => obj,\n    afterDeserialize: obj => obj\n  }, options);\n}\n\nfunction engineFactory(options, platformId) {\n  if (isPlatformServer(platformId)) {\n    return null;\n  }\n\n  if (options.storage === 0\n  /* LocalStorage */\n  ) {\n    return localStorage;\n  } else if (options.storage === 1\n  /* SessionStorage */\n  ) {\n    return sessionStorage;\n  }\n\n  return null;\n}\n\nfunction getStorageKey(key, options) {\n  // Prepends the `namespace` option to any key if it's been provided by a user.\n  // So `@@STATE` becomes `my-app:@@STATE`.\n  return options && options.namespace ? `${options.namespace}:${key}` : key;\n}\n/** Determines whether the provided key has the following structure. */\n\n\nfunction isKeyWithExplicitEngine(key) {\n  return key != null && !!key.engine;\n}\n/** This symbol is used to store the metadata on state classes. */\n\n\nconst META_OPTIONS_KEY = 'NGXS_OPTIONS_META';\n\nfunction exctractStringKey(storageKey) {\n  // Extract the actual key out of the `{ key, engine }` structure.\n  if (isKeyWithExplicitEngine(storageKey)) {\n    storageKey = storageKey.key;\n  } // Given the `storageKey` is a class, for instance, `AuthState`.\n  // We should retrieve its metadata and the `name` property.\n  // The `name` property might be a string (state name) or a state token.\n\n\n  if (storageKey.hasOwnProperty(META_OPTIONS_KEY)) {\n    storageKey = storageKey[META_OPTIONS_KEY].name;\n  }\n\n  return storageKey instanceof StateToken ? storageKey.getName() : storageKey;\n}\n\nconst NG_DEV_MODE$3 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst FINAL_NGXS_STORAGE_PLUGIN_OPTIONS = new InjectionToken(NG_DEV_MODE$3 ? 'FINAL_NGXS_STORAGE_PLUGIN_OPTIONS' : '');\n\nfunction createFinalStoragePluginOptions(injector, options) {\n  const storageKeys = Array.isArray(options.key) ? options.key : [options.key];\n  const keysWithEngines = storageKeys.map(storageKey => {\n    const key = exctractStringKey(storageKey);\n    const engine = isKeyWithExplicitEngine(storageKey) ? injector.get(storageKey.engine) : injector.get(STORAGE_ENGINE);\n    return {\n      key,\n      engine\n    };\n  });\n  return Object.assign(Object.assign({}, options), {\n    keysWithEngines\n  });\n}\n\nconst NG_DEV_MODE$2 = typeof ngDevMode === 'undefined' || ngDevMode;\n\nclass NgxsStoragePlugin {\n  constructor(_options, _platformId) {\n    this._options = _options;\n    this._platformId = _platformId;\n    this._keysWithEngines = this._options.keysWithEngines; // We default to `[DEFAULT_STATE_KEY]` if the user explicitly does not provide the `key` option.\n\n    this._usesDefaultStateKey = this._keysWithEngines.length === 1 && this._keysWithEngines[0].key === DEFAULT_STATE_KEY;\n  }\n\n  handle(state, event, next) {\n    var _a;\n\n    if (isPlatformServer(this._platformId)) {\n      return next(state, event);\n    }\n\n    const matches = actionMatcher(event);\n    const isInitAction = matches(InitState);\n    const isUpdateAction = matches(UpdateState);\n    const isInitOrUpdateAction = isInitAction || isUpdateAction;\n    let hasMigration = false;\n\n    if (isInitOrUpdateAction) {\n      const addedStates = isUpdateAction && event.addedStates;\n\n      for (const {\n        key,\n        engine\n      } of this._keysWithEngines) {\n        // We're checking what states have been added by NGXS and if any of these states should be handled by\n        // the storage plugin. For instance, we only want to deserialize the `auth` state, NGXS has added\n        // the `user` state, the storage plugin will be rerun and will do redundant deserialization.\n        // `usesDefaultStateKey` is necessary to check since `event.addedStates` never contains `@@STATE`.\n        if (!this._usesDefaultStateKey && addedStates) {\n          // We support providing keys that can be deeply nested via dot notation, for instance,\n          // `keys: ['myState.myProperty']` is a valid key.\n          // The state name should always go first. The below code checks if the `key` includes dot\n          // notation and extracts the state name out of the key.\n          // Given the `key` is `myState.myProperty`, the `addedStates` will only contain `myState`.\n          const dotNotationIndex = key.indexOf(DOT);\n          const stateName = dotNotationIndex > -1 ? key.slice(0, dotNotationIndex) : key;\n\n          if (!addedStates.hasOwnProperty(stateName)) {\n            continue;\n          }\n        }\n\n        const storageKey = getStorageKey(key, this._options);\n        let storedValue = engine.getItem(storageKey);\n\n        if (storedValue !== 'undefined' && storedValue != null) {\n          try {\n            const newVal = this._options.deserialize(storedValue);\n\n            storedValue = this._options.afterDeserialize(newVal, key);\n          } catch (_b) {\n            if (NG_DEV_MODE$2) {\n              console.error(`Error ocurred while deserializing the ${storageKey} store value, falling back to empty object, the value obtained from the store: `, storedValue);\n            }\n\n            storedValue = {};\n          }\n\n          (_a = this._options.migrations) === null || _a === void 0 ? void 0 : _a.forEach(strategy => {\n            const versionMatch = strategy.version === getValue(storedValue, strategy.versionKey || 'version');\n            const keyMatch = !strategy.key && this._usesDefaultStateKey || strategy.key === key;\n\n            if (versionMatch && keyMatch) {\n              storedValue = strategy.migrate(storedValue);\n              hasMigration = true;\n            }\n          });\n\n          if (!this._usesDefaultStateKey) {\n            state = setValue(state, key, storedValue);\n          } else {\n            // The `UpdateState` action is dispatched whenever the feature\n            // state is added. The condition below is satisfied only when\n            // the `UpdateState` action is dispatched. Let's consider two states:\n            // `counter` and `@ngxs/router-plugin` state. When we call `NgxsModule.forRoot()`,\n            // `CounterState` is provided at the root level, while `@ngxs/router-plugin`\n            // is provided as a feature state. Beforehand, the storage plugin may have\n            // stored the value of the counter state as `10`. If `CounterState` implements\n            // the `ngxsOnInit` hook and calls `ctx.setState(999)`, the storage plugin\n            // will rehydrate the entire state when the `RouterState` is registered.\n            // Consequently, the `counter` state will revert back to `10` instead of `999`.\n            if (storedValue && addedStates && Object.keys(addedStates).length > 0) {\n              storedValue = Object.keys(addedStates).reduce((accumulator, addedState) => {\n                // The `storedValue` can be equal to the entire state when the default\n                // state key is used. However, if `addedStates` only contains the `router` value,\n                // we only want to merge the state with the `router` value.\n                // Let's assume that the `storedValue` is an object:\n                // `{ counter: 10, router: {...} }`\n                // This will pick only the `router` object from the `storedValue` and `counter`\n                // state will not be rehydrated unnecessary.\n                if (storedValue.hasOwnProperty(addedState)) {\n                  accumulator[addedState] = storedValue[addedState];\n                }\n\n                return accumulator;\n              }, {});\n            }\n\n            state = Object.assign(Object.assign({}, state), storedValue);\n          }\n        }\n      }\n    }\n\n    return next(state, event).pipe(tap(nextState => {\n      if (isInitOrUpdateAction && !hasMigration) {\n        return;\n      }\n\n      for (const {\n        key,\n        engine\n      } of this._keysWithEngines) {\n        let storedValue = nextState;\n        const storageKey = getStorageKey(key, this._options);\n\n        if (key !== DEFAULT_STATE_KEY) {\n          storedValue = getValue(nextState, key);\n        }\n\n        try {\n          const newStoredValue = this._options.beforeSerialize(storedValue, key);\n\n          engine.setItem(storageKey, this._options.serialize(newStoredValue));\n        } catch (error) {\n          if (NG_DEV_MODE$2) {\n            if (error && (error.name === 'QuotaExceededError' || error.name === 'NS_ERROR_DOM_QUOTA_REACHED')) {\n              console.error(`The ${storageKey} store value exceeds the browser storage quota: `, storedValue);\n            } else {\n              console.error(`Error ocurred while serializing the ${storageKey} store value, value not updated, the value obtained from the store: `, storedValue);\n            }\n          }\n        }\n      }\n    }));\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsStoragePlugin.ɵfac = function NgxsStoragePlugin_Factory(t) {\n  return new (t || NgxsStoragePlugin)(i0.ɵɵinject(FINAL_NGXS_STORAGE_PLUGIN_OPTIONS), i0.ɵɵinject(PLATFORM_ID));\n};\n/** @nocollapse */\n\n\nNgxsStoragePlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxsStoragePlugin,\n  factory: NgxsStoragePlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsStoragePlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [FINAL_NGXS_STORAGE_PLUGIN_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n\nconst DOT = '.';\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst USER_OPTIONS = new InjectionToken(NG_DEV_MODE$1 ? 'USER_OPTIONS' : '');\n\nclass NgxsStoragePluginModule {\n  static forRoot(options) {\n    return {\n      ngModule: NgxsStoragePluginModule,\n      providers: [{\n        provide: NGXS_PLUGINS,\n        useClass: NgxsStoragePlugin,\n        multi: true\n      }, {\n        provide: USER_OPTIONS,\n        useValue: options\n      }, {\n        provide: NGXS_STORAGE_PLUGIN_OPTIONS,\n        useFactory: storageOptionsFactory,\n        deps: [USER_OPTIONS]\n      }, {\n        provide: STORAGE_ENGINE,\n        useFactory: engineFactory,\n        deps: [NGXS_STORAGE_PLUGIN_OPTIONS, PLATFORM_ID]\n      }, {\n        provide: FINAL_NGXS_STORAGE_PLUGIN_OPTIONS,\n        useFactory: createFinalStoragePluginOptions,\n        deps: [Injector, NGXS_STORAGE_PLUGIN_OPTIONS]\n      }]\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nNgxsStoragePluginModule.ɵfac = function NgxsStoragePluginModule_Factory(t) {\n  return new (t || NgxsStoragePluginModule)();\n};\n/** @nocollapse */\n\n\nNgxsStoragePluginModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxsStoragePluginModule\n});\n/** @nocollapse */\n\nNgxsStoragePluginModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxsStoragePluginModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\nconst LOCAL_STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE ? 'LOCAL_STORAGE_ENGINE' : '', {\n  providedIn: 'root',\n  factory: () => isPlatformBrowser(inject(PLATFORM_ID)) ? localStorage : null\n});\nconst SESSION_STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE ? 'SESSION_STORAGE_ENGINE' : '', {\n  providedIn: 'root',\n  factory: () => isPlatformBrowser(inject(PLATFORM_ID)) ? sessionStorage : null\n});\n/**\n * The public api for consumers of @ngxs/storage-plugin\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LOCAL_STORAGE_ENGINE, NGXS_STORAGE_PLUGIN_OPTIONS, NgxsStoragePlugin, NgxsStoragePluginModule, SESSION_STORAGE_ENGINE, STORAGE_ENGINE };", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/node_modules/@ngxs/storage-plugin/fesm2015/ngxs-storage-plugin.js"], "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Injector", "NgModule", "inject", "StateToken", "actionMatcher", "InitState", "UpdateState", "getValue", "setValue", "NGXS_PLUGINS", "isPlatformServer", "isPlatformBrowser", "tap", "NG_DEV_MODE$4", "ngDevMode", "NGXS_STORAGE_PLUGIN_OPTIONS", "STORAGE_ENGINE", "DEFAULT_STATE_KEY", "storageOptionsFactory", "options", "Object", "assign", "key", "storage", "serialize", "JSON", "stringify", "deserialize", "parse", "beforeSerialize", "obj", "afterDeserialize", "engineFactory", "platformId", "localStorage", "sessionStorage", "getStorageKey", "namespace", "isKeyWithExplicitEngine", "engine", "META_OPTIONS_KEY", "exctractStringKey", "storageKey", "hasOwnProperty", "name", "getName", "NG_DEV_MODE$3", "FINAL_NGXS_STORAGE_PLUGIN_OPTIONS", "createFinalStoragePluginOptions", "injector", "storageKeys", "Array", "isArray", "keysWithEngines", "map", "get", "NG_DEV_MODE$2", "NgxsStoragePlugin", "constructor", "_options", "_platformId", "_keysWithEngines", "_usesDefaultStateKey", "length", "handle", "state", "event", "next", "_a", "matches", "isInitAction", "isUpdateAction", "isInitOrUpdateAction", "hasMigration", "addedStates", "dotNotationIndex", "indexOf", "DOT", "stateName", "slice", "storedValue", "getItem", "newVal", "_b", "console", "error", "migrations", "for<PERSON>ach", "strategy", "versionMatch", "version", "version<PERSON>ey", "keyMatch", "migrate", "keys", "reduce", "accumulator", "addedState", "pipe", "nextState", "newStoredValue", "setItem", "ɵfac", "ɵprov", "type", "undefined", "decorators", "args", "NG_DEV_MODE$1", "USER_OPTIONS", "NgxsStoragePluginModule", "forRoot", "ngModule", "providers", "provide", "useClass", "multi", "useValue", "useFactory", "deps", "ɵmod", "ɵinj", "NG_DEV_MODE", "LOCAL_STORAGE_ENGINE", "providedIn", "factory", "SESSION_STORAGE_ENGINE"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,WAAzB,EAAsCC,UAAtC,EAAkDC,MAAlD,EAA0DC,QAA1D,EAAoEC,QAApE,EAA8EC,MAA9E,QAA4F,eAA5F;AACA,SAASC,UAAT,EAAqBC,aAArB,EAAoCC,SAApC,EAA+CC,WAA/C,EAA4DC,QAA5D,EAAsEC,QAAtE,EAAgFC,YAAhF,QAAoG,aAApG;AACA,SAASC,gBAAT,EAA2BC,iBAA3B,QAAoD,iBAApD;AACA,SAASC,GAAT,QAAoB,gBAApB;AAEA,MAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAA1D;AACA,MAAMC,2BAA2B,GAAG,IAAInB,cAAJ,CAAmBiB,aAAa,GAAG,6BAAH,GAAmC,EAAnE,CAApC;AACA,MAAMG,cAAc,GAAG,IAAIpB,cAAJ,CAAmBiB,aAAa,GAAG,gBAAH,GAAsB,EAAtD,CAAvB;AAEA;AACA;AACA;AACA;;AACA,MAAMI,iBAAiB,GAAG,SAA1B;;AACA,SAASC,qBAAT,CAA+BC,OAA/B,EAAwC;AACpC,SAAOC,MAAM,CAACC,MAAP,CAAc;AAAEC,IAAAA,GAAG,EAAE,CAACL,iBAAD,CAAP;AAA4BM,IAAAA,OAAO,EAAE;AAAE;AAAvC;AAA2DC,IAAAA,SAAS,EAAEC,IAAI,CAACC,SAA3E;AAAsFC,IAAAA,WAAW,EAAEF,IAAI,CAACG,KAAxG;AAA+GC,IAAAA,eAAe,EAAEC,GAAG,IAAIA,GAAvI;AAA4IC,IAAAA,gBAAgB,EAAED,GAAG,IAAIA;AAArK,GAAd,EAA0LX,OAA1L,CAAP;AACH;;AACD,SAASa,aAAT,CAAuBb,OAAvB,EAAgCc,UAAhC,EAA4C;AACxC,MAAIvB,gBAAgB,CAACuB,UAAD,CAApB,EAAkC;AAC9B,WAAO,IAAP;AACH;;AACD,MAAId,OAAO,CAACI,OAAR,KAAoB;AAAE;AAA1B,IAA8C;AAC1C,WAAOW,YAAP;AACH,GAFD,MAGK,IAAIf,OAAO,CAACI,OAAR,KAAoB;AAAE;AAA1B,IAAgD;AACjD,WAAOY,cAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,aAAT,CAAuBd,GAAvB,EAA4BH,OAA5B,EAAqC;AACjC;AACA;AACA,SAAOA,OAAO,IAAIA,OAAO,CAACkB,SAAnB,GAAgC,GAAElB,OAAO,CAACkB,SAAU,IAAGf,GAAI,EAA3D,GAA+DA,GAAtE;AACH;AAED;;;AACA,SAASgB,uBAAT,CAAiChB,GAAjC,EAAsC;AAClC,SAAOA,GAAG,IAAI,IAAP,IAAe,CAAC,CAACA,GAAG,CAACiB,MAA5B;AACH;AACD;;;AACA,MAAMC,gBAAgB,GAAG,mBAAzB;;AACA,SAASC,iBAAT,CAA2BC,UAA3B,EAAuC;AACnC;AACA,MAAIJ,uBAAuB,CAACI,UAAD,CAA3B,EAAyC;AACrCA,IAAAA,UAAU,GAAGA,UAAU,CAACpB,GAAxB;AACH,GAJkC,CAKnC;AACA;AACA;;;AACA,MAAIoB,UAAU,CAACC,cAAX,CAA0BH,gBAA1B,CAAJ,EAAiD;AAC7CE,IAAAA,UAAU,GAAGA,UAAU,CAACF,gBAAD,CAAV,CAA6BI,IAA1C;AACH;;AACD,SAAOF,UAAU,YAAYvC,UAAtB,GAAmCuC,UAAU,CAACG,OAAX,EAAnC,GAA0DH,UAAjE;AACH;;AAED,MAAMI,aAAa,GAAG,OAAOhC,SAAP,KAAqB,WAArB,IAAoCA,SAA1D;AACA,MAAMiC,iCAAiC,GAAG,IAAInD,cAAJ,CAAmBkD,aAAa,GAAG,mCAAH,GAAyC,EAAzE,CAA1C;;AACA,SAASE,+BAAT,CAAyCC,QAAzC,EAAmD9B,OAAnD,EAA4D;AACxD,QAAM+B,WAAW,GAAGC,KAAK,CAACC,OAAN,CAAcjC,OAAO,CAACG,GAAtB,IAA6BH,OAAO,CAACG,GAArC,GAA2C,CAACH,OAAO,CAACG,GAAT,CAA/D;AACA,QAAM+B,eAAe,GAAGH,WAAW,CAACI,GAAZ,CAAiBZ,UAAD,IAAgB;AACpD,UAAMpB,GAAG,GAAGmB,iBAAiB,CAACC,UAAD,CAA7B;AACA,UAAMH,MAAM,GAAGD,uBAAuB,CAACI,UAAD,CAAvB,GACTO,QAAQ,CAACM,GAAT,CAAab,UAAU,CAACH,MAAxB,CADS,GAETU,QAAQ,CAACM,GAAT,CAAavC,cAAb,CAFN;AAGA,WAAO;AAAEM,MAAAA,GAAF;AAAOiB,MAAAA;AAAP,KAAP;AACH,GANuB,CAAxB;AAOA,SAAOnB,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,OAAlB,CAAd,EAA0C;AAAEkC,IAAAA;AAAF,GAA1C,CAAP;AACH;;AAED,MAAMG,aAAa,GAAG,OAAO1C,SAAP,KAAqB,WAArB,IAAoCA,SAA1D;;AACA,MAAM2C,iBAAN,CAAwB;AACpBC,EAAAA,WAAW,CAACC,QAAD,EAAWC,WAAX,EAAwB;AAC/B,SAAKD,QAAL,GAAgBA,QAAhB;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,gBAAL,GAAwB,KAAKF,QAAL,CAAcN,eAAtC,CAH+B,CAI/B;;AACA,SAAKS,oBAAL,GAA4B,KAAKD,gBAAL,CAAsBE,MAAtB,KAAiC,CAAjC,IAAsC,KAAKF,gBAAL,CAAsB,CAAtB,EAAyBvC,GAAzB,KAAiCL,iBAAnG;AACH;;AACD+C,EAAAA,MAAM,CAACC,KAAD,EAAQC,KAAR,EAAeC,IAAf,EAAqB;AACvB,QAAIC,EAAJ;;AACA,QAAI1D,gBAAgB,CAAC,KAAKkD,WAAN,CAApB,EAAwC;AACpC,aAAOO,IAAI,CAACF,KAAD,EAAQC,KAAR,CAAX;AACH;;AACD,UAAMG,OAAO,GAAGjE,aAAa,CAAC8D,KAAD,CAA7B;AACA,UAAMI,YAAY,GAAGD,OAAO,CAAChE,SAAD,CAA5B;AACA,UAAMkE,cAAc,GAAGF,OAAO,CAAC/D,WAAD,CAA9B;AACA,UAAMkE,oBAAoB,GAAGF,YAAY,IAAIC,cAA7C;AACA,QAAIE,YAAY,GAAG,KAAnB;;AACA,QAAID,oBAAJ,EAA0B;AACtB,YAAME,WAAW,GAAGH,cAAc,IAAIL,KAAK,CAACQ,WAA5C;;AACA,WAAK,MAAM;AAAEpD,QAAAA,GAAF;AAAOiB,QAAAA;AAAP,OAAX,IAA8B,KAAKsB,gBAAnC,EAAqD;AACjD;AACA;AACA;AACA;AACA,YAAI,CAAC,KAAKC,oBAAN,IAA8BY,WAAlC,EAA+C;AAC3C;AACA;AACA;AACA;AACA;AACA,gBAAMC,gBAAgB,GAAGrD,GAAG,CAACsD,OAAJ,CAAYC,GAAZ,CAAzB;AACA,gBAAMC,SAAS,GAAGH,gBAAgB,GAAG,CAAC,CAApB,GAAwBrD,GAAG,CAACyD,KAAJ,CAAU,CAAV,EAAaJ,gBAAb,CAAxB,GAAyDrD,GAA3E;;AACA,cAAI,CAACoD,WAAW,CAAC/B,cAAZ,CAA2BmC,SAA3B,CAAL,EAA4C;AACxC;AACH;AACJ;;AACD,cAAMpC,UAAU,GAAGN,aAAa,CAACd,GAAD,EAAM,KAAKqC,QAAX,CAAhC;AACA,YAAIqB,WAAW,GAAGzC,MAAM,CAAC0C,OAAP,CAAevC,UAAf,CAAlB;;AACA,YAAIsC,WAAW,KAAK,WAAhB,IAA+BA,WAAW,IAAI,IAAlD,EAAwD;AACpD,cAAI;AACA,kBAAME,MAAM,GAAG,KAAKvB,QAAL,CAAchC,WAAd,CAA0BqD,WAA1B,CAAf;;AACAA,YAAAA,WAAW,GAAG,KAAKrB,QAAL,CAAc5B,gBAAd,CAA+BmD,MAA/B,EAAuC5D,GAAvC,CAAd;AACH,WAHD,CAIA,OAAO6D,EAAP,EAAW;AACP,gBAAI3B,aAAJ,EAAmB;AACf4B,cAAAA,OAAO,CAACC,KAAR,CAAe,yCAAwC3C,UAAW,iFAAlE,EAAoJsC,WAApJ;AACH;;AACDA,YAAAA,WAAW,GAAG,EAAd;AACH;;AACD,WAACZ,EAAE,GAAG,KAAKT,QAAL,CAAc2B,UAApB,MAAoC,IAApC,IAA4ClB,EAAE,KAAK,KAAK,CAAxD,GAA4D,KAAK,CAAjE,GAAqEA,EAAE,CAACmB,OAAH,CAAWC,QAAQ,IAAI;AACxF,kBAAMC,YAAY,GAAGD,QAAQ,CAACE,OAAT,KAAqBnF,QAAQ,CAACyE,WAAD,EAAcQ,QAAQ,CAACG,UAAT,IAAuB,SAArC,CAAlD;AACA,kBAAMC,QAAQ,GAAI,CAACJ,QAAQ,CAAClE,GAAV,IAAiB,KAAKwC,oBAAvB,IAAgD0B,QAAQ,CAAClE,GAAT,KAAiBA,GAAlF;;AACA,gBAAImE,YAAY,IAAIG,QAApB,EAA8B;AAC1BZ,cAAAA,WAAW,GAAGQ,QAAQ,CAACK,OAAT,CAAiBb,WAAjB,CAAd;AACAP,cAAAA,YAAY,GAAG,IAAf;AACH;AACJ,WAPoE,CAArE;;AAQA,cAAI,CAAC,KAAKX,oBAAV,EAAgC;AAC5BG,YAAAA,KAAK,GAAGzD,QAAQ,CAACyD,KAAD,EAAQ3C,GAAR,EAAa0D,WAAb,CAAhB;AACH,WAFD,MAGK;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAIA,WAAW,IAAIN,WAAf,IAA8BtD,MAAM,CAAC0E,IAAP,CAAYpB,WAAZ,EAAyBX,MAAzB,GAAkC,CAApE,EAAuE;AACnEiB,cAAAA,WAAW,GAAG5D,MAAM,CAAC0E,IAAP,CAAYpB,WAAZ,EAAyBqB,MAAzB,CAAgC,CAACC,WAAD,EAAcC,UAAd,KAA6B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAIjB,WAAW,CAACrC,cAAZ,CAA2BsD,UAA3B,CAAJ,EAA4C;AACxCD,kBAAAA,WAAW,CAACC,UAAD,CAAX,GAA0BjB,WAAW,CAACiB,UAAD,CAArC;AACH;;AACD,uBAAOD,WAAP;AACH,eAZa,EAYX,EAZW,CAAd;AAaH;;AACD/B,YAAAA,KAAK,GAAG7C,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB4C,KAAlB,CAAd,EAAwCe,WAAxC,CAAR;AACH;AACJ;AACJ;AACJ;;AACD,WAAOb,IAAI,CAACF,KAAD,EAAQC,KAAR,CAAJ,CAAmBgC,IAAnB,CAAwBtF,GAAG,CAACuF,SAAS,IAAI;AAC5C,UAAI3B,oBAAoB,IAAI,CAACC,YAA7B,EAA2C;AACvC;AACH;;AACD,WAAK,MAAM;AAAEnD,QAAAA,GAAF;AAAOiB,QAAAA;AAAP,OAAX,IAA8B,KAAKsB,gBAAnC,EAAqD;AACjD,YAAImB,WAAW,GAAGmB,SAAlB;AACA,cAAMzD,UAAU,GAAGN,aAAa,CAACd,GAAD,EAAM,KAAKqC,QAAX,CAAhC;;AACA,YAAIrC,GAAG,KAAKL,iBAAZ,EAA+B;AAC3B+D,UAAAA,WAAW,GAAGzE,QAAQ,CAAC4F,SAAD,EAAY7E,GAAZ,CAAtB;AACH;;AACD,YAAI;AACA,gBAAM8E,cAAc,GAAG,KAAKzC,QAAL,CAAc9B,eAAd,CAA8BmD,WAA9B,EAA2C1D,GAA3C,CAAvB;;AACAiB,UAAAA,MAAM,CAAC8D,OAAP,CAAe3D,UAAf,EAA2B,KAAKiB,QAAL,CAAcnC,SAAd,CAAwB4E,cAAxB,CAA3B;AACH,SAHD,CAIA,OAAOf,KAAP,EAAc;AACV,cAAI7B,aAAJ,EAAmB;AACf,gBAAI6B,KAAK,KACJA,KAAK,CAACzC,IAAN,KAAe,oBAAf,IACGyC,KAAK,CAACzC,IAAN,KAAe,4BAFd,CAAT,EAEsD;AAClDwC,cAAAA,OAAO,CAACC,KAAR,CAAe,OAAM3C,UAAW,kDAAhC,EAAmFsC,WAAnF;AACH,aAJD,MAKK;AACDI,cAAAA,OAAO,CAACC,KAAR,CAAe,uCAAsC3C,UAAW,sEAAhE,EAAuIsC,WAAvI;AACH;AACJ;AACJ;AACJ;AACJ,KA3BiC,CAA3B,CAAP;AA4BH;;AAxHmB;AA0HxB;;;AAAmBvB,iBAAiB,CAAC6C,IAAlB;AAAA,mBAA+G7C,iBAA/G,EAAqG9D,EAArG,UAAkJoD,iCAAlJ,GAAqGpD,EAArG,UAAgME,WAAhM;AAAA;AACnB;;;AAAmB4D,iBAAiB,CAAC8C,KAAlB,kBADqG5G,EACrG;AAAA,SAAmH8D,iBAAnH;AAAA,WAAmHA,iBAAnH;AAAA;;AACnB;AAAA,qDAFwH9D,EAExH,mBAA4F8D,iBAA5F,EAA2H,CAAC;AAChH+C,IAAAA,IAAI,EAAE1G;AAD0G,GAAD,CAA3H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE0G,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DF,QAAAA,IAAI,EAAEzG,MADwD;AAE9D4G,QAAAA,IAAI,EAAE,CAAC5D,iCAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEyD,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCF,QAAAA,IAAI,EAAEzG,MAD4B;AAElC4G,QAAAA,IAAI,EAAE,CAAC9G,WAAD;AAF4B,OAAD;AAA/B,KAH2B,CAAP;AAMlB,GARxB;AAAA;;AASA,MAAMgF,GAAG,GAAG,GAAZ;AAEA,MAAM+B,aAAa,GAAG,OAAO9F,SAAP,KAAqB,WAArB,IAAoCA,SAA1D;AACA,MAAM+F,YAAY,GAAG,IAAIjH,cAAJ,CAAmBgH,aAAa,GAAG,cAAH,GAAoB,EAApD,CAArB;;AACA,MAAME,uBAAN,CAA8B;AACZ,SAAPC,OAAO,CAAC5F,OAAD,EAAU;AACpB,WAAO;AACH6F,MAAAA,QAAQ,EAAEF,uBADP;AAEHG,MAAAA,SAAS,EAAE,CACP;AACIC,QAAAA,OAAO,EAAEzG,YADb;AAEI0G,QAAAA,QAAQ,EAAE1D,iBAFd;AAGI2D,QAAAA,KAAK,EAAE;AAHX,OADO,EAMP;AACIF,QAAAA,OAAO,EAAEL,YADb;AAEIQ,QAAAA,QAAQ,EAAElG;AAFd,OANO,EAUP;AACI+F,QAAAA,OAAO,EAAEnG,2BADb;AAEIuG,QAAAA,UAAU,EAAEpG,qBAFhB;AAGIqG,QAAAA,IAAI,EAAE,CAACV,YAAD;AAHV,OAVO,EAeP;AACIK,QAAAA,OAAO,EAAElG,cADb;AAEIsG,QAAAA,UAAU,EAAEtF,aAFhB;AAGIuF,QAAAA,IAAI,EAAE,CAACxG,2BAAD,EAA8BlB,WAA9B;AAHV,OAfO,EAoBP;AACIqH,QAAAA,OAAO,EAAEnE,iCADb;AAEIuE,QAAAA,UAAU,EAAEtE,+BAFhB;AAGIuE,QAAAA,IAAI,EAAE,CAACvH,QAAD,EAAWe,2BAAX;AAHV,OApBO;AAFR,KAAP;AA6BH;;AA/ByB;AAiC9B;;;AAAmB+F,uBAAuB,CAACR,IAAxB;AAAA,mBAAqHQ,uBAArH;AAAA;AACnB;;;AAAmBA,uBAAuB,CAACU,IAAxB,kBAjDqG7H,EAiDrG;AAAA,QAAsHmH;AAAtH;AACnB;;AAAmBA,uBAAuB,CAACW,IAAxB,kBAlDqG9H,EAkDrG;;AACnB;AAAA,qDAnDwHA,EAmDxH,mBAA4FmH,uBAA5F,EAAiI,CAAC;AACtHN,IAAAA,IAAI,EAAEvG;AADgH,GAAD,CAAjI;AAAA;;AAIA,MAAMyH,WAAW,GAAG,OAAO5G,SAAP,KAAqB,WAArB,IAAoCA,SAAxD;AACA,MAAM6G,oBAAoB,GAAG,IAAI/H,cAAJ,CAAmB8H,WAAW,GAAG,sBAAH,GAA4B,EAA1D,EAA8D;AACvFE,EAAAA,UAAU,EAAE,MAD2E;AAEvFC,EAAAA,OAAO,EAAE,MAAOlH,iBAAiB,CAACT,MAAM,CAACL,WAAD,CAAP,CAAjB,GAAyCqC,YAAzC,GAAwD;AAFe,CAA9D,CAA7B;AAIA,MAAM4F,sBAAsB,GAAG,IAAIlI,cAAJ,CAAmB8H,WAAW,GAAG,wBAAH,GAA8B,EAA5D,EAAgE;AAC3FE,EAAAA,UAAU,EAAE,MAD+E;AAE3FC,EAAAA,OAAO,EAAE,MAAOlH,iBAAiB,CAACT,MAAM,CAACL,WAAD,CAAP,CAAjB,GAAyCsC,cAAzC,GAA0D;AAFiB,CAAhE,CAA/B;AAKA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASwF,oBAAT,EAA+B5G,2BAA/B,EAA4D0C,iBAA5D,EAA+EqD,uBAA/E,EAAwGgB,sBAAxG,EAAgI9G,cAAhI", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Injector, NgModule, inject } from '@angular/core';\nimport { StateToken, actionMatcher, InitState, UpdateState, getValue, setValue, NGXS_PLUGINS } from '@ngxs/store';\nimport { isPlatformServer, isPlatformBrowser } from '@angular/common';\nimport { tap } from 'rxjs/operators';\n\nconst NG_DEV_MODE$4 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst NGXS_STORAGE_PLUGIN_OPTIONS = new InjectionToken(NG_DEV_MODE$4 ? 'NGXS_STORAGE_PLUGIN_OPTIONS' : '');\nconst STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE$4 ? 'STORAGE_ENGINE' : '');\n\n/**\n * The following key is used to store the entire serialized\n * state when there's no specific state provided.\n */\nconst DEFAULT_STATE_KEY = '@@STATE';\nfunction storageOptionsFactory(options) {\n    return Object.assign({ key: [DEFAULT_STATE_KEY], storage: 0 /* LocalStorage */, serialize: JSON.stringify, deserialize: JSON.parse, beforeSerialize: obj => obj, afterDeserialize: obj => obj }, options);\n}\nfunction engineFactory(options, platformId) {\n    if (isPlatformServer(platformId)) {\n        return null;\n    }\n    if (options.storage === 0 /* LocalStorage */) {\n        return localStorage;\n    }\n    else if (options.storage === 1 /* SessionStorage */) {\n        return sessionStorage;\n    }\n    return null;\n}\nfunction getStorageKey(key, options) {\n    // Prepends the `namespace` option to any key if it's been provided by a user.\n    // So `@@STATE` becomes `my-app:@@STATE`.\n    return options && options.namespace ? `${options.namespace}:${key}` : key;\n}\n\n/** Determines whether the provided key has the following structure. */\nfunction isKeyWithExplicitEngine(key) {\n    return key != null && !!key.engine;\n}\n/** This symbol is used to store the metadata on state classes. */\nconst META_OPTIONS_KEY = 'NGXS_OPTIONS_META';\nfunction exctractStringKey(storageKey) {\n    // Extract the actual key out of the `{ key, engine }` structure.\n    if (isKeyWithExplicitEngine(storageKey)) {\n        storageKey = storageKey.key;\n    }\n    // Given the `storageKey` is a class, for instance, `AuthState`.\n    // We should retrieve its metadata and the `name` property.\n    // The `name` property might be a string (state name) or a state token.\n    if (storageKey.hasOwnProperty(META_OPTIONS_KEY)) {\n        storageKey = storageKey[META_OPTIONS_KEY].name;\n    }\n    return storageKey instanceof StateToken ? storageKey.getName() : storageKey;\n}\n\nconst NG_DEV_MODE$3 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst FINAL_NGXS_STORAGE_PLUGIN_OPTIONS = new InjectionToken(NG_DEV_MODE$3 ? 'FINAL_NGXS_STORAGE_PLUGIN_OPTIONS' : '');\nfunction createFinalStoragePluginOptions(injector, options) {\n    const storageKeys = Array.isArray(options.key) ? options.key : [options.key];\n    const keysWithEngines = storageKeys.map((storageKey) => {\n        const key = exctractStringKey(storageKey);\n        const engine = isKeyWithExplicitEngine(storageKey)\n            ? injector.get(storageKey.engine)\n            : injector.get(STORAGE_ENGINE);\n        return { key, engine };\n    });\n    return Object.assign(Object.assign({}, options), { keysWithEngines });\n}\n\nconst NG_DEV_MODE$2 = typeof ngDevMode === 'undefined' || ngDevMode;\nclass NgxsStoragePlugin {\n    constructor(_options, _platformId) {\n        this._options = _options;\n        this._platformId = _platformId;\n        this._keysWithEngines = this._options.keysWithEngines;\n        // We default to `[DEFAULT_STATE_KEY]` if the user explicitly does not provide the `key` option.\n        this._usesDefaultStateKey = this._keysWithEngines.length === 1 && this._keysWithEngines[0].key === DEFAULT_STATE_KEY;\n    }\n    handle(state, event, next) {\n        var _a;\n        if (isPlatformServer(this._platformId)) {\n            return next(state, event);\n        }\n        const matches = actionMatcher(event);\n        const isInitAction = matches(InitState);\n        const isUpdateAction = matches(UpdateState);\n        const isInitOrUpdateAction = isInitAction || isUpdateAction;\n        let hasMigration = false;\n        if (isInitOrUpdateAction) {\n            const addedStates = isUpdateAction && event.addedStates;\n            for (const { key, engine } of this._keysWithEngines) {\n                // We're checking what states have been added by NGXS and if any of these states should be handled by\n                // the storage plugin. For instance, we only want to deserialize the `auth` state, NGXS has added\n                // the `user` state, the storage plugin will be rerun and will do redundant deserialization.\n                // `usesDefaultStateKey` is necessary to check since `event.addedStates` never contains `@@STATE`.\n                if (!this._usesDefaultStateKey && addedStates) {\n                    // We support providing keys that can be deeply nested via dot notation, for instance,\n                    // `keys: ['myState.myProperty']` is a valid key.\n                    // The state name should always go first. The below code checks if the `key` includes dot\n                    // notation and extracts the state name out of the key.\n                    // Given the `key` is `myState.myProperty`, the `addedStates` will only contain `myState`.\n                    const dotNotationIndex = key.indexOf(DOT);\n                    const stateName = dotNotationIndex > -1 ? key.slice(0, dotNotationIndex) : key;\n                    if (!addedStates.hasOwnProperty(stateName)) {\n                        continue;\n                    }\n                }\n                const storageKey = getStorageKey(key, this._options);\n                let storedValue = engine.getItem(storageKey);\n                if (storedValue !== 'undefined' && storedValue != null) {\n                    try {\n                        const newVal = this._options.deserialize(storedValue);\n                        storedValue = this._options.afterDeserialize(newVal, key);\n                    }\n                    catch (_b) {\n                        if (NG_DEV_MODE$2) {\n                            console.error(`Error ocurred while deserializing the ${storageKey} store value, falling back to empty object, the value obtained from the store: `, storedValue);\n                        }\n                        storedValue = {};\n                    }\n                    (_a = this._options.migrations) === null || _a === void 0 ? void 0 : _a.forEach(strategy => {\n                        const versionMatch = strategy.version === getValue(storedValue, strategy.versionKey || 'version');\n                        const keyMatch = (!strategy.key && this._usesDefaultStateKey) || strategy.key === key;\n                        if (versionMatch && keyMatch) {\n                            storedValue = strategy.migrate(storedValue);\n                            hasMigration = true;\n                        }\n                    });\n                    if (!this._usesDefaultStateKey) {\n                        state = setValue(state, key, storedValue);\n                    }\n                    else {\n                        // The `UpdateState` action is dispatched whenever the feature\n                        // state is added. The condition below is satisfied only when\n                        // the `UpdateState` action is dispatched. Let's consider two states:\n                        // `counter` and `@ngxs/router-plugin` state. When we call `NgxsModule.forRoot()`,\n                        // `CounterState` is provided at the root level, while `@ngxs/router-plugin`\n                        // is provided as a feature state. Beforehand, the storage plugin may have\n                        // stored the value of the counter state as `10`. If `CounterState` implements\n                        // the `ngxsOnInit` hook and calls `ctx.setState(999)`, the storage plugin\n                        // will rehydrate the entire state when the `RouterState` is registered.\n                        // Consequently, the `counter` state will revert back to `10` instead of `999`.\n                        if (storedValue && addedStates && Object.keys(addedStates).length > 0) {\n                            storedValue = Object.keys(addedStates).reduce((accumulator, addedState) => {\n                                // The `storedValue` can be equal to the entire state when the default\n                                // state key is used. However, if `addedStates` only contains the `router` value,\n                                // we only want to merge the state with the `router` value.\n                                // Let's assume that the `storedValue` is an object:\n                                // `{ counter: 10, router: {...} }`\n                                // This will pick only the `router` object from the `storedValue` and `counter`\n                                // state will not be rehydrated unnecessary.\n                                if (storedValue.hasOwnProperty(addedState)) {\n                                    accumulator[addedState] = storedValue[addedState];\n                                }\n                                return accumulator;\n                            }, {});\n                        }\n                        state = Object.assign(Object.assign({}, state), storedValue);\n                    }\n                }\n            }\n        }\n        return next(state, event).pipe(tap(nextState => {\n            if (isInitOrUpdateAction && !hasMigration) {\n                return;\n            }\n            for (const { key, engine } of this._keysWithEngines) {\n                let storedValue = nextState;\n                const storageKey = getStorageKey(key, this._options);\n                if (key !== DEFAULT_STATE_KEY) {\n                    storedValue = getValue(nextState, key);\n                }\n                try {\n                    const newStoredValue = this._options.beforeSerialize(storedValue, key);\n                    engine.setItem(storageKey, this._options.serialize(newStoredValue));\n                }\n                catch (error) {\n                    if (NG_DEV_MODE$2) {\n                        if (error &&\n                            (error.name === 'QuotaExceededError' ||\n                                error.name === 'NS_ERROR_DOM_QUOTA_REACHED')) {\n                            console.error(`The ${storageKey} store value exceeds the browser storage quota: `, storedValue);\n                        }\n                        else {\n                            console.error(`Error ocurred while serializing the ${storageKey} store value, value not updated, the value obtained from the store: `, storedValue);\n                        }\n                    }\n                }\n            }\n        }));\n    }\n}\n/** @nocollapse */ NgxsStoragePlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePlugin, deps: [{ token: FINAL_NGXS_STORAGE_PLUGIN_OPTIONS }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ NgxsStoragePlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [FINAL_NGXS_STORAGE_PLUGIN_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\nconst DOT = '.';\n\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || ngDevMode;\nconst USER_OPTIONS = new InjectionToken(NG_DEV_MODE$1 ? 'USER_OPTIONS' : '');\nclass NgxsStoragePluginModule {\n    static forRoot(options) {\n        return {\n            ngModule: NgxsStoragePluginModule,\n            providers: [\n                {\n                    provide: NGXS_PLUGINS,\n                    useClass: NgxsStoragePlugin,\n                    multi: true\n                },\n                {\n                    provide: USER_OPTIONS,\n                    useValue: options\n                },\n                {\n                    provide: NGXS_STORAGE_PLUGIN_OPTIONS,\n                    useFactory: storageOptionsFactory,\n                    deps: [USER_OPTIONS]\n                },\n                {\n                    provide: STORAGE_ENGINE,\n                    useFactory: engineFactory,\n                    deps: [NGXS_STORAGE_PLUGIN_OPTIONS, PLATFORM_ID]\n                },\n                {\n                    provide: FINAL_NGXS_STORAGE_PLUGIN_OPTIONS,\n                    useFactory: createFinalStoragePluginOptions,\n                    deps: [Injector, NGXS_STORAGE_PLUGIN_OPTIONS]\n                }\n            ]\n        };\n    }\n}\n/** @nocollapse */ NgxsStoragePluginModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePluginModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ NgxsStoragePluginModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePluginModule });\n/** @nocollapse */ NgxsStoragePluginModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePluginModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.17\", ngImport: i0, type: NgxsStoragePluginModule, decorators: [{\n            type: NgModule\n        }] });\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || ngDevMode;\nconst LOCAL_STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE ? 'LOCAL_STORAGE_ENGINE' : '', {\n    providedIn: 'root',\n    factory: () => (isPlatformBrowser(inject(PLATFORM_ID)) ? localStorage : null)\n});\nconst SESSION_STORAGE_ENGINE = new InjectionToken(NG_DEV_MODE ? 'SESSION_STORAGE_ENGINE' : '', {\n    providedIn: 'root',\n    factory: () => (isPlatformBrowser(inject(PLATFORM_ID)) ? sessionStorage : null)\n});\n\n/**\n * The public api for consumers of @ngxs/storage-plugin\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LOCAL_STORAGE_ENGINE, NGXS_STORAGE_PLUGIN_OPTIONS, NgxsStoragePlugin, NgxsStoragePluginModule, SESSION_STORAGE_ENGINE, STORAGE_ENGINE };\n"]}, "metadata": {}, "sourceType": "module"}