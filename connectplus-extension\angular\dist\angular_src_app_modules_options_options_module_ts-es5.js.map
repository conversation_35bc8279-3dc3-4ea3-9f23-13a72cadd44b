{"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,UAAMA,MAAM,GAAW,CACrB;AACEC,YAAI,EAAE,oCADR,CAEE;;AAFF,OADqB,CAAvB;;UAWaC;;;;;yBAAAA;AAAoB;;;;;cAApBA;;;;;kBAHF,CAACC;AAAA;AAAA,qBAAsBH,MAAtB,CAAD,GACCG;AAAA;AAAA;;;;;;aAECD,sBAAoB;AAAAE;AAAA;AAAA;AAAAC,oBAFrBF;AAAA;AAAA,aAEqB;AAAA;AAFT;;;;UCNXG;;;;;yBAAAA;AAAgB;;;;;cAAhBA;AAAgBC;AAAAC;AAAAC;AAAAC;AAAAC;AAAA;ACP7BC;AAAA;AAAA;;AAA8BA;AAAA;AAAA;;AAAOA;AAAA;AAAA;AACrCA;AAAA;AAAA;;AAA6BA;AAAA;AAAA;;AAAgCA;AAAA;AAAA;;;;;;;UCQhDC;;;;;yBAAAA;AAAa;;;;;cAAbA;;;;;kBAFF,CAACC;AAAA;AAAA,WAAD,EAAeZ,oBAAf;;;;;;aAEEW,gBAAa;AAAAE,yBAHTT,gBAGS;AAHOF,oBACrBU;AAAA;AAAA,aADqB,EACPZ,oBADO;AAGP;AAFoB;;;;", "names": ["routes", "path", "OptionsRoutingModule", "router", "imports", "exports", "OptionsComponent", "selectors", "decls", "vars", "consts", "template", "core", "OptionsModule", "common", "declarations"], "sources": ["webpack:///angular/src/app/modules/options/options-routing.module.ts", "webpack:///angular/src/app/modules/options/pages/options/options.component.ts", "webpack:///angular/src/app/modules/options/pages/options/options.component.html", "webpack:///angular/src/app/modules/options/options.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { OptionsComponent } from './pages/options/options.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'https://www.salezshark.com/pricing'\r\n    // component: OptionsComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class OptionsRoutingModule {}\r\n", "import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-options',\r\n  templateUrl: 'options.component.html',\r\n  styleUrls: ['options.component.scss']\r\n})\r\nexport class OptionsComponent {}\r\n", "<h1 style=\"text-align:center\">Options</h1>\r\n<p style=\"text-align:center\">You are now on the options page!</p>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { OptionsRoutingModule } from './options-routing.module';\r\nimport { OptionsComponent } from './pages/options/options.component';\r\n\r\n@NgModule({\r\n  declarations: [OptionsComponent],\r\n  imports: [CommonModule, OptionsRoutingModule]\r\n})\r\nexport class OptionsModule {}\r\n"]}