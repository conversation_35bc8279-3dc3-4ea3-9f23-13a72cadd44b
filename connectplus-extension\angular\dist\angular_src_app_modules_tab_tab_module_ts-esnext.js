"use strict";
(self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["angular_src_app_modules_tab_tab_module_ts"],{

/***/ 9651:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "TabModule": function() { return /* binding */ TabModule; }
});

// EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/common.js
var common = __webpack_require__(4364);
// EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js
var core = __webpack_require__(2316);
;// CONCATENATED MODULE: ./angular/src/app/modules/tab/pages/tab/tab.component.ts

class TabComponent {
}
TabComponent.ɵfac = function TabComponent_Factory(t) { return new (t || TabComponent)(); };
TabComponent.ɵcmp = /*@__PURE__*/ core/* ɵɵdefineComponent */.Xpm({ type: TabComponent, selectors: [["app-tab"]], decls: 4, vars: 0, consts: [[2, "text-align", "center"]], template: function TabComponent_Template(rf, ctx) { if (rf & 1) {
        core/* ɵɵelementStart */.TgZ(0, "h1", 0);
        core/* ɵɵtext */._uU(1, "Tab");
        core/* ɵɵelementEnd */.qZA();
        core/* ɵɵelementStart */.TgZ(2, "p", 0);
        core/* ɵɵtext */._uU(3, "You opened a new tab!");
        core/* ɵɵelementEnd */.qZA();
    } }, styles: [""] });

// EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules
var router = __webpack_require__(24);
;// CONCATENATED MODULE: ./angular/src/app/modules/tab/tab-routing.module.ts




const routes = [
    {
        path: '',
        component: TabComponent
    }
];
class TabRoutingModule {
}
TabRoutingModule.ɵfac = function TabRoutingModule_Factory(t) { return new (t || TabRoutingModule)(); };
TabRoutingModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: TabRoutingModule });
TabRoutingModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[router/* RouterModule.forChild */.Bz.forChild(routes)], router/* RouterModule */.Bz] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(TabRoutingModule, { imports: [router/* RouterModule */.Bz], exports: [router/* RouterModule */.Bz] }); })();

;// CONCATENATED MODULE: ./angular/src/app/modules/tab/tab.module.ts




class TabModule {
}
TabModule.ɵfac = function TabModule_Factory(t) { return new (t || TabModule)(); };
TabModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: TabModule });
TabModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[common/* CommonModule */.ez, TabRoutingModule]] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(TabModule, { declarations: [TabComponent], imports: [common/* CommonModule */.ez, TabRoutingModule] }); })();


/***/ })

}]);
//# sourceMappingURL=angular_src_app_modules_tab_tab_module_ts-esnext.js.map