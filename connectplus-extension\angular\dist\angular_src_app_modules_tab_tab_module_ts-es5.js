(function () {
  "use strict";

  function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }

  function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }

  function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }

  function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

  function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }

  (self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["angular_src_app_modules_tab_tab_module_ts"], {
    /***/
    9651:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      // ESM COMPAT FLAG
      __webpack_require__.r(__webpack_exports__); // EXPORTS


      __webpack_require__.d(__webpack_exports__, {
        "TabModule": function TabModule() {
          return (
            /* binding */
            _TabModule
          );
        }
      }); // EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/common.js


      var common = __webpack_require__(4364); // EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js


      var core = __webpack_require__(2316);

      ; // CONCATENATED MODULE: ./angular/src/app/modules/tab/pages/tab/tab.component.ts

      var TabComponent = /*#__PURE__*/_createClass(function TabComponent() {
        _classCallCheck(this, TabComponent);
      });

      TabComponent.ɵfac = function TabComponent_Factory(t) {
        return new (t || TabComponent)();
      };

      TabComponent.ɵcmp = /*@__PURE__*/core
      /* ɵɵdefineComponent */
      .Xpm({
        type: TabComponent,
        selectors: [["app-tab"]],
        decls: 4,
        vars: 0,
        consts: [[2, "text-align", "center"]],
        template: function TabComponent_Template(rf, ctx) {
          if (rf & 1) {
            core
            /* ɵɵelementStart */
            .TgZ(0, "h1", 0);

            core
            /* ɵɵtext */
            ._uU(1, "Tab");

            core
            /* ɵɵelementEnd */
            .qZA();
            core
            /* ɵɵelementStart */
            .TgZ(2, "p", 0);

            core
            /* ɵɵtext */
            ._uU(3, "You opened a new tab!");

            core
            /* ɵɵelementEnd */
            .qZA();
          }
        },
        styles: [""]
      }); // EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules

      var router = __webpack_require__(24);

      ; // CONCATENATED MODULE: ./angular/src/app/modules/tab/tab-routing.module.ts

      var routes = [{
        path: '',
        component: TabComponent
      }];

      var TabRoutingModule = /*#__PURE__*/_createClass(function TabRoutingModule() {
        _classCallCheck(this, TabRoutingModule);
      });

      TabRoutingModule.ɵfac = function TabRoutingModule_Factory(t) {
        return new (t || TabRoutingModule)();
      };

      TabRoutingModule.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: TabRoutingModule
      });
      TabRoutingModule.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        imports: [[router
        /* RouterModule.forChild */
        .Bz.forChild(routes)], router
        /* RouterModule */
        .Bz]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(TabRoutingModule, {
          imports: [router
          /* RouterModule */
          .Bz],
          exports: [router
          /* RouterModule */
          .Bz]
        });
      })();

      ; // CONCATENATED MODULE: ./angular/src/app/modules/tab/tab.module.ts

      var _TabModule = /*#__PURE__*/_createClass(function _TabModule() {
        _classCallCheck(this, _TabModule);
      });

      _TabModule.ɵfac = function TabModule_Factory(t) {
        return new (t || _TabModule)();
      };

      _TabModule.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: _TabModule
      });
      _TabModule.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        imports: [[common
        /* CommonModule */
        .ez, TabRoutingModule]]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(_TabModule, {
          declarations: [TabComponent],
          imports: [common
          /* CommonModule */
          .ez, TabRoutingModule]
        });
      })();
      /***/

    }
  }]);
})();
//# sourceMappingURL=angular_src_app_modules_tab_tab_module_ts-es5.js.map