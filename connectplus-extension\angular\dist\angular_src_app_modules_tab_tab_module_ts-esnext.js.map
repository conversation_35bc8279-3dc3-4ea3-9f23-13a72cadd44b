{"version": 3, "file": "angular_src_app_modules_tab_tab_module_ts-esnext.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAOO,MAAM,YAAY;;wEAAZ,YAAY;0EAAZ,YAAY;QCPzB,wCAA8B;QAAA,8BAAG;QAAA,4BAAK;QACtC,uCAA6B;QAAA,gDAAqB;QAAA,4BAAI;;;;;;ACAC;AACE;;;AAEzD,MAAM,MAAM,GAAW;IACrB;QACE,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,YAAY;KACxB;CACF,CAAC;AAMK,MAAM,gBAAgB;;gFAAhB,gBAAgB;6EAAhB,gBAAgB;iFAHlB,CAAC,6CAAqB,CAAC,MAAM,CAAC,CAAC,EAC9B,2BAAY;mGAEX,gBAAgB,sDAFjB,2BAAY;;;ACbuB;AAEU;AACD;;AAMjD,MAAM,SAAS;;kEAAT,SAAS;sEAAT,SAAS;0EAFX,CAAC,2BAAY,EAAE,gBAAgB,CAAC;mGAE9B,SAAS,mBAHL,YAAY,aACjB,2BAAY,EAAE,gBAAgB", "sources": ["./angular/src/app/modules/tab/pages/tab/tab.component.ts", "./angular/src/app/modules/tab/pages/tab/tab.component.html", "./angular/src/app/modules/tab/tab-routing.module.ts", "./angular/src/app/modules/tab/tab.module.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-tab',\r\n  templateUrl: 'tab.component.html',\r\n  styleUrls: ['tab.component.scss']\r\n})\r\nexport class TabComponent {}\r\n", "<h1 style=\"text-align:center\">Tab</h1>\r\n<p style=\"text-align:center\">You opened a new tab!</p>\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TabComponent } from './pages/tab/tab.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: TabComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TabRoutingModule {}\r\n", "import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { TabComponent } from './pages/tab/tab.component';\r\nimport { TabRoutingModule } from './tab-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [TabComponent],\r\n  imports: [CommonModule, TabRoutingModule]\r\n})\r\nexport class TabModule {}\r\n"], "names": [], "sourceRoot": "webpack:///"}