{"ast": null, "code": "import { __decorate, __metadata } from \"tslib\";\nimport { EventEmitter, ChangeDetectorRef } from \"@angular/core\";\nimport { SelectionService } from \"../../popup/store/service/popup.service\";\nimport { Select, Store } from \"@ngxs/store\";\nimport { AddExecutive, ClearExecutives } from \"../../popup/store/action/popup.action\";\nimport { Observable } from \"rxjs\";\nimport { map } from \"rxjs/operators\";\nimport { CreateExecutiveList, GetExecutiveListOptions, SearchListName } from \"../../popup/store/action/company.action\";\nimport { PopupState } from \"../../popup/store/state/popup.state\";\nimport { FormControl } from \"@angular/forms\";\nimport { CompanyState } from \"../../popup/store/state/company.state\";\nimport { SnackbarService } from \"src/app/common/snack-bar/snack-bar.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../popup/store/service/popup.service\";\nimport * as i2 from \"@ngxs/store\";\nimport * as i3 from \"src/app/common/snack-bar/snack-bar.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/forms\";\n\nfunction SaveToListPopupComponent_div_0_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const list_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", list_r4.id)(\"selected\", list_r4.id === ctx_r2.selectedItem);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", list_r4.name, \" \");\n  }\n}\n\nfunction SaveToListPopupComponent_div_0_mat_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 16);\n    i0.ɵɵtext(1, \"sync\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SaveToListPopupComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"mat-icon\", 4);\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_mat_icon_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.cancel();\n    });\n    i0.ɵɵtext(3, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵelementStart(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"hr\");\n    i0.ɵɵelementStart(8, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function SaveToListPopupComponent_div_0_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.save();\n    });\n    i0.ɵɵelementStart(9, \"div\", 6);\n    i0.ɵɵelementStart(10, \"label\", 7);\n    i0.ɵɵelementStart(11, \"b\");\n    i0.ɵɵtext(12, \"List\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 8);\n    i0.ɵɵlistener(\"change\", function SaveToListPopupComponent_div_0_Template_select_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.onItemSelected($event);\n    });\n    i0.ɵɵelementStart(14, \"option\", 9);\n    i0.ɵɵtext(15, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SaveToListPopupComponent_div_0_option_16_Template, 2, 3, \"option\", 10);\n    i0.ɵɵpipe(17, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 11);\n    i0.ɵɵelementStart(19, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return ctx_r9.createNewList();\n    });\n    i0.ɵɵtext(20, \" Create New List \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.save();\n    });\n    i0.ɵɵtext(22, \" Save \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, SaveToListPopupComponent_div_0_mat_icon_23_Template, 2, 0, \"mat-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.designType);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.isEditing ? \"Edit List\" : \"Select List\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"selected\", !ctx_r0.selectedItem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(17, 8, ctx_r0.executiveListOptions$));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r0.isSaveButtonEnabled || ctx_r0.isSaved || ctx_r0.isLoading);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isSaveButtonEnabled || ctx_r0.isSaved || ctx_r0.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n  }\n}\n\nfunction SaveToListPopupComponent_div_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.searchMessage, \" \");\n  }\n}\n\nfunction SaveToListPopupComponent_div_1_mat_icon_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 16);\n    i0.ɵɵtext(1, \"sync\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SaveToListPopupComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelementStart(2, \"mat-icon\", 4);\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_1_Template_mat_icon_click_2_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return ctx_r13.cancel();\n    });\n    i0.ɵɵtext(3, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵelementStart(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"hr\");\n    i0.ɵɵelementStart(8, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function SaveToListPopupComponent_div_1_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.save();\n    });\n    i0.ɵɵelementStart(9, \"div\", 20);\n    i0.ɵɵelementStart(10, \"label\", 21);\n    i0.ɵɵelementStart(11, \"b\");\n    i0.ɵɵtext(12, \"List Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 22);\n    i0.ɵɵtemplate(14, SaveToListPopupComponent_div_1_p_14_Template, 2, 1, \"p\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 24);\n    i0.ɵɵelementStart(16, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_1_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return ctx_r16.cancel();\n    });\n    i0.ɵɵtext(17, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 25);\n    i0.ɵɵtext(19, \" Save \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, SaveToListPopupComponent_div_1_mat_icon_20_Template, 2, 0, \"mat-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.designType);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.isEditing ? \"Edit List\" : \"Create List\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formControl\", ctx_r1.listSearchInput);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchMessage !== \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.listSearchInput.invalid || ctx_r1.listSearchInput.value === \"\" || ctx_r1.searchMessage !== \"\" || ctx_r1.isSaved);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listSearchInput.invalid || ctx_r1.listSearchInput.value === \"\" || ctx_r1.searchMessage !== \"\" || ctx_r1.isSaved);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\n\nexport class SaveToListPopupComponent {\n  constructor(selectionService, store, cd, cdr, snackbarService) {\n    this.selectionService = selectionService;\n    this.store = store;\n    this.cd = cd;\n    this.cdr = cdr;\n    this.snackbarService = snackbarService;\n    this.selectedItem = \"\";\n    this.selectedName = \"\";\n    this.isEditing = false;\n    this.itemSelected = new EventEmitter();\n    this.designType = \"design1\";\n    this.executives = [];\n    this.isCreatingNewList = false;\n    this.isSaveButtonEnabled = false;\n    this.isCreateListSaveButtonEnabled = false;\n    this.newListName = null;\n    this.editListName = \"\";\n    this.originalSelectedItem = \"\";\n    this.lists = [\"Test1\", \"Test2\", \"Test3\", \"Test4\"];\n    this.isSaved = false;\n    this.filteredOptions = [];\n    this.searchTerm = \"\";\n    this.isDropdownOpen = false;\n    this.selectedItemName = null;\n    this.listSearchInput = new FormControl(\"\");\n  } // ngOnDestroy(): void {\n  //   this.clearSelections();\n  // }\n\n\n  clearSelections() {\n    this.selectionService.clearSelection();\n  }\n\n  ngOnInit() {\n    // if(this.listSearchInput.value !== \"\"){\n    this.fromPage = this.selectionService?.fromPage?.getValue();\n    this.filtersPayload = this.selectionService?.FiltersPayload?.getValue();\n    this.listSearchInput.valueChanges.subscribe(val => {\n      if (val?.length > 0) {\n        this.newListName = val;\n        this.store.dispatch(new SearchListName(val));\n      } else {\n        this.searchMessage = \"\";\n      }\n    }); // }\n\n    this.listNameSearchResult$.subscribe(result => {\n      if (result === null || result === undefined) {} else {\n        if (result.message) {\n          if (result.message.includes(\"List Name already exist!\")) {\n            this.searchMessage = result.message;\n          } else {\n            this.searchMessage = \"\";\n          }\n        } else {\n          this.searchMessage = \"\";\n        }\n      }\n\n      this.cdr.detectChanges();\n    });\n\n    if (this.selectedItem) {\n      this.isSaveButtonEnabled = true;\n      this.isCreatingNewList = !this.lists.includes(this.selectedItem);\n      this.originalSelectedItem = this.selectedItem;\n    }\n\n    if (this.isCreatingNewList) {\n      if (this.lists.includes(this.editListName)) {\n        const index = this.lists.indexOf(this.selectedItem);\n\n        if (index !== -1) {\n          this.lists[index] = this.editListName;\n        }\n\n        this.selectedItem = this.editListName;\n      } else {\n        this.lists.push(this.newListName);\n        this.selectedItem = this.newListName;\n        this.isCreatingNewList = false;\n      }\n\n      this.itemSelected.emit({\n        itemId: this.selectedItem,\n        itemName: this.selectedName\n      });\n    } else {\n      if (this.selectedItem) {\n        this.itemSelected.emit({\n          itemId: this.selectedItem,\n          itemName: this.selectedName\n        });\n      }\n    }\n\n    this.selectionService.selectedExecutives$.subscribe(val => {\n      this.executivesData = val;\n    }); // const allNotPresent = selectedExecutives.every(\n    //   (executive) => executive.source === \"NOTPRESENT\"\n    // );\n\n    this.store.dispatch(new ClearExecutives());\n    this.executiveStatus$ = this.store.select(state => state.popup.executiveData);\n    this.executivesData.forEach(executive => {\n      this.store.dispatch(new AddExecutive(executive));\n    });\n    this.store.dispatch(new GetExecutiveListOptions());\n    this.executiveListOptions$ = this.store.select(state => state.company.executiveListOptions).pipe(map(executiveListOptions => executiveListOptions?.data?.listVOs || [])); // Subscribe to the executive list options\n\n    this.executiveListOptions$.subscribe(executiveListOptions => {\n      this.filteredOptions = executiveListOptions;\n    });\n    this.executiveStatus$ = this.store.select(state => state.popup.executiveData);\n    this.executiveStatus$.subscribe(executiveStatus => {});\n    this.executiveListOptions$.subscribe(options => {\n      this.filteredOptions = options;\n    });\n    this.cd.detectChanges();\n  }\n\n  updatelistname(val) {\n    this.listSearchInput.setValue(val.target.value);\n  }\n\n  ngAfterViewInit() {\n    this.executiveListOptions$.subscribe(options => {\n      this.filteredOptions = options;\n    });\n  }\n\n  goBack() {\n    if (this.isEditing) {\n      this.selectedItem = this.originalSelectedItem;\n      this.itemSelected.emit({\n        itemId: this.originalSelectedItem,\n        itemName: this.selectedName\n      });\n    } else {\n      this.itemSelected.emit(null);\n    }\n\n    this.isCreatingNewList = false;\n    this.isEditing = false;\n    this.isSaveButtonEnabled = false;\n    this.editListName = \"\";\n  }\n\n  save() {\n    this.isLoading = true; // Start loading message\n\n    this.isSaveButtonEnabled = false; // Disable save button during save\n\n    let request;\n    const myContacts = this.selectionService?.selectedExecutivesSubject?.getValue().map(executive => {\n      const [firstName = null, lastName = null] = (executive.name || \"\").split(\" \");\n      return {\n        sourceName: executive.Linksource || \"LINKEDIN\",\n        firstName: firstName || executive.firstName || null,\n        lastName: lastName || executive.lastName || null,\n        emailDomain: executive.emailDomain || null,\n        companySize: executive.companySize || null,\n        sourceId: executive.sourceId || null,\n        source: [executive.source],\n        staffCount: executive.source === \"TEMP_DB\" ? +executive?.staffCount : null,\n        checked: executive?.checked\n      };\n    }).filter(contact => contact.checked !== false);\n    const listName = this.newListName;\n    const listId = +this.selectedItem;\n    const isListExist = !!listId;\n    var frompage = this.fromPage;\n    var filtersPayload = this.filtersPayload;\n    request = {\n      myContacts,\n      listName,\n      listId,\n      isListExist,\n      campaignList: false,\n      isBulkView: false,\n      frompage,\n      filtersPayload\n    };\n\n    if (request) {\n      this.store.dispatch(new CreateExecutiveList(request)).subscribe({\n        next: response => {\n          this.isLoading = false; // Stop loading\n          //this.isSaved = true; // Mark as saved\n\n          if (response?.company?.IsContactsSaved?.message) if (response?.company?.IsContactsSaved?.statusCode === 200) {\n            this.isSaved = true;\n            this.isPopupVisible = false;\n          } else {\n            this.isSaved = false;\n          }\n          this.cdr.detectChanges(); //this.store.dispatch(new ClearExecutives());\n\n          this.selectionService?.selectedExecutivesSubject.next([]); //this.selectionService?.selectedExecutivesSubject.getValue().map(item => item.checked = false);\n\n          if (!this.isLoading && this.isSaved && (response?.company?.IsContactsSaved?.message !== \"Insufficient remaining limit for emails or phones.\" || response?.company?.IsContactsSaved?.message !== \"contacts are not created because email or mobile is not available\")) {\n            if (this.isCreatingNewList) {\n              if (this.lists.includes(this.editListName)) {\n                const index = this.lists.indexOf(this.selectedItem);\n\n                if (index !== -1) {\n                  this.lists[index] = this.editListName;\n                }\n\n                this.selectedItem = this.editListName;\n              } else {\n                this.lists.push(this.newListName);\n                this.selectedItem = this.newListName;\n                this.isCreatingNewList = false;\n              }\n\n              this.itemSelected.emit({\n                itemId: this.selectedItem,\n                itemName: this.selectedName\n              });\n            } else {\n              if (this.selectedItem) {\n                this.itemSelected.emit({\n                  itemId: this.selectedItem,\n                  itemName: this.selectedName\n                });\n              }\n            }\n          }\n        },\n        error: err => {\n          this.isLoading = false; // Stop loading on error\n        }\n      });\n    } else {\n      this.isLoading = false; // Stop loading if no request is made\n    }\n  }\n\n  cancel() {\n    if (this.isEditing) {\n      this.selectedItem = this.originalSelectedItem;\n      this.itemSelected.emit({\n        itemId: this.originalSelectedItem,\n        itemName: this.selectedName\n      }); // this.itemSelected.emit(itemId:this.originalSelectedItem);\n    } else {\n      this.itemSelected.emit(null);\n    }\n\n    this.isCreatingNewList = false;\n    this.isSaveButtonEnabled = false;\n    this.isEditing = false;\n  }\n\n  createNewList() {\n    this.isCreatingNewList = true;\n    this.editListName = \"\";\n    this.isEditing = false;\n  }\n\n  editSelectedItem() {\n    if (this.lists.includes(this.selectedItem)) {\n      this.isCreatingNewList = false;\n      this.editListName = this.selectedItem;\n      this.isEditing = true;\n    } else {\n      this.createNewList();\n    }\n  }\n\n  onItemSelected(event) {\n    this.selectedItem = event.target.value;\n    this.isSaveButtonEnabled = !!this.selectedItem;\n    this.executiveListOptions$.subscribe(options => {\n      const selectedObject = options.find(item => item.id === Number(this.selectedItem));\n\n      if (selectedObject) {\n        this.selectedName = selectedObject.name; // Assign the name of the matched item to selectedName\n      } else {\n        this.selectedName = \"\"; // Reset if no match found\n      }\n    }); // this.isCreatingNewList = !this.lists.includes(this.selectedItem);\n\n    if (!this.isCreatingNewList) {\n      this.isEditing = this.isEditing;\n    }\n  }\n\n  onNewListNameChange(event) {\n    this.newListName = event.target.value;\n    this.editListName = this.newListName;\n    this.isCreateListSaveButtonEnabled = !!this.newListName;\n    this.store.dispatch(new SearchListName(this.newListName));\n    this.listNameSearchResult$ = this.store.select(state => state.company.listNameSearchResult);\n    this.listNameSearchResult$.subscribe(result => {\n      if (result.message) {\n        if (result.message.includes(\"List Name already exist!\")) {\n          this.searchMessage = result.message;\n        } else {\n          this.searchMessage = \"\";\n        }\n      } else {\n        this.searchMessage = \"\";\n      }\n    });\n  }\n\n  onSearch() {\n    // Filter options based on search term\n    if (this.executiveListOptions$) {\n      this.executiveListOptions$.subscribe(options => {\n        this.filteredOptions = options.filter(option => option.name.toLowerCase().includes(this.searchTerm.toLowerCase()));\n      });\n    }\n  }\n\n  toggleDropdown() {\n    this.isDropdownOpen = !this.isDropdownOpen;\n  }\n\n  selectItem(list) {\n    this.selectedItem = list.id;\n    this.selectedItemName = list.name;\n    this.isDropdownOpen = false;\n  }\n\n}\n\nSaveToListPopupComponent.ɵfac = function SaveToListPopupComponent_Factory(t) {\n  return new (t || SaveToListPopupComponent)(i0.ɵɵdirectiveInject(i1.SelectionService), i0.ɵɵdirectiveInject(i2.Store), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.SnackbarService));\n};\n\nSaveToListPopupComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SaveToListPopupComponent,\n  selectors: [[\"app-save-to-list-popup\"]],\n  inputs: {\n    selectedItem: \"selectedItem\",\n    selectedName: \"selectedName\",\n    isEditing: \"isEditing\",\n    designType: \"designType\"\n  },\n  outputs: {\n    itemSelected: \"itemSelected\"\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[\"class\", \"card\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"card create-list\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"card\", 3, \"ngClass\"], [1, \"header\"], [1, \"clickable\", 3, \"click\"], [1, \"select-list-form\", 3, \"ngSubmit\"], [1, \"form-field\"], [\"for\", \"list\"], [\"id\", \"list\", 3, \"change\"], [\"value\", \"\", \"disabled\", \"\", 3, \"selected\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"button\", \"create-new-list-button\", 3, \"click\"], [\"type\", \"submit\", 1, \"button\", \"save-button\", 3, \"disabled\", \"click\"], [\"class\", \"loading-icon\", 4, \"ngIf\"], [3, \"value\", \"selected\"], [1, \"loading-icon\"], [1, \"card\", \"create-list\", 3, \"ngClass\"], [1, \"header-mat\"], [1, \"select-list\", 3, \"ngSubmit\"], [1, \"list\"], [\"for\", \"listName\"], [\"type\", \"text\", \"id\", \"listName\", \"placeholder\", \"Create New List...\", 3, \"formControl\"], [\"class\", \"SearchMessage\", 4, \"ngIf\"], [1, \"button-list-group\"], [\"type\", \"submit\", 1, \"button\", \"save-button\", 3, \"disabled\"], [1, \"SearchMessage\"]],\n  template: function SaveToListPopupComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, SaveToListPopupComponent_div_0_Template, 24, 10, \"div\", 0);\n      i0.ɵɵtemplate(1, SaveToListPopupComponent_div_1_Template, 21, 8, \"div\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.isCreatingNewList);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isCreatingNewList);\n    }\n  },\n  directives: [i4.NgIf, i4.NgClass, i5.MatIcon, i6.ɵNgNoValidate, i6.NgControlStatusGroup, i6.NgForm, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i4.NgForOf, i6.DefaultValueAccessor, i6.NgControlStatus, i6.FormControlDirective],\n  pipes: [i4.AsyncPipe],\n  styles: [\"body[_ngcontent-%COMP%]{font-family:Arial,sans-serif;display:flex;justify-content:center;align-items:center;height:100vh;margin:0;background-color:#f9f9f9}.card.design1[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 2px 4px #0000001a;border-radius:8px;width:435px;margin-left:-33px;padding:16px;box-sizing:border-box;position:relative;overflow:hidden}.card.design2[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 2px 4px #0000001a;border-radius:8px;width:430px;padding:16px;box-sizing:border-box;position:fixed;bottom:53px;left:50%;transform:translate(-50%)}.create-list.design1[_ngcontent-%COMP%]{border-radius:8px;width:435px;margin-left:-33px}.create-list.design2[_ngcontent-%COMP%]{border-radius:8px;width:430px}.header[_ngcontent-%COMP%]{position:relative;margin-bottom:0;display:flex;padding:0}.header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;display:block;font-weight:500;margin-right:185px}hr[_ngcontent-%COMP%]{border:none;border-top:1px solid #ccc}.select-list-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{margin-bottom:16px}.select-list-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:4px;font-size:14px;margin-right:280px}.select-list-form[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{width:100%;padding:8px;font-size:14px;border:1px solid #ccc;border-radius:4px;box-sizing:border-box;appearance:none;-webkit-appearance:none;-moz-appearance:none;background:url('data:image/svg+xml;utf8,<svg fill=\\\"%23000\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M7 10l5 5 5-5z\\\"/></svg>') no-repeat;background-position-x:100%;background-position-y:5px}.button-group[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.button[_ngcontent-%COMP%]{padding:6px 10px;font-size:14px;border:none;border-radius:4px;cursor:pointer;margin-right:8px}.create-new-list-button[_ngcontent-%COMP%]{background-color:#fff;color:#d83f87}.select-list[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:4px;font-size:14px;margin-left:15px}#listName[_ngcontent-%COMP%]{width:95%;padding:8px;font-size:14px;border:1px solid #ccc;border-radius:6px;margin-left:12px}.button-list-group[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:15px 10px 15px 0}.clickable[_ngcontent-%COMP%]{cursor:pointer;font-size:20px}.header-mat[_ngcontent-%COMP%]{display:flex;margin:15px 0 0 10px}.button.save-button[_ngcontent-%COMP%]{background-color:#d83f87;color:#fff;cursor:pointer}.button.save-button.disabled[_ngcontent-%COMP%]{background-color:#ccc;color:#666;cursor:not-allowed}.custom-dropdown[_ngcontent-%COMP%]{position:relative;width:200px;cursor:pointer}.selected-item[_ngcontent-%COMP%]{padding:10px;border:1px solid #ccc;background-color:#fff;border-radius:4px}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;width:100%;max-height:200px;overflow-y:auto;border:1px solid #ccc;background-color:#fff;z-index:10;top:100%;left:0;border-radius:4px}.search-input[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #ccc;box-sizing:border-box;border-bottom:1px solid #ddd}.dropdown-option[_ngcontent-%COMP%]{padding:10px;cursor:pointer}.dropdown-option[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}.no-results[_ngcontent-%COMP%]{padding:10px;color:#888}.SearchMessage[_ngcontent-%COMP%]{color:#a82d2d;margin-left:15px}.loading-icon[_ngcontent-%COMP%]{animation:rotate 1s linear infinite;vertical-align:middle;margin-top:1px;color:#d83f87}@keyframes rotate{to{transform:rotate(360deg)}}\"]\n});\n\n__decorate([Select(PopupState.getExecutives), __metadata(\"design:type\", Observable)], SaveToListPopupComponent.prototype, \"getExecutives$\", void 0);\n\n__decorate([Select(PopupState.getSelectedExecutives), __metadata(\"design:type\", Observable)], SaveToListPopupComponent.prototype, \"executives$\", void 0);\n\n__decorate([Select(CompanyState.listNameSearchResult), __metadata(\"design:type\", Object)], SaveToListPopupComponent.prototype, \"listNameSearchResult$\", void 0);", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/angular/src/app/modules/popup/pages/common/save-to-list-popup/save-to-list-popup.component.ts"], "names": ["__decorate", "__metadata", "EventEmitter", "ChangeDetectorRef", "SelectionService", "Select", "Store", "AddExecutive", "ClearExecutives", "Observable", "map", "CreateExecutiveList", "GetExecutiveListOptions", "SearchListName", "PopupState", "FormControl", "CompanyState", "SnackbarService", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "SaveToListPopupComponent_div_0_option_16_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "list_r4", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵproperty", "id", "selectedItem", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "SaveToListPopupComponent_div_0_mat_icon_23_Template", "SaveToListPopupComponent_div_0_Template", "_r6", "ɵɵgetCurrentView", "ɵɵlistener", "SaveToListPopupComponent_div_0_Template_mat_icon_click_2_listener", "ɵɵrestoreView", "ctx_r5", "cancel", "ɵɵelement", "SaveToListPopupComponent_div_0_Template_form_ngSubmit_8_listener", "ctx_r7", "save", "SaveToListPopupComponent_div_0_Template_select_change_13_listener", "$event", "ctx_r8", "onItemSelected", "ɵɵtemplate", "ɵɵpipe", "SaveToListPopupComponent_div_0_Template_button_click_19_listener", "ctx_r9", "createNewList", "SaveToListPopupComponent_div_0_Template_button_click_21_listener", "ctx_r10", "ctx_r0", "designType", "ɵɵtextInterpolate", "isEditing", "ɵɵpipeBind1", "executiveListOptions$", "ɵɵclassProp", "isSaveButtonEnabled", "isSaved", "isLoading", "SaveToListPopupComponent_div_1_p_14_Template", "ctx_r11", "searchMessage", "SaveToListPopupComponent_div_1_mat_icon_20_Template", "SaveToListPopupComponent_div_1_Template", "_r14", "SaveToListPopupComponent_div_1_Template_mat_icon_click_2_listener", "ctx_r13", "SaveToListPopupComponent_div_1_Template_form_ngSubmit_8_listener", "ctx_r15", "SaveToListPopupComponent_div_1_Template_button_click_16_listener", "ctx_r16", "ctx_r1", "listSearchInput", "invalid", "value", "SaveToListPopupComponent", "constructor", "selectionService", "store", "cd", "cdr", "snackbarService", "<PERSON><PERSON><PERSON>", "itemSelected", "executives", "isCreatingNewList", "isCreateListSaveButtonEnabled", "newListName", "editListName", "originalSelectedItem", "lists", "filteredOptions", "searchTerm", "isDropdownOpen", "selectedItemName", "clearSelections", "clearSelection", "ngOnInit", "fromPage", "getValue", "filtersPayload", "FiltersPayload", "valueChanges", "subscribe", "val", "length", "dispatch", "listNameSearchResult$", "result", "undefined", "message", "includes", "detectChanges", "index", "indexOf", "push", "emit", "itemId", "itemName", "selectedExecutives$", "executivesData", "executiveStatus$", "select", "state", "popup", "executiveData", "for<PERSON>ach", "executive", "company", "executiveListOptions", "pipe", "data", "listVOs", "executive<PERSON><PERSON><PERSON>", "options", "updatelistname", "setValue", "target", "ngAfterViewInit", "goBack", "request", "myContacts", "selectedExecutivesSubject", "firstName", "lastName", "split", "sourceName", "Linksource", "emailDomain", "companySize", "sourceId", "source", "staffCount", "checked", "filter", "contact", "listName", "listId", "isListExist", "frompage", "campaignList", "isBulkView", "next", "response", "IsContactsSaved", "statusCode", "isPopupVisible", "error", "err", "editSelectedItem", "event", "selectedObject", "find", "item", "Number", "onNewListNameChange", "listNameSearchResult", "onSearch", "option", "toLowerCase", "toggleDropdown", "selectItem", "list", "ɵfac", "SaveToListPopupComponent_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SaveToListPopupComponent_Template", "directives", "NgIf", "Ng<PERSON><PERSON>", "MatIcon", "ɵNgNoValidate", "NgControlStatusGroup", "NgForm", "NgSelectOption", "ɵNgSelectMultipleOption", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "pipes", "AsyncPipe", "styles", "getExecutives", "prototype", "getSelectedExecutives", "Object"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,UAArB,QAAuC,OAAvC;AACA,SAASC,YAAT,EAAuBC,iBAAvB,QAAiD,eAAjD;AACA,SAASC,gBAAT,QAAiC,yCAAjC;AACA,SAASC,MAAT,EAAiBC,KAAjB,QAA8B,aAA9B;AACA,SAASC,YAAT,EAAuBC,eAAvB,QAA+C,uCAA/C;AACA,SAASC,UAAT,QAA2B,MAA3B;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,mBAAT,EAA8BC,uBAA9B,EAAuDC,cAAvD,QAA8E,yCAA9E;AACA,SAASC,UAAT,QAA2B,qCAA3B;AACA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,YAAT,QAA6B,uCAA7B;AACA,SAASC,eAAT,QAAgC,4CAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,yCAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;;AACA,SAASC,iDAAT,CAA2DC,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9ER,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV;AACAX,IAAAA,EAAE,CAACY,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMK,OAAO,GAAGJ,GAAG,CAACK,SAApB;AACA,UAAMC,MAAM,GAAGf,EAAE,CAACgB,aAAH,CAAiB,CAAjB,CAAf;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuBJ,OAAO,CAACK,EAA/B,EAAmC,UAAnC,EAA+CL,OAAO,CAACK,EAAR,KAAeH,MAAM,CAACI,YAArE;AACAnB,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACqB,kBAAH,CAAsB,GAAtB,EAA2BR,OAAO,CAACS,IAAnC,EAAyC,GAAzC;AACH;AAAE;;AACH,SAASC,mDAAT,CAA6Df,EAA7D,EAAiEC,GAAjE,EAAsE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAChFR,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,EAAjC;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV,EAAa,MAAb;AACAX,IAAAA,EAAE,CAACY,YAAH;AACH;AAAE;;AACH,SAASY,uCAAT,CAAiDhB,EAAjD,EAAqDC,GAArD,EAA0D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACpE,UAAMiB,GAAG,GAAGzB,EAAE,CAAC0B,gBAAH,EAAZ;;AACA1B,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,CAAjC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,OAAd,EAAuB,SAASC,iEAAT,GAA6E;AAAE5B,MAAAA,EAAE,CAAC6B,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMK,MAAM,GAAG9B,EAAE,CAACgB,aAAH,EAAf;AAAmC,aAAOc,MAAM,CAACC,MAAP,EAAP;AAAyB,KAAzL;AACA/B,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV,EAAa,YAAb;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,MAArB;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,GAArB;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACgC,SAAH,CAAa,CAAb,EAAgB,IAAhB;AACAhC,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,UAAd,EAA0B,SAASM,gEAAT,GAA4E;AAAEjC,MAAAA,EAAE,CAAC6B,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMS,MAAM,GAAGlC,EAAE,CAACgB,aAAH,EAAf;AAAmC,aAAOkB,MAAM,CAACC,IAAP,EAAP;AAAuB,KAAzL;AACAnC,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,GAAtB;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,MAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,CAAhC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,QAAd,EAAwB,SAASS,iEAAT,CAA2EC,MAA3E,EAAmF;AAAErC,MAAAA,EAAE,CAAC6B,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMa,MAAM,GAAGtC,EAAE,CAACgB,aAAH,EAAf;AAAmC,aAAOsB,MAAM,CAACC,cAAP,CAAsBF,MAAtB,CAAP;AAAuC,KAA9M;AACArC,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,CAAhC;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,QAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACwC,UAAH,CAAc,EAAd,EAAkBjC,iDAAlB,EAAqE,CAArE,EAAwE,CAAxE,EAA2E,QAA3E,EAAqF,EAArF;AACAP,IAAAA,EAAE,CAACyC,MAAH,CAAU,EAAV,EAAc,OAAd;AACAzC,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,OAAd,EAAuB,SAASe,gEAAT,GAA4E;AAAE1C,MAAAA,EAAE,CAAC6B,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMkB,MAAM,GAAG3C,EAAE,CAACgB,aAAH,EAAf;AAAmC,aAAO2B,MAAM,CAACC,aAAP,EAAP;AAAgC,KAA/L;AACA5C,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,mBAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,OAAd,EAAuB,SAASkB,gEAAT,GAA4E;AAAE7C,MAAAA,EAAE,CAAC6B,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMqB,OAAO,GAAG9C,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAO8B,OAAO,CAACX,IAAR,EAAP;AAAwB,KAAxL;AACAnC,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,QAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACwC,UAAH,CAAc,EAAd,EAAkBjB,mDAAlB,EAAuE,CAAvE,EAA0E,CAA1E,EAA6E,UAA7E,EAAyF,EAAzF;AACAvB,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuC,MAAM,GAAG/C,EAAE,CAACgB,aAAH,EAAf;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyB8B,MAAM,CAACC,UAAhC;AACAhD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiD,iBAAH,CAAqBF,MAAM,CAACG,SAAP,GAAmB,WAAnB,GAAiC,aAAtD;AACAlD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0B,CAAC8B,MAAM,CAAC5B,YAAlC;AACAnB,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBjB,EAAE,CAACmD,WAAH,CAAe,EAAf,EAAmB,CAAnB,EAAsBJ,MAAM,CAACK,qBAA7B,CAAzB;AACApD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACqD,WAAH,CAAe,UAAf,EAA2B,CAACN,MAAM,CAACO,mBAAR,IAA+BP,MAAM,CAACQ,OAAtC,IAAiDR,MAAM,CAACS,SAAnF;AACAxD,IAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0B,CAAC8B,MAAM,CAACO,mBAAR,IAA+BP,MAAM,CAACQ,OAAtC,IAAiDR,MAAM,CAACS,SAAlF;AACAxD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB8B,MAAM,CAACS,SAA7B;AACH;AAAE;;AACH,SAASC,4CAAT,CAAsDjD,EAAtD,EAA0DC,GAA1D,EAA+D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACzER,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV;AACAX,IAAAA,EAAE,CAACY,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMkD,OAAO,GAAG1D,EAAE,CAACgB,aAAH,CAAiB,CAAjB,CAAhB;AACAhB,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACqB,kBAAH,CAAsB,GAAtB,EAA2BqC,OAAO,CAACC,aAAnC,EAAkD,GAAlD;AACH;AAAE;;AACH,SAASC,mDAAT,CAA6DpD,EAA7D,EAAiEC,GAAjE,EAAsE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAChFR,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,EAAjC;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV,EAAa,MAAb;AACAX,IAAAA,EAAE,CAACY,YAAH;AACH;AAAE;;AACH,SAASiD,uCAAT,CAAiDrD,EAAjD,EAAqDC,GAArD,EAA0D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACpE,UAAMsD,IAAI,GAAG9D,EAAE,CAAC0B,gBAAH,EAAb;;AACA1B,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,CAAjC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,OAAd,EAAuB,SAASoC,iEAAT,GAA6E;AAAE/D,MAAAA,EAAE,CAAC6B,aAAH,CAAiBiC,IAAjB;AAAwB,YAAME,OAAO,GAAGhE,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAOgD,OAAO,CAACjC,MAAR,EAAP;AAA0B,KAA5L;AACA/B,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV,EAAa,YAAb;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,MAArB;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,GAArB;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,CAAV;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACgC,SAAH,CAAa,CAAb,EAAgB,IAAhB;AACAhC,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,UAAd,EAA0B,SAASsC,gEAAT,GAA4E;AAAEjE,MAAAA,EAAE,CAAC6B,aAAH,CAAiBiC,IAAjB;AAAwB,YAAMI,OAAO,GAAGlE,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAOkD,OAAO,CAAC/B,IAAR,EAAP;AAAwB,KAA5L;AACAnC,IAAAA,EAAE,CAACU,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,GAAtB;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,WAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACgC,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAhC,IAAAA,EAAE,CAACwC,UAAH,CAAc,EAAd,EAAkBiB,4CAAlB,EAAgE,CAAhE,EAAmE,CAAnE,EAAsE,GAAtE,EAA2E,EAA3E;AACAzD,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAV,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAV,IAAAA,EAAE,CAAC2B,UAAH,CAAc,OAAd,EAAuB,SAASwC,gEAAT,GAA4E;AAAEnE,MAAAA,EAAE,CAAC6B,aAAH,CAAiBiC,IAAjB;AAAwB,YAAMM,OAAO,GAAGpE,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAOoD,OAAO,CAACrC,MAAR,EAAP;AAA0B,KAA3L;AACA/B,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,UAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACU,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAV,IAAAA,EAAE,CAACW,MAAH,CAAU,EAAV,EAAc,QAAd;AACAX,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACwC,UAAH,CAAc,EAAd,EAAkBoB,mDAAlB,EAAuE,CAAvE,EAA0E,CAA1E,EAA6E,UAA7E,EAAyF,EAAzF;AACA5D,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACAZ,IAAAA,EAAE,CAACY,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6D,MAAM,GAAGrE,EAAE,CAACgB,aAAH,EAAf;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBoD,MAAM,CAACrB,UAAhC;AACAhD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiD,iBAAH,CAAqBoB,MAAM,CAACnB,SAAP,GAAmB,WAAnB,GAAiC,aAAtD;AACAlD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,aAAd,EAA6BoD,MAAM,CAACC,eAApC;AACAtE,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBoD,MAAM,CAACV,aAAP,KAAyB,EAA/C;AACA3D,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACqD,WAAH,CAAe,UAAf,EAA2BgB,MAAM,CAACC,eAAP,CAAuBC,OAAvB,IAAkCF,MAAM,CAACC,eAAP,CAAuBE,KAAvB,KAAiC,EAAnE,IAAyEH,MAAM,CAACV,aAAP,KAAyB,EAAlG,IAAwGU,MAAM,CAACd,OAA1I;AACAvD,IAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BoD,MAAM,CAACC,eAAP,CAAuBC,OAAvB,IAAkCF,MAAM,CAACC,eAAP,CAAuBE,KAAvB,KAAiC,EAAnE,IAAyEH,MAAM,CAACV,aAAP,KAAyB,EAAlG,IAAwGU,MAAM,CAACd,OAAzI;AACAvD,IAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBoD,MAAM,CAACb,SAA7B;AACH;AAAE;;AACH,OAAO,MAAMiB,wBAAN,CAA+B;AAClCC,EAAAA,WAAW,CAACC,gBAAD,EAAmBC,KAAnB,EAA0BC,EAA1B,EAA8BC,GAA9B,EAAmCC,eAAnC,EAAoD;AAC3D,SAAKJ,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,EAAL,GAAUA,EAAV;AACA,SAAKC,GAAL,GAAWA,GAAX;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACA,SAAK5D,YAAL,GAAoB,EAApB;AACA,SAAK6D,YAAL,GAAoB,EAApB;AACA,SAAK9B,SAAL,GAAiB,KAAjB;AACA,SAAK+B,YAAL,GAAoB,IAAIjG,YAAJ,EAApB;AACA,SAAKgE,UAAL,GAAkB,SAAlB;AACA,SAAKkC,UAAL,GAAkB,EAAlB;AACA,SAAKC,iBAAL,GAAyB,KAAzB;AACA,SAAK7B,mBAAL,GAA2B,KAA3B;AACA,SAAK8B,6BAAL,GAAqC,KAArC;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACA,SAAKC,KAAL,GAAa,CAAC,OAAD,EAAU,OAAV,EAAmB,OAAnB,EAA4B,OAA5B,CAAb;AACA,SAAKjC,OAAL,GAAe,KAAf;AACA,SAAKkC,eAAL,GAAuB,EAAvB;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA,SAAKtB,eAAL,GAAuB,IAAIzE,WAAJ,CAAgB,EAAhB,CAAvB;AACH,GA1BiC,CA2BlC;AACA;AACA;;;AACAgG,EAAAA,eAAe,GAAG;AACd,SAAKlB,gBAAL,CAAsBmB,cAAtB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP;AACA,SAAKC,QAAL,GAAgB,KAAKrB,gBAAL,EAAuBqB,QAAvB,EAAiCC,QAAjC,EAAhB;AACA,SAAKC,cAAL,GAAsB,KAAKvB,gBAAL,EAAuBwB,cAAvB,EAAuCF,QAAvC,EAAtB;AACA,SAAK3B,eAAL,CAAqB8B,YAArB,CAAkCC,SAAlC,CAA6CC,GAAD,IAAS;AACjD,UAAIA,GAAG,EAAEC,MAAL,GAAc,CAAlB,EAAqB;AACjB,aAAKlB,WAAL,GAAmBiB,GAAnB;AACA,aAAK1B,KAAL,CAAW4B,QAAX,CAAoB,IAAI7G,cAAJ,CAAmB2G,GAAnB,CAApB;AACH,OAHD,MAIK;AACD,aAAK3C,aAAL,GAAqB,EAArB;AACH;AACJ,KARD,EAJO,CAaP;;AACA,SAAK8C,qBAAL,CAA2BJ,SAA3B,CAAsCK,MAAD,IAAY;AAC7C,UAAIA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAKC,SAAlC,EAA6C,CAC5C,CADD,MAEK;AACD,YAAID,MAAM,CAACE,OAAX,EAAoB;AAChB,cAAIF,MAAM,CAACE,OAAP,CAAeC,QAAf,CAAwB,0BAAxB,CAAJ,EAAyD;AACrD,iBAAKlD,aAAL,GAAqB+C,MAAM,CAACE,OAA5B;AACH,WAFD,MAGK;AACD,iBAAKjD,aAAL,GAAqB,EAArB;AACH;AACJ,SAPD,MAQK;AACD,eAAKA,aAAL,GAAqB,EAArB;AACH;AACJ;;AACD,WAAKmB,GAAL,CAASgC,aAAT;AACH,KAjBD;;AAkBA,QAAI,KAAK3F,YAAT,EAAuB;AACnB,WAAKmC,mBAAL,GAA2B,IAA3B;AACA,WAAK6B,iBAAL,GAAyB,CAAC,KAAKK,KAAL,CAAWqB,QAAX,CAAoB,KAAK1F,YAAzB,CAA1B;AACA,WAAKoE,oBAAL,GAA4B,KAAKpE,YAAjC;AACH;;AACD,QAAI,KAAKgE,iBAAT,EAA4B;AACxB,UAAI,KAAKK,KAAL,CAAWqB,QAAX,CAAoB,KAAKvB,YAAzB,CAAJ,EAA4C;AACxC,cAAMyB,KAAK,GAAG,KAAKvB,KAAL,CAAWwB,OAAX,CAAmB,KAAK7F,YAAxB,CAAd;;AACA,YAAI4F,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,eAAKvB,KAAL,CAAWuB,KAAX,IAAoB,KAAKzB,YAAzB;AACH;;AACD,aAAKnE,YAAL,GAAoB,KAAKmE,YAAzB;AACH,OAND,MAOK;AACD,aAAKE,KAAL,CAAWyB,IAAX,CAAgB,KAAK5B,WAArB;AACA,aAAKlE,YAAL,GAAoB,KAAKkE,WAAzB;AACA,aAAKF,iBAAL,GAAyB,KAAzB;AACH;;AACD,WAAKF,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,QAAAA,MAAM,EAAE,KAAKhG,YADM;AAEnBiG,QAAAA,QAAQ,EAAE,KAAKpC;AAFI,OAAvB;AAIH,KAjBD,MAkBK;AACD,UAAI,KAAK7D,YAAT,EAAuB;AACnB,aAAK8D,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,UAAAA,MAAM,EAAE,KAAKhG,YADM;AAEnBiG,UAAAA,QAAQ,EAAE,KAAKpC;AAFI,SAAvB;AAIH;AACJ;;AACD,SAAKL,gBAAL,CAAsB0C,mBAAtB,CAA0ChB,SAA1C,CAAqDC,GAAD,IAAS;AACzD,WAAKgB,cAAL,GAAsBhB,GAAtB;AACH,KAFD,EA/DO,CAkEP;AACA;AACA;;AACA,SAAK1B,KAAL,CAAW4B,QAAX,CAAoB,IAAIlH,eAAJ,EAApB;AACA,SAAKiI,gBAAL,GAAwB,KAAK3C,KAAL,CAAW4C,MAAX,CAAmBC,KAAD,IAAWA,KAAK,CAACC,KAAN,CAAYC,aAAzC,CAAxB;AACA,SAAKL,cAAL,CAAoBM,OAApB,CAA6BC,SAAD,IAAe;AACvC,WAAKjD,KAAL,CAAW4B,QAAX,CAAoB,IAAInH,YAAJ,CAAiBwI,SAAjB,CAApB;AACH,KAFD;AAGA,SAAKjD,KAAL,CAAW4B,QAAX,CAAoB,IAAI9G,uBAAJ,EAApB;AACA,SAAK0D,qBAAL,GAA6B,KAAKwB,KAAL,CACxB4C,MADwB,CAChBC,KAAD,IAAWA,KAAK,CAACK,OAAN,CAAcC,oBADR,EAExBC,IAFwB,CAEnBxI,GAAG,CAAEuI,oBAAD,IAA0BA,oBAAoB,EAAEE,IAAtB,EAA4BC,OAA5B,IAAuC,EAAlE,CAFgB,CAA7B,CA3EO,CA8EP;;AACA,SAAK9E,qBAAL,CAA2BiD,SAA3B,CAAsC0B,oBAAD,IAA0B;AAC3D,WAAKtC,eAAL,GAAuBsC,oBAAvB;AACH,KAFD;AAGA,SAAKR,gBAAL,GAAwB,KAAK3C,KAAL,CAAW4C,MAAX,CAAmBC,KAAD,IAAWA,KAAK,CAACC,KAAN,CAAYC,aAAzC,CAAxB;AACA,SAAKJ,gBAAL,CAAsBlB,SAAtB,CAAiC8B,eAAD,IAAqB,CAAG,CAAxD;AACA,SAAK/E,qBAAL,CAA2BiD,SAA3B,CAAsC+B,OAAD,IAAa;AAC9C,WAAK3C,eAAL,GAAuB2C,OAAvB;AACH,KAFD;AAGA,SAAKvD,EAAL,CAAQiC,aAAR;AACH;;AACDuB,EAAAA,cAAc,CAAC/B,GAAD,EAAM;AAChB,SAAKhC,eAAL,CAAqBgE,QAArB,CAA8BhC,GAAG,CAACiC,MAAJ,CAAW/D,KAAzC;AACH;;AACDgE,EAAAA,eAAe,GAAG;AACd,SAAKpF,qBAAL,CAA2BiD,SAA3B,CAAsC+B,OAAD,IAAa;AAC9C,WAAK3C,eAAL,GAAuB2C,OAAvB;AACH,KAFD;AAGH;;AACDK,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKvF,SAAT,EAAoB;AAChB,WAAK/B,YAAL,GAAoB,KAAKoE,oBAAzB;AACA,WAAKN,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,QAAAA,MAAM,EAAE,KAAK5B,oBADM;AAEnB6B,QAAAA,QAAQ,EAAE,KAAKpC;AAFI,OAAvB;AAIH,KAND,MAOK;AACD,WAAKC,YAAL,CAAkBiC,IAAlB,CAAuB,IAAvB;AACH;;AACD,SAAK/B,iBAAL,GAAyB,KAAzB;AACA,SAAKjC,SAAL,GAAiB,KAAjB;AACA,SAAKI,mBAAL,GAA2B,KAA3B;AACA,SAAKgC,YAAL,GAAoB,EAApB;AACH;;AACDnD,EAAAA,IAAI,GAAG;AACH,SAAKqB,SAAL,GAAiB,IAAjB,CADG,CACoB;;AACvB,SAAKF,mBAAL,GAA2B,KAA3B,CAFG,CAE+B;;AAClC,QAAIoF,OAAJ;AACA,UAAMC,UAAU,GAAG,KAAKhE,gBAAL,EAAuBiE,yBAAvB,EACb3C,QADa,GAEdzG,GAFc,CAETqI,SAAD,IAAe;AACpB,YAAM,CAACgB,SAAS,GAAG,IAAb,EAAmBC,QAAQ,GAAG,IAA9B,IAAsC,CAACjB,SAAS,CAACvG,IAAV,IAAkB,EAAnB,EAAuByH,KAAvB,CAA6B,GAA7B,CAA5C;AACA,aAAO;AACHC,QAAAA,UAAU,EAAEnB,SAAS,CAACoB,UAAV,IAAwB,UADjC;AAEHJ,QAAAA,SAAS,EAAEA,SAAS,IAAIhB,SAAS,CAACgB,SAAvB,IAAoC,IAF5C;AAGHC,QAAAA,QAAQ,EAAEA,QAAQ,IAAIjB,SAAS,CAACiB,QAAtB,IAAkC,IAHzC;AAIHI,QAAAA,WAAW,EAAErB,SAAS,CAACqB,WAAV,IAAyB,IAJnC;AAKHC,QAAAA,WAAW,EAAEtB,SAAS,CAACsB,WAAV,IAAyB,IALnC;AAMHC,QAAAA,QAAQ,EAAEvB,SAAS,CAACuB,QAAV,IAAsB,IAN7B;AAOHC,QAAAA,MAAM,EAAE,CAACxB,SAAS,CAACwB,MAAX,CAPL;AAQHC,QAAAA,UAAU,EAAEzB,SAAS,CAACwB,MAAV,KAAqB,SAArB,GAAiC,CAACxB,SAAS,EAAEyB,UAA7C,GAA0D,IARnE;AASHC,QAAAA,OAAO,EAAE1B,SAAS,EAAE0B;AATjB,OAAP;AAWH,KAfkB,EAgBdC,MAhBc,CAgBNC,OAAD,IAAaA,OAAO,CAACF,OAAR,KAAoB,KAhB1B,CAAnB;AAiBA,UAAMG,QAAQ,GAAG,KAAKrE,WAAtB;AACA,UAAMsE,MAAM,GAAG,CAAC,KAAKxI,YAArB;AACA,UAAMyI,WAAW,GAAG,CAAC,CAACD,MAAtB;AACA,QAAIE,QAAQ,GAAG,KAAK7D,QAApB;AACA,QAAIE,cAAc,GAAG,KAAKA,cAA1B;AACAwC,IAAAA,OAAO,GAAG;AACNC,MAAAA,UADM;AAENe,MAAAA,QAFM;AAGNC,MAAAA,MAHM;AAINC,MAAAA,WAJM;AAKNE,MAAAA,YAAY,EAAE,KALR;AAMNC,MAAAA,UAAU,EAAE,KANN;AAONF,MAAAA,QAPM;AAQN3D,MAAAA;AARM,KAAV;;AAUA,QAAIwC,OAAJ,EAAa;AACT,WAAK9D,KAAL,CAAW4B,QAAX,CAAoB,IAAI/G,mBAAJ,CAAwBiJ,OAAxB,CAApB,EAAsDrC,SAAtD,CAAgE;AAC5D2D,QAAAA,IAAI,EAAGC,QAAD,IAAc;AAChB,eAAKzG,SAAL,GAAiB,KAAjB,CADgB,CACQ;AACxB;;AACA,cAAIyG,QAAQ,EAAEnC,OAAV,EAAmBoC,eAAnB,EAAoCtD,OAAxC,EACI,IAAIqD,QAAQ,EAAEnC,OAAV,EAAmBoC,eAAnB,EAAoCC,UAApC,KAAmD,GAAvD,EAA4D;AACxD,iBAAK5G,OAAL,GAAe,IAAf;AACA,iBAAK6G,cAAL,GAAsB,KAAtB;AACH,WAHD,MAIK;AACD,iBAAK7G,OAAL,GAAe,KAAf;AACH;AACL,eAAKuB,GAAL,CAASgC,aAAT,GAXgB,CAYhB;;AACA,eAAKnC,gBAAL,EAAuBiE,yBAAvB,CAAiDoB,IAAjD,CAAsD,EAAtD,EAbgB,CAchB;;AACA,cAAI,CAAC,KAAKxG,SAAN,IACA,KAAKD,OADL,KAEC0G,QAAQ,EAAEnC,OAAV,EAAmBoC,eAAnB,EAAoCtD,OAApC,KACG,oDADH,IAEGqD,QAAQ,EAAEnC,OAAV,EAAmBoC,eAAnB,EAAoCtD,OAApC,KACI,mEALR,CAAJ,EAKkF;AAC9E,gBAAI,KAAKzB,iBAAT,EAA4B;AACxB,kBAAI,KAAKK,KAAL,CAAWqB,QAAX,CAAoB,KAAKvB,YAAzB,CAAJ,EAA4C;AACxC,sBAAMyB,KAAK,GAAG,KAAKvB,KAAL,CAAWwB,OAAX,CAAmB,KAAK7F,YAAxB,CAAd;;AACA,oBAAI4F,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,uBAAKvB,KAAL,CAAWuB,KAAX,IAAoB,KAAKzB,YAAzB;AACH;;AACD,qBAAKnE,YAAL,GAAoB,KAAKmE,YAAzB;AACH,eAND,MAOK;AACD,qBAAKE,KAAL,CAAWyB,IAAX,CAAgB,KAAK5B,WAArB;AACA,qBAAKlE,YAAL,GAAoB,KAAKkE,WAAzB;AACA,qBAAKF,iBAAL,GAAyB,KAAzB;AACH;;AACD,mBAAKF,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,gBAAAA,MAAM,EAAE,KAAKhG,YADM;AAEnBiG,gBAAAA,QAAQ,EAAE,KAAKpC;AAFI,eAAvB;AAIH,aAjBD,MAkBK;AACD,kBAAI,KAAK7D,YAAT,EAAuB;AACnB,qBAAK8D,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,kBAAAA,MAAM,EAAE,KAAKhG,YADM;AAEnBiG,kBAAAA,QAAQ,EAAE,KAAKpC;AAFI,iBAAvB;AAIH;AACJ;AACJ;AACJ,SAjD2D;AAkD5DqF,QAAAA,KAAK,EAAGC,GAAD,IAAS;AACZ,eAAK9G,SAAL,GAAiB,KAAjB,CADY,CACY;AAC3B;AApD2D,OAAhE;AAsDH,KAvDD,MAwDK;AACD,WAAKA,SAAL,GAAiB,KAAjB,CADC,CACuB;AAC3B;AACJ;;AACDzB,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKmB,SAAT,EAAoB;AAChB,WAAK/B,YAAL,GAAoB,KAAKoE,oBAAzB;AACA,WAAKN,YAAL,CAAkBiC,IAAlB,CAAuB;AACnBC,QAAAA,MAAM,EAAE,KAAK5B,oBADM;AAEnB6B,QAAAA,QAAQ,EAAE,KAAKpC;AAFI,OAAvB,EAFgB,CAMhB;AACH,KAPD,MAQK;AACD,WAAKC,YAAL,CAAkBiC,IAAlB,CAAuB,IAAvB;AACH;;AACD,SAAK/B,iBAAL,GAAyB,KAAzB;AACA,SAAK7B,mBAAL,GAA2B,KAA3B;AACA,SAAKJ,SAAL,GAAiB,KAAjB;AACH;;AACDN,EAAAA,aAAa,GAAG;AACZ,SAAKuC,iBAAL,GAAyB,IAAzB;AACA,SAAKG,YAAL,GAAoB,EAApB;AACA,SAAKpC,SAAL,GAAiB,KAAjB;AACH;;AACDqH,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAK/E,KAAL,CAAWqB,QAAX,CAAoB,KAAK1F,YAAzB,CAAJ,EAA4C;AACxC,WAAKgE,iBAAL,GAAyB,KAAzB;AACA,WAAKG,YAAL,GAAoB,KAAKnE,YAAzB;AACA,WAAK+B,SAAL,GAAiB,IAAjB;AACH,KAJD,MAKK;AACD,WAAKN,aAAL;AACH;AACJ;;AACDL,EAAAA,cAAc,CAACiI,KAAD,EAAQ;AAClB,SAAKrJ,YAAL,GAAoBqJ,KAAK,CAACjC,MAAN,CAAa/D,KAAjC;AACA,SAAKlB,mBAAL,GAA2B,CAAC,CAAC,KAAKnC,YAAlC;AACA,SAAKiC,qBAAL,CAA2BiD,SAA3B,CAAsC+B,OAAD,IAAa;AAC9C,YAAMqC,cAAc,GAAGrC,OAAO,CAACsC,IAAR,CAAcC,IAAD,IAAUA,IAAI,CAACzJ,EAAL,KAAY0J,MAAM,CAAC,KAAKzJ,YAAN,CAAzC,CAAvB;;AACA,UAAIsJ,cAAJ,EAAoB;AAChB,aAAKzF,YAAL,GAAoByF,cAAc,CAACnJ,IAAnC,CADgB,CACyB;AAC5C,OAFD,MAGK;AACD,aAAK0D,YAAL,GAAoB,EAApB,CADC,CACuB;AAC3B;AACJ,KARD,EAHkB,CAYlB;;AACA,QAAI,CAAC,KAAKG,iBAAV,EAA6B;AACzB,WAAKjC,SAAL,GAAiB,KAAKA,SAAtB;AACH;AACJ;;AACD2H,EAAAA,mBAAmB,CAACL,KAAD,EAAQ;AACvB,SAAKnF,WAAL,GAAmBmF,KAAK,CAACjC,MAAN,CAAa/D,KAAhC;AACA,SAAKc,YAAL,GAAoB,KAAKD,WAAzB;AACA,SAAKD,6BAAL,GAAqC,CAAC,CAAC,KAAKC,WAA5C;AACA,SAAKT,KAAL,CAAW4B,QAAX,CAAoB,IAAI7G,cAAJ,CAAmB,KAAK0F,WAAxB,CAApB;AACA,SAAKoB,qBAAL,GAA6B,KAAK7B,KAAL,CAAW4C,MAAX,CAAmBC,KAAD,IAAWA,KAAK,CAACK,OAAN,CAAcgD,oBAA3C,CAA7B;AACA,SAAKrE,qBAAL,CAA2BJ,SAA3B,CAAsCK,MAAD,IAAY;AAC7C,UAAIA,MAAM,CAACE,OAAX,EAAoB;AAChB,YAAIF,MAAM,CAACE,OAAP,CAAeC,QAAf,CAAwB,0BAAxB,CAAJ,EAAyD;AACrD,eAAKlD,aAAL,GAAqB+C,MAAM,CAACE,OAA5B;AACH,SAFD,MAGK;AACD,eAAKjD,aAAL,GAAqB,EAArB;AACH;AACJ,OAPD,MAQK;AACD,aAAKA,aAAL,GAAqB,EAArB;AACH;AACJ,KAZD;AAaH;;AACDoH,EAAAA,QAAQ,GAAG;AACP;AACA,QAAI,KAAK3H,qBAAT,EAAgC;AAC5B,WAAKA,qBAAL,CAA2BiD,SAA3B,CAAsC+B,OAAD,IAAa;AAC9C,aAAK3C,eAAL,GAAuB2C,OAAO,CAACoB,MAAR,CAAgBwB,MAAD,IAAYA,MAAM,CAAC1J,IAAP,CAAY2J,WAAZ,GAA0BpE,QAA1B,CAAmC,KAAKnB,UAAL,CAAgBuF,WAAhB,EAAnC,CAA3B,CAAvB;AACH,OAFD;AAGH;AACJ;;AACDC,EAAAA,cAAc,GAAG;AACb,SAAKvF,cAAL,GAAsB,CAAC,KAAKA,cAA5B;AACH;;AACDwF,EAAAA,UAAU,CAACC,IAAD,EAAO;AACb,SAAKjK,YAAL,GAAoBiK,IAAI,CAAClK,EAAzB;AACA,SAAK0E,gBAAL,GAAwBwF,IAAI,CAAC9J,IAA7B;AACA,SAAKqE,cAAL,GAAsB,KAAtB;AACH;;AArUiC;;AAuUtClB,wBAAwB,CAAC4G,IAAzB,GAAgC,SAASC,gCAAT,CAA0CC,CAA1C,EAA6C;AAAE,SAAO,KAAKA,CAAC,IAAI9G,wBAAV,EAAoCzE,EAAE,CAACwL,iBAAH,CAAqBvL,EAAE,CAACf,gBAAxB,CAApC,EAA+Ec,EAAE,CAACwL,iBAAH,CAAqBtL,EAAE,CAACd,KAAxB,CAA/E,EAA+GY,EAAE,CAACwL,iBAAH,CAAqBxL,EAAE,CAACf,iBAAxB,CAA/G,EAA2Je,EAAE,CAACwL,iBAAH,CAAqBxL,EAAE,CAACf,iBAAxB,CAA3J,EAAuMe,EAAE,CAACwL,iBAAH,CAAqBrL,EAAE,CAACJ,eAAxB,CAAvM,CAAP;AAA0P,CAAzU;;AACA0E,wBAAwB,CAACgH,IAAzB,GAAgC,aAAczL,EAAE,CAAC0L,iBAAH,CAAqB;AAAEC,EAAAA,IAAI,EAAElH,wBAAR;AAAkCmH,EAAAA,SAAS,EAAE,CAAC,CAAC,wBAAD,CAAD,CAA7C;AAA2EC,EAAAA,MAAM,EAAE;AAAE1K,IAAAA,YAAY,EAAE,cAAhB;AAAgC6D,IAAAA,YAAY,EAAE,cAA9C;AAA8D9B,IAAAA,SAAS,EAAE,WAAzE;AAAsFF,IAAAA,UAAU,EAAE;AAAlG,GAAnF;AAAqM8I,EAAAA,OAAO,EAAE;AAAE7G,IAAAA,YAAY,EAAE;AAAhB,GAA9M;AAAgP8G,EAAAA,KAAK,EAAE,CAAvP;AAA0PC,EAAAA,IAAI,EAAE,CAAhQ;AAAmQC,EAAAA,MAAM,EAAE,CAAC,CAAC,OAAD,EAAU,MAAV,EAAkB,CAAlB,EAAqB,SAArB,EAAgC,CAAhC,EAAmC,MAAnC,CAAD,EAA6C,CAAC,OAAD,EAAU,kBAAV,EAA8B,CAA9B,EAAiC,SAAjC,EAA4C,CAA5C,EAA+C,MAA/C,CAA7C,EAAqG,CAAC,CAAD,EAAI,MAAJ,EAAY,CAAZ,EAAe,SAAf,CAArG,EAAgI,CAAC,CAAD,EAAI,QAAJ,CAAhI,EAA+I,CAAC,CAAD,EAAI,WAAJ,EAAiB,CAAjB,EAAoB,OAApB,CAA/I,EAA6K,CAAC,CAAD,EAAI,kBAAJ,EAAwB,CAAxB,EAA2B,UAA3B,CAA7K,EAAqN,CAAC,CAAD,EAAI,YAAJ,CAArN,EAAwO,CAAC,KAAD,EAAQ,MAAR,CAAxO,EAAyP,CAAC,IAAD,EAAO,MAAP,EAAe,CAAf,EAAkB,QAAlB,CAAzP,EAAsR,CAAC,OAAD,EAAU,EAAV,EAAc,UAAd,EAA0B,EAA1B,EAA8B,CAA9B,EAAiC,UAAjC,CAAtR,EAAoU,CAAC,CAAD,EAAI,OAAJ,EAAa,UAAb,EAAyB,CAAzB,EAA4B,OAA5B,EAAqC,SAArC,CAApU,EAAqX,CAAC,CAAD,EAAI,cAAJ,CAArX,EAA0Y,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,QAAtB,EAAgC,wBAAhC,EAA0D,CAA1D,EAA6D,OAA7D,CAA1Y,EAAid,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,QAAtB,EAAgC,aAAhC,EAA+C,CAA/C,EAAkD,UAAlD,EAA8D,OAA9D,CAAjd,EAAyhB,CAAC,OAAD,EAAU,cAAV,EAA0B,CAA1B,EAA6B,MAA7B,CAAzhB,EAA+jB,CAAC,CAAD,EAAI,OAAJ,EAAa,UAAb,CAA/jB,EAAylB,CAAC,CAAD,EAAI,cAAJ,CAAzlB,EAA8mB,CAAC,CAAD,EAAI,MAAJ,EAAY,aAAZ,EAA2B,CAA3B,EAA8B,SAA9B,CAA9mB,EAAwpB,CAAC,CAAD,EAAI,YAAJ,CAAxpB,EAA2qB,CAAC,CAAD,EAAI,aAAJ,EAAmB,CAAnB,EAAsB,UAAtB,CAA3qB,EAA8sB,CAAC,CAAD,EAAI,MAAJ,CAA9sB,EAA2tB,CAAC,KAAD,EAAQ,UAAR,CAA3tB,EAAgvB,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,UAAvB,EAAmC,aAAnC,EAAkD,oBAAlD,EAAwE,CAAxE,EAA2E,aAA3E,CAAhvB,EAA20B,CAAC,OAAD,EAAU,eAAV,EAA2B,CAA3B,EAA8B,MAA9B,CAA30B,EAAk3B,CAAC,CAAD,EAAI,mBAAJ,CAAl3B,EAA44B,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,QAAtB,EAAgC,aAAhC,EAA+C,CAA/C,EAAkD,UAAlD,CAA54B,EAA28B,CAAC,CAAD,EAAI,eAAJ,CAA38B,CAA3Q;AAA6uCC,EAAAA,QAAQ,EAAE,SAASC,iCAAT,CAA2C3L,EAA3C,EAA+CC,GAA/C,EAAoD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACp3CR,MAAAA,EAAE,CAACwC,UAAH,CAAc,CAAd,EAAiBhB,uCAAjB,EAA0D,EAA1D,EAA8D,EAA9D,EAAkE,KAAlE,EAAyE,CAAzE;AACAxB,MAAAA,EAAE,CAACwC,UAAH,CAAc,CAAd,EAAiBqB,uCAAjB,EAA0D,EAA1D,EAA8D,CAA9D,EAAiE,KAAjE,EAAwE,CAAxE;AACH;;AAAC,QAAIrD,EAAE,GAAG,CAAT,EAAY;AACVR,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACR,GAAG,CAAC0E,iBAA3B;AACAnF,MAAAA,EAAE,CAACoB,SAAH,CAAa,CAAb;AACApB,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBR,GAAG,CAAC0E,iBAA1B;AACH;AAAE,GAP4D;AAO1DiH,EAAAA,UAAU,EAAE,CAAChM,EAAE,CAACiM,IAAJ,EAAUjM,EAAE,CAACkM,OAAb,EAAsBjM,EAAE,CAACkM,OAAzB,EAAkCjM,EAAE,CAACkM,aAArC,EAAoDlM,EAAE,CAACmM,oBAAvD,EAA6EnM,EAAE,CAACoM,MAAhF,EAAwFpM,EAAE,CAACqM,cAA3F,EAA2GrM,EAAE,CAACsM,uBAA9G,EAAuIxM,EAAE,CAACyM,OAA1I,EAAmJvM,EAAE,CAACwM,oBAAtJ,EAA4KxM,EAAE,CAACyM,eAA/K,EAAgMzM,EAAE,CAAC0M,oBAAnM,CAP8C;AAO4KC,EAAAA,KAAK,EAAE,CAAC7M,EAAE,CAAC8M,SAAJ,CAPnL;AAOmMC,EAAAA,MAAM,EAAE,CAAC,m9GAAD;AAP3M,CAArB,CAA9C;;AAQArO,UAAU,CAAC,CACPK,MAAM,CAACS,UAAU,CAACwN,aAAZ,CADC,EAEPrO,UAAU,CAAC,aAAD,EAAgBQ,UAAhB,CAFH,CAAD,EAGPkF,wBAAwB,CAAC4I,SAHlB,EAG6B,gBAH7B,EAG+C,KAAK,CAHpD,CAAV;;AAIAvO,UAAU,CAAC,CACPK,MAAM,CAACS,UAAU,CAAC0N,qBAAZ,CADC,EAEPvO,UAAU,CAAC,aAAD,EAAgBQ,UAAhB,CAFH,CAAD,EAGPkF,wBAAwB,CAAC4I,SAHlB,EAG6B,aAH7B,EAG4C,KAAK,CAHjD,CAAV;;AAIAvO,UAAU,CAAC,CACPK,MAAM,CAACW,YAAY,CAACgL,oBAAd,CADC,EAEP/L,UAAU,CAAC,aAAD,EAAgBwO,MAAhB,CAFH,CAAD,EAGP9I,wBAAwB,CAAC4I,SAHlB,EAG6B,uBAH7B,EAGsD,KAAK,CAH3D,CAAV", "sourcesContent": ["import { __decorate, __metadata } from \"tslib\";\r\nimport { EventEmitter, ChangeDetectorRef, } from \"@angular/core\";\r\nimport { SelectionService } from \"../../popup/store/service/popup.service\";\r\nimport { Select, Store } from \"@ngxs/store\";\r\nimport { AddExecutive, ClearExecutives, } from \"../../popup/store/action/popup.action\";\r\nimport { Observable } from \"rxjs\";\r\nimport { map } from \"rxjs/operators\";\r\nimport { CreateExecutiveList, GetExecutiveListOptions, SearchListName, } from \"../../popup/store/action/company.action\";\r\nimport { PopupState } from \"../../popup/store/state/popup.state\";\r\nimport { FormControl } from \"@angular/forms\";\r\nimport { CompanyState } from \"../../popup/store/state/company.state\";\r\nimport { SnackbarService } from \"src/app/common/snack-bar/snack-bar.service\";\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"../../popup/store/service/popup.service\";\r\nimport * as i2 from \"@ngxs/store\";\r\nimport * as i3 from \"src/app/common/snack-bar/snack-bar.service\";\r\nimport * as i4 from \"@angular/common\";\r\nimport * as i5 from \"@angular/material/icon\";\r\nimport * as i6 from \"@angular/forms\";\r\nfunction SaveToListPopupComponent_div_0_option_16_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 15);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const list_r4 = ctx.$implicit;\r\n    const ctx_r2 = i0.ɵɵnextContext(2);\r\n    i0.ɵɵproperty(\"value\", list_r4.id)(\"selected\", list_r4.id === ctx_r2.selectedItem);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", list_r4.name, \" \");\r\n} }\r\nfunction SaveToListPopupComponent_div_0_mat_icon_23_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"mat-icon\", 16);\r\n    i0.ɵɵtext(1, \"sync\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction SaveToListPopupComponent_div_0_Template(rf, ctx) { if (rf & 1) {\r\n    const _r6 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 2);\r\n    i0.ɵɵelementStart(1, \"div\", 3);\r\n    i0.ɵɵelementStart(2, \"mat-icon\", 4);\r\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_mat_icon_click_2_listener() { i0.ɵɵrestoreView(_r6); const ctx_r5 = i0.ɵɵnextContext(); return ctx_r5.cancel(); });\r\n    i0.ɵɵtext(3, \"arrow_back\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(4, \"span\");\r\n    i0.ɵɵelementStart(5, \"b\");\r\n    i0.ɵɵtext(6);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(7, \"hr\");\r\n    i0.ɵɵelementStart(8, \"form\", 5);\r\n    i0.ɵɵlistener(\"ngSubmit\", function SaveToListPopupComponent_div_0_Template_form_ngSubmit_8_listener() { i0.ɵɵrestoreView(_r6); const ctx_r7 = i0.ɵɵnextContext(); return ctx_r7.save(); });\r\n    i0.ɵɵelementStart(9, \"div\", 6);\r\n    i0.ɵɵelementStart(10, \"label\", 7);\r\n    i0.ɵɵelementStart(11, \"b\");\r\n    i0.ɵɵtext(12, \"List\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(13, \"select\", 8);\r\n    i0.ɵɵlistener(\"change\", function SaveToListPopupComponent_div_0_Template_select_change_13_listener($event) { i0.ɵɵrestoreView(_r6); const ctx_r8 = i0.ɵɵnextContext(); return ctx_r8.onItemSelected($event); });\r\n    i0.ɵɵelementStart(14, \"option\", 9);\r\n    i0.ɵɵtext(15, \"Select\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(16, SaveToListPopupComponent_div_0_option_16_Template, 2, 3, \"option\", 10);\r\n    i0.ɵɵpipe(17, \"async\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(18, \"div\", 11);\r\n    i0.ɵɵelementStart(19, \"button\", 12);\r\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_button_click_19_listener() { i0.ɵɵrestoreView(_r6); const ctx_r9 = i0.ɵɵnextContext(); return ctx_r9.createNewList(); });\r\n    i0.ɵɵtext(20, \" Create New List \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(21, \"button\", 13);\r\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_0_Template_button_click_21_listener() { i0.ɵɵrestoreView(_r6); const ctx_r10 = i0.ɵɵnextContext(); return ctx_r10.save(); });\r\n    i0.ɵɵtext(22, \" Save \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(23, SaveToListPopupComponent_div_0_mat_icon_23_Template, 2, 0, \"mat-icon\", 14);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r0 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.designType);\r\n    i0.ɵɵadvance(6);\r\n    i0.ɵɵtextInterpolate(ctx_r0.isEditing ? \"Edit List\" : \"Select List\");\r\n    i0.ɵɵadvance(8);\r\n    i0.ɵɵproperty(\"selected\", !ctx_r0.selectedItem);\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(17, 8, ctx_r0.executiveListOptions$));\r\n    i0.ɵɵadvance(5);\r\n    i0.ɵɵclassProp(\"disabled\", !ctx_r0.isSaveButtonEnabled || ctx_r0.isSaved || ctx_r0.isLoading);\r\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.isSaveButtonEnabled || ctx_r0.isSaved || ctx_r0.isLoading);\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\r\n} }\r\nfunction SaveToListPopupComponent_div_1_p_14_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"p\", 26);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r11 = i0.ɵɵnextContext(2);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.searchMessage, \" \");\r\n} }\r\nfunction SaveToListPopupComponent_div_1_mat_icon_20_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"mat-icon\", 16);\r\n    i0.ɵɵtext(1, \"sync\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction SaveToListPopupComponent_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    const _r14 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 17);\r\n    i0.ɵɵelementStart(1, \"div\", 18);\r\n    i0.ɵɵelementStart(2, \"mat-icon\", 4);\r\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_1_Template_mat_icon_click_2_listener() { i0.ɵɵrestoreView(_r14); const ctx_r13 = i0.ɵɵnextContext(); return ctx_r13.cancel(); });\r\n    i0.ɵɵtext(3, \"arrow_back\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(4, \"span\");\r\n    i0.ɵɵelementStart(5, \"b\");\r\n    i0.ɵɵtext(6);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(7, \"hr\");\r\n    i0.ɵɵelementStart(8, \"form\", 19);\r\n    i0.ɵɵlistener(\"ngSubmit\", function SaveToListPopupComponent_div_1_Template_form_ngSubmit_8_listener() { i0.ɵɵrestoreView(_r14); const ctx_r15 = i0.ɵɵnextContext(); return ctx_r15.save(); });\r\n    i0.ɵɵelementStart(9, \"div\", 20);\r\n    i0.ɵɵelementStart(10, \"label\", 21);\r\n    i0.ɵɵelementStart(11, \"b\");\r\n    i0.ɵɵtext(12, \"List Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(13, \"input\", 22);\r\n    i0.ɵɵtemplate(14, SaveToListPopupComponent_div_1_p_14_Template, 2, 1, \"p\", 23);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(15, \"div\", 24);\r\n    i0.ɵɵelementStart(16, \"button\", 12);\r\n    i0.ɵɵlistener(\"click\", function SaveToListPopupComponent_div_1_Template_button_click_16_listener() { i0.ɵɵrestoreView(_r14); const ctx_r16 = i0.ɵɵnextContext(); return ctx_r16.cancel(); });\r\n    i0.ɵɵtext(17, \" Cancel \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(18, \"button\", 25);\r\n    i0.ɵɵtext(19, \" Save \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(20, SaveToListPopupComponent_div_1_mat_icon_20_Template, 2, 0, \"mat-icon\", 14);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.designType);\r\n    i0.ɵɵadvance(6);\r\n    i0.ɵɵtextInterpolate(ctx_r1.isEditing ? \"Edit List\" : \"Create List\");\r\n    i0.ɵɵadvance(7);\r\n    i0.ɵɵproperty(\"formControl\", ctx_r1.listSearchInput);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchMessage !== \"\");\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.listSearchInput.invalid || ctx_r1.listSearchInput.value === \"\" || ctx_r1.searchMessage !== \"\" || ctx_r1.isSaved);\r\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listSearchInput.invalid || ctx_r1.listSearchInput.value === \"\" || ctx_r1.searchMessage !== \"\" || ctx_r1.isSaved);\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\r\n} }\r\nexport class SaveToListPopupComponent {\r\n    constructor(selectionService, store, cd, cdr, snackbarService) {\r\n        this.selectionService = selectionService;\r\n        this.store = store;\r\n        this.cd = cd;\r\n        this.cdr = cdr;\r\n        this.snackbarService = snackbarService;\r\n        this.selectedItem = \"\";\r\n        this.selectedName = \"\";\r\n        this.isEditing = false;\r\n        this.itemSelected = new EventEmitter();\r\n        this.designType = \"design1\";\r\n        this.executives = [];\r\n        this.isCreatingNewList = false;\r\n        this.isSaveButtonEnabled = false;\r\n        this.isCreateListSaveButtonEnabled = false;\r\n        this.newListName = null;\r\n        this.editListName = \"\";\r\n        this.originalSelectedItem = \"\";\r\n        this.lists = [\"Test1\", \"Test2\", \"Test3\", \"Test4\"];\r\n        this.isSaved = false;\r\n        this.filteredOptions = [];\r\n        this.searchTerm = \"\";\r\n        this.isDropdownOpen = false;\r\n        this.selectedItemName = null;\r\n        this.listSearchInput = new FormControl(\"\");\r\n    }\r\n    // ngOnDestroy(): void {\r\n    //   this.clearSelections();\r\n    // }\r\n    clearSelections() {\r\n        this.selectionService.clearSelection();\r\n    }\r\n    ngOnInit() {\r\n        // if(this.listSearchInput.value !== \"\"){\r\n        this.fromPage = this.selectionService?.fromPage?.getValue();\r\n        this.filtersPayload = this.selectionService?.FiltersPayload?.getValue();\r\n        this.listSearchInput.valueChanges.subscribe((val) => {\r\n            if (val?.length > 0) {\r\n                this.newListName = val;\r\n                this.store.dispatch(new SearchListName(val));\r\n            }\r\n            else {\r\n                this.searchMessage = \"\";\r\n            }\r\n        });\r\n        // }\r\n        this.listNameSearchResult$.subscribe((result) => {\r\n            if (result === null || result === undefined) {\r\n            }\r\n            else {\r\n                if (result.message) {\r\n                    if (result.message.includes(\"List Name already exist!\")) {\r\n                        this.searchMessage = result.message;\r\n                    }\r\n                    else {\r\n                        this.searchMessage = \"\";\r\n                    }\r\n                }\r\n                else {\r\n                    this.searchMessage = \"\";\r\n                }\r\n            }\r\n            this.cdr.detectChanges();\r\n        });\r\n        if (this.selectedItem) {\r\n            this.isSaveButtonEnabled = true;\r\n            this.isCreatingNewList = !this.lists.includes(this.selectedItem);\r\n            this.originalSelectedItem = this.selectedItem;\r\n        }\r\n        if (this.isCreatingNewList) {\r\n            if (this.lists.includes(this.editListName)) {\r\n                const index = this.lists.indexOf(this.selectedItem);\r\n                if (index !== -1) {\r\n                    this.lists[index] = this.editListName;\r\n                }\r\n                this.selectedItem = this.editListName;\r\n            }\r\n            else {\r\n                this.lists.push(this.newListName);\r\n                this.selectedItem = this.newListName;\r\n                this.isCreatingNewList = false;\r\n            }\r\n            this.itemSelected.emit({\r\n                itemId: this.selectedItem,\r\n                itemName: this.selectedName,\r\n            });\r\n        }\r\n        else {\r\n            if (this.selectedItem) {\r\n                this.itemSelected.emit({\r\n                    itemId: this.selectedItem,\r\n                    itemName: this.selectedName,\r\n                });\r\n            }\r\n        }\r\n        this.selectionService.selectedExecutives$.subscribe((val) => {\r\n            this.executivesData = val;\r\n        });\r\n        // const allNotPresent = selectedExecutives.every(\r\n        //   (executive) => executive.source === \"NOTPRESENT\"\r\n        // );\r\n        this.store.dispatch(new ClearExecutives());\r\n        this.executiveStatus$ = this.store.select((state) => state.popup.executiveData);\r\n        this.executivesData.forEach((executive) => {\r\n            this.store.dispatch(new AddExecutive(executive));\r\n        });\r\n        this.store.dispatch(new GetExecutiveListOptions());\r\n        this.executiveListOptions$ = this.store\r\n            .select((state) => state.company.executiveListOptions)\r\n            .pipe(map((executiveListOptions) => executiveListOptions?.data?.listVOs || []));\r\n        // Subscribe to the executive list options\r\n        this.executiveListOptions$.subscribe((executiveListOptions) => {\r\n            this.filteredOptions = executiveListOptions;\r\n        });\r\n        this.executiveStatus$ = this.store.select((state) => state.popup.executiveData);\r\n        this.executiveStatus$.subscribe((executiveStatus) => { });\r\n        this.executiveListOptions$.subscribe((options) => {\r\n            this.filteredOptions = options;\r\n        });\r\n        this.cd.detectChanges();\r\n    }\r\n    updatelistname(val) {\r\n        this.listSearchInput.setValue(val.target.value);\r\n    }\r\n    ngAfterViewInit() {\r\n        this.executiveListOptions$.subscribe((options) => {\r\n            this.filteredOptions = options;\r\n        });\r\n    }\r\n    goBack() {\r\n        if (this.isEditing) {\r\n            this.selectedItem = this.originalSelectedItem;\r\n            this.itemSelected.emit({\r\n                itemId: this.originalSelectedItem,\r\n                itemName: this.selectedName,\r\n            });\r\n        }\r\n        else {\r\n            this.itemSelected.emit(null);\r\n        }\r\n        this.isCreatingNewList = false;\r\n        this.isEditing = false;\r\n        this.isSaveButtonEnabled = false;\r\n        this.editListName = \"\";\r\n    }\r\n    save() {\r\n        this.isLoading = true; // Start loading message\r\n        this.isSaveButtonEnabled = false; // Disable save button during save\r\n        let request;\r\n        const myContacts = this.selectionService?.selectedExecutivesSubject\r\n            ?.getValue()\r\n            .map((executive) => {\r\n            const [firstName = null, lastName = null] = (executive.name || \"\").split(\" \");\r\n            return {\r\n                sourceName: executive.Linksource || \"LINKEDIN\",\r\n                firstName: firstName || executive.firstName || null,\r\n                lastName: lastName || executive.lastName || null,\r\n                emailDomain: executive.emailDomain || null,\r\n                companySize: executive.companySize || null,\r\n                sourceId: executive.sourceId || null,\r\n                source: [executive.source],\r\n                staffCount: executive.source === \"TEMP_DB\" ? +executive?.staffCount : null,\r\n                checked: executive?.checked,\r\n            };\r\n        })\r\n            .filter((contact) => contact.checked !== false);\r\n        const listName = this.newListName;\r\n        const listId = +this.selectedItem;\r\n        const isListExist = !!listId;\r\n        var frompage = this.fromPage;\r\n        var filtersPayload = this.filtersPayload;\r\n        request = {\r\n            myContacts,\r\n            listName,\r\n            listId,\r\n            isListExist,\r\n            campaignList: false,\r\n            isBulkView: false,\r\n            frompage,\r\n            filtersPayload,\r\n        };\r\n        if (request) {\r\n            this.store.dispatch(new CreateExecutiveList(request)).subscribe({\r\n                next: (response) => {\r\n                    this.isLoading = false; // Stop loading\r\n                    //this.isSaved = true; // Mark as saved\r\n                    if (response?.company?.IsContactsSaved?.message)\r\n                        if (response?.company?.IsContactsSaved?.statusCode === 200) {\r\n                            this.isSaved = true;\r\n                            this.isPopupVisible = false;\r\n                        }\r\n                        else {\r\n                            this.isSaved = false;\r\n                        }\r\n                    this.cdr.detectChanges();\r\n                    //this.store.dispatch(new ClearExecutives());\r\n                    this.selectionService?.selectedExecutivesSubject.next([]);\r\n                    //this.selectionService?.selectedExecutivesSubject.getValue().map(item => item.checked = false);\r\n                    if (!this.isLoading &&\r\n                        this.isSaved &&\r\n                        (response?.company?.IsContactsSaved?.message !==\r\n                            \"Insufficient remaining limit for emails or phones.\" ||\r\n                            response?.company?.IsContactsSaved?.message !==\r\n                                \"contacts are not created because email or mobile is not available\")) {\r\n                        if (this.isCreatingNewList) {\r\n                            if (this.lists.includes(this.editListName)) {\r\n                                const index = this.lists.indexOf(this.selectedItem);\r\n                                if (index !== -1) {\r\n                                    this.lists[index] = this.editListName;\r\n                                }\r\n                                this.selectedItem = this.editListName;\r\n                            }\r\n                            else {\r\n                                this.lists.push(this.newListName);\r\n                                this.selectedItem = this.newListName;\r\n                                this.isCreatingNewList = false;\r\n                            }\r\n                            this.itemSelected.emit({\r\n                                itemId: this.selectedItem,\r\n                                itemName: this.selectedName,\r\n                            });\r\n                        }\r\n                        else {\r\n                            if (this.selectedItem) {\r\n                                this.itemSelected.emit({\r\n                                    itemId: this.selectedItem,\r\n                                    itemName: this.selectedName,\r\n                                });\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                error: (err) => {\r\n                    this.isLoading = false; // Stop loading on error\r\n                },\r\n            });\r\n        }\r\n        else {\r\n            this.isLoading = false; // Stop loading if no request is made\r\n        }\r\n    }\r\n    cancel() {\r\n        if (this.isEditing) {\r\n            this.selectedItem = this.originalSelectedItem;\r\n            this.itemSelected.emit({\r\n                itemId: this.originalSelectedItem,\r\n                itemName: this.selectedName,\r\n            });\r\n            // this.itemSelected.emit(itemId:this.originalSelectedItem);\r\n        }\r\n        else {\r\n            this.itemSelected.emit(null);\r\n        }\r\n        this.isCreatingNewList = false;\r\n        this.isSaveButtonEnabled = false;\r\n        this.isEditing = false;\r\n    }\r\n    createNewList() {\r\n        this.isCreatingNewList = true;\r\n        this.editListName = \"\";\r\n        this.isEditing = false;\r\n    }\r\n    editSelectedItem() {\r\n        if (this.lists.includes(this.selectedItem)) {\r\n            this.isCreatingNewList = false;\r\n            this.editListName = this.selectedItem;\r\n            this.isEditing = true;\r\n        }\r\n        else {\r\n            this.createNewList();\r\n        }\r\n    }\r\n    onItemSelected(event) {\r\n        this.selectedItem = event.target.value;\r\n        this.isSaveButtonEnabled = !!this.selectedItem;\r\n        this.executiveListOptions$.subscribe((options) => {\r\n            const selectedObject = options.find((item) => item.id === Number(this.selectedItem));\r\n            if (selectedObject) {\r\n                this.selectedName = selectedObject.name; // Assign the name of the matched item to selectedName\r\n            }\r\n            else {\r\n                this.selectedName = \"\"; // Reset if no match found\r\n            }\r\n        });\r\n        // this.isCreatingNewList = !this.lists.includes(this.selectedItem);\r\n        if (!this.isCreatingNewList) {\r\n            this.isEditing = this.isEditing;\r\n        }\r\n    }\r\n    onNewListNameChange(event) {\r\n        this.newListName = event.target.value;\r\n        this.editListName = this.newListName;\r\n        this.isCreateListSaveButtonEnabled = !!this.newListName;\r\n        this.store.dispatch(new SearchListName(this.newListName));\r\n        this.listNameSearchResult$ = this.store.select((state) => state.company.listNameSearchResult);\r\n        this.listNameSearchResult$.subscribe((result) => {\r\n            if (result.message) {\r\n                if (result.message.includes(\"List Name already exist!\")) {\r\n                    this.searchMessage = result.message;\r\n                }\r\n                else {\r\n                    this.searchMessage = \"\";\r\n                }\r\n            }\r\n            else {\r\n                this.searchMessage = \"\";\r\n            }\r\n        });\r\n    }\r\n    onSearch() {\r\n        // Filter options based on search term\r\n        if (this.executiveListOptions$) {\r\n            this.executiveListOptions$.subscribe((options) => {\r\n                this.filteredOptions = options.filter((option) => option.name.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n            });\r\n        }\r\n    }\r\n    toggleDropdown() {\r\n        this.isDropdownOpen = !this.isDropdownOpen;\r\n    }\r\n    selectItem(list) {\r\n        this.selectedItem = list.id;\r\n        this.selectedItemName = list.name;\r\n        this.isDropdownOpen = false;\r\n    }\r\n}\r\nSaveToListPopupComponent.ɵfac = function SaveToListPopupComponent_Factory(t) { return new (t || SaveToListPopupComponent)(i0.ɵɵdirectiveInject(i1.SelectionService), i0.ɵɵdirectiveInject(i2.Store), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.SnackbarService)); };\r\nSaveToListPopupComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: SaveToListPopupComponent, selectors: [[\"app-save-to-list-popup\"]], inputs: { selectedItem: \"selectedItem\", selectedName: \"selectedName\", isEditing: \"isEditing\", designType: \"designType\" }, outputs: { itemSelected: \"itemSelected\" }, decls: 2, vars: 2, consts: [[\"class\", \"card\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"card create-list\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"card\", 3, \"ngClass\"], [1, \"header\"], [1, \"clickable\", 3, \"click\"], [1, \"select-list-form\", 3, \"ngSubmit\"], [1, \"form-field\"], [\"for\", \"list\"], [\"id\", \"list\", 3, \"change\"], [\"value\", \"\", \"disabled\", \"\", 3, \"selected\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"button\", \"create-new-list-button\", 3, \"click\"], [\"type\", \"submit\", 1, \"button\", \"save-button\", 3, \"disabled\", \"click\"], [\"class\", \"loading-icon\", 4, \"ngIf\"], [3, \"value\", \"selected\"], [1, \"loading-icon\"], [1, \"card\", \"create-list\", 3, \"ngClass\"], [1, \"header-mat\"], [1, \"select-list\", 3, \"ngSubmit\"], [1, \"list\"], [\"for\", \"listName\"], [\"type\", \"text\", \"id\", \"listName\", \"placeholder\", \"Create New List...\", 3, \"formControl\"], [\"class\", \"SearchMessage\", 4, \"ngIf\"], [1, \"button-list-group\"], [\"type\", \"submit\", 1, \"button\", \"save-button\", 3, \"disabled\"], [1, \"SearchMessage\"]], template: function SaveToListPopupComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵtemplate(0, SaveToListPopupComponent_div_0_Template, 24, 10, \"div\", 0);\r\n        i0.ɵɵtemplate(1, SaveToListPopupComponent_div_1_Template, 21, 8, \"div\", 1);\r\n    } if (rf & 2) {\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isCreatingNewList);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isCreatingNewList);\r\n    } }, directives: [i4.NgIf, i4.NgClass, i5.MatIcon, i6.ɵNgNoValidate, i6.NgControlStatusGroup, i6.NgForm, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i4.NgForOf, i6.DefaultValueAccessor, i6.NgControlStatus, i6.FormControlDirective], pipes: [i4.AsyncPipe], styles: [\"body[_ngcontent-%COMP%]{font-family:Arial,sans-serif;display:flex;justify-content:center;align-items:center;height:100vh;margin:0;background-color:#f9f9f9}.card.design1[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 2px 4px #0000001a;border-radius:8px;width:435px;margin-left:-33px;padding:16px;box-sizing:border-box;position:relative;overflow:hidden}.card.design2[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 2px 4px #0000001a;border-radius:8px;width:430px;padding:16px;box-sizing:border-box;position:fixed;bottom:53px;left:50%;transform:translate(-50%)}.create-list.design1[_ngcontent-%COMP%]{border-radius:8px;width:435px;margin-left:-33px}.create-list.design2[_ngcontent-%COMP%]{border-radius:8px;width:430px}.header[_ngcontent-%COMP%]{position:relative;margin-bottom:0;display:flex;padding:0}.header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;display:block;font-weight:500;margin-right:185px}hr[_ngcontent-%COMP%]{border:none;border-top:1px solid #ccc}.select-list-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{margin-bottom:16px}.select-list-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:4px;font-size:14px;margin-right:280px}.select-list-form[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{width:100%;padding:8px;font-size:14px;border:1px solid #ccc;border-radius:4px;box-sizing:border-box;appearance:none;-webkit-appearance:none;-moz-appearance:none;background:url('data:image/svg+xml;utf8,<svg fill=\\\"%23000\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M7 10l5 5 5-5z\\\"/></svg>') no-repeat;background-position-x:100%;background-position-y:5px}.button-group[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.button[_ngcontent-%COMP%]{padding:6px 10px;font-size:14px;border:none;border-radius:4px;cursor:pointer;margin-right:8px}.create-new-list-button[_ngcontent-%COMP%]{background-color:#fff;color:#d83f87}.select-list[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:4px;font-size:14px;margin-left:15px}#listName[_ngcontent-%COMP%]{width:95%;padding:8px;font-size:14px;border:1px solid #ccc;border-radius:6px;margin-left:12px}.button-list-group[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding:15px 10px 15px 0}.clickable[_ngcontent-%COMP%]{cursor:pointer;font-size:20px}.header-mat[_ngcontent-%COMP%]{display:flex;margin:15px 0 0 10px}.button.save-button[_ngcontent-%COMP%]{background-color:#d83f87;color:#fff;cursor:pointer}.button.save-button.disabled[_ngcontent-%COMP%]{background-color:#ccc;color:#666;cursor:not-allowed}.custom-dropdown[_ngcontent-%COMP%]{position:relative;width:200px;cursor:pointer}.selected-item[_ngcontent-%COMP%]{padding:10px;border:1px solid #ccc;background-color:#fff;border-radius:4px}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;width:100%;max-height:200px;overflow-y:auto;border:1px solid #ccc;background-color:#fff;z-index:10;top:100%;left:0;border-radius:4px}.search-input[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #ccc;box-sizing:border-box;border-bottom:1px solid #ddd}.dropdown-option[_ngcontent-%COMP%]{padding:10px;cursor:pointer}.dropdown-option[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}.no-results[_ngcontent-%COMP%]{padding:10px;color:#888}.SearchMessage[_ngcontent-%COMP%]{color:#a82d2d;margin-left:15px}.loading-icon[_ngcontent-%COMP%]{animation:rotate 1s linear infinite;vertical-align:middle;margin-top:1px;color:#d83f87}@keyframes rotate{to{transform:rotate(360deg)}}\"] });\r\n__decorate([\r\n    Select(PopupState.getExecutives),\r\n    __metadata(\"design:type\", Observable)\r\n], SaveToListPopupComponent.prototype, \"getExecutives$\", void 0);\r\n__decorate([\r\n    Select(PopupState.getSelectedExecutives),\r\n    __metadata(\"design:type\", Observable)\r\n], SaveToListPopupComponent.prototype, \"executives$\", void 0);\r\n__decorate([\r\n    Select(CompanyState.listNameSearchResult),\r\n    __metadata(\"design:type\", Object)\r\n], SaveToListPopupComponent.prototype, \"listNameSearchResult$\", void 0);\r\n"]}, "metadata": {}, "sourceType": "module"}