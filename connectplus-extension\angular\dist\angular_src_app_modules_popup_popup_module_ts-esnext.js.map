{"version": 3, "file": "angular_src_app_modules_popup_popup_module_ts-esnext.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AACuD;AACkB;;;AAEzE,MAAM,MAAM,GAAW;IACrB;QACE,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,qCAAc;KAC1B;CACF,CAAC;AAMK,MAAM,kBAAkB;;oFAAlB,kBAAkB;+EAAlB,kBAAkB;mFAHpB,CAAC,6CAAqB,CAAC,MAAM,CAAC,CAAC,EAC9B,2BAAY;mGAEX,kBAAkB,sDAFnB,2BAAY;;;;;;;ACToC;AAuBD;AAYP;;AAY7C,MAAM,WAAW;;sEAAX,WAAW;wEAAX,WAAW;6EAPX;QACT;YACE,OAAO,EAAE,6BAAM;YACf,QAAQ,EAAE,CAAC;SACZ;KACF,YANQ,CAAC,sCAAiB,EAAE,kBAAkB,CAAC;mGAQrC,WAAW,cARZ,sCAAiB,EAAE,kBAAkB", "sources": ["./angular/src/app/modules/popup/popup-routing.module.ts", "./angular/src/app/modules/popup/popup.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule, Routes } from \"@angular/router\";\r\nimport { PopupComponent } from \"./pages/popup/component/popup.component\";\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: \"\",\r\n    component: PopupComponent,\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class PopupRoutingModule {}\r\n", "import { CommonModule } from \"@angular/common\";\r\nimport { NgModule } from \"@angular/core\";\r\nimport { PopupComponent } from \"./pages/popup/component/popup.component\";\r\nimport { SCLoginComponent } from \"./pages/login/component/login.component\";\r\nimport { PopupRoutingModule } from \"./popup-routing.module\";\r\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\r\nimport { NgxsModule } from \"@ngxs/store\";\r\nimport { PopupState } from \"./pages/popup/store/state/popup.state\";\r\nimport { ExecutiveListComponent } from \"./pages/executive-list/executive-list.component\";\r\nimport { RangeContainerComponent } from \"./pages/range-container/range-container.component\";\r\nimport { LoaderComponent } from \"./pages/loader/loader.component\";\r\nimport { RangeModalComponent } from \"./pages/range-modal/range-modal.component\";\r\nimport { HttpClientModule } from \"@angular/common/http\";\r\nimport { ScLoginState } from \"./pages/login/store/state/login.state\";\r\nimport { MatFormFieldModule } from \"@angular/material/form-field\";\r\nimport { MatIconModule } from \"@angular/material/icon\";\r\nimport { MatInputModule } from \"@angular/material/input\";\r\nimport { LoaderModule } from \"src/app/common/custom-loader/loader.module\";\r\nimport { SnackBarModule } from \"src/app/common/snack-bar/snack-bar.module\";\r\nimport { MatMenuModule } from \"@angular/material/menu\";\r\nimport { PipeModule } from \"src/app/common/pipe/pipe.module\";\r\nimport { ButtonModule } from \"src/ss-ui/button/button.module\";\r\nimport { InputModule } from \"src/ss-ui/input/input.module\";\r\nimport { NgPipesModule } from \"ngx-pipes\";\r\nimport { CheckboxModule } from \"src/ss-ui/checkbox/checkbox.module\";\r\nimport { MatCheckboxModule } from \"@angular/material/checkbox\";\r\nimport { ClipboardModule } from \"@angular/cdk/clipboard\";\r\nimport { TAB_ID } from \"src/app/providers/tab-id.provider\";\r\nimport { PopupService } from \"./pages/popup/store/service/popup.service\";\r\nimport { BottomMenuComponent } from \"./pages/action/bottom-menu/bottom-menu.component\";\r\nimport { ProfileComponent } from \"./pages/profile/profile/profile.component\";\r\nimport { SaveToListPopupComponent } from \"./pages/common/save-to-list-popup/save-to-list-popup.component\";\r\nimport { SaveProfileComponent } from \"./pages/common/save-profile/save-profile.component\";\r\nimport { ActionMenuComponent } from \"./pages/action/action-menu/action-menu.component\";\r\nimport { SaveMenuComponent } from \"./pages/action/save-menu/save-menu/save-menu.component\";\r\nimport { CompanyPageComponent } from \"./pages/action/company-page/company-page.component\";\r\nimport { CompanyState } from \"./pages/popup/store/state/company.state\";\r\nimport { ExtrtactAnywebsiteComponent } from \"./pages/action/extrtact-anywebsite/extrtact-anywebsite.component\";\r\nimport { extractRoutingModule } from \"./extract-routing.module\";\r\nimport { CommonPopupModule } from \"./common.module\";\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [CommonPopupModule, PopupRoutingModule],\r\n  providers: [\r\n    {\r\n      provide: TAB_ID,\r\n      useValue: 0,\r\n    },\r\n  ],\r\n})\r\nexport class PopupModule {}\r\n"], "names": [], "sourceRoot": "webpack:///"}