"use strict";
(self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["angular_src_app_modules_popup_popup_module_ts"],{

/***/ 1224:
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "PopupModule": function() { return /* binding */ PopupModule; }
});

// EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules
var router = __webpack_require__(24);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/component/popup.component.ts + 5 modules
var popup_component = __webpack_require__(6893);
// EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js
var core = __webpack_require__(2316);
;// CONCATENATED MODULE: ./angular/src/app/modules/popup/popup-routing.module.ts




const routes = [
    {
        path: "",
        component: popup_component/* PopupComponent */.R,
    },
];
class PopupRoutingModule {
}
PopupRoutingModule.ɵfac = function PopupRoutingModule_Factory(t) { return new (t || PopupRoutingModule)(); };
PopupRoutingModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: PopupRoutingModule });
PopupRoutingModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ imports: [[router/* RouterModule.forChild */.Bz.forChild(routes)], router/* RouterModule */.Bz] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(PopupRoutingModule, { imports: [router/* RouterModule */.Bz], exports: [router/* RouterModule */.Bz] }); })();

// EXTERNAL MODULE: ./angular/src/app/providers/tab-id.provider.ts
var tab_id_provider = __webpack_require__(3154);
// EXTERNAL MODULE: ./angular/src/app/modules/popup/common.module.ts + 12 modules
var common_module = __webpack_require__(8451);
;// CONCATENATED MODULE: ./angular/src/app/modules/popup/popup.module.ts




class PopupModule {
}
PopupModule.ɵfac = function PopupModule_Factory(t) { return new (t || PopupModule)(); };
PopupModule.ɵmod = /*@__PURE__*/ core/* ɵɵdefineNgModule */.oAB({ type: PopupModule });
PopupModule.ɵinj = /*@__PURE__*/ core/* ɵɵdefineInjector */.cJS({ providers: [
        {
            provide: tab_id_provider/* TAB_ID */.$,
            useValue: 0,
        },
    ], imports: [[common_module/* CommonPopupModule */.A, PopupRoutingModule]] });
(function () { (typeof ngJitMode === "undefined" || ngJitMode) && core/* ɵɵsetNgModuleScope */.kYT(PopupModule, { imports: [common_module/* CommonPopupModule */.A, PopupRoutingModule] }); })();


/***/ })

}]);
//# sourceMappingURL=angular_src_app_modules_popup_popup_module_ts-esnext.js.map