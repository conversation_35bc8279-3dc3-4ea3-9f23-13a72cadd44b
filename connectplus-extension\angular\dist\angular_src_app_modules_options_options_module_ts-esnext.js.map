{"version": 3, "file": "angular_src_app_modules_options_options_module_ts-esnext.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AACuD;;;AAGvD,MAAM,MAAM,GAAW;IACrB;QACE,IAAI,EAAE,oCAAoC;QAC1C,8BAA8B;KAC/B;CACF,CAAC;AAMK,MAAM,oBAAoB;;wFAApB,oBAAoB;iFAApB,oBAAoB;qFAHtB,CAAC,6CAAqB,CAAC,MAAM,CAAC,CAAC,EAC9B,2BAAY;mGAEX,oBAAoB,sDAFrB,2BAAY;;;;ACNjB,MAAM,gBAAgB;;gFAAhB,gBAAgB;8EAAhB,gBAAgB;QCP7B,wCAA8B;QAAA,kCAAO;QAAA,4BAAK;QAC1C,uCAA6B;QAAA,2DAAgC;QAAA,4BAAI;;;;ACDlB;AAEiB;AACK;;AAM9D,MAAM,aAAa;;0EAAb,aAAa;0EAAb,aAAa;8EAFf,CAAC,2BAAY,EAAE,oBAAoB,CAAC;mGAElC,aAAa,mBAHT,gBAAgB,aACrB,2BAAY,EAAE,oBAAoB", "sources": ["./angular/src/app/modules/options/options-routing.module.ts", "./angular/src/app/modules/options/pages/options/options.component.ts", "./angular/src/app/modules/options/pages/options/options.component.html", "./angular/src/app/modules/options/options.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { OptionsComponent } from './pages/options/options.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'https://www.salezshark.com/pricing'\r\n    // component: OptionsComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class OptionsRoutingModule {}\r\n", "import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-options',\r\n  templateUrl: 'options.component.html',\r\n  styleUrls: ['options.component.scss']\r\n})\r\nexport class OptionsComponent {}\r\n", "<h1 style=\"text-align:center\">Options</h1>\r\n<p style=\"text-align:center\">You are now on the options page!</p>\r\n", "import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { OptionsRoutingModule } from './options-routing.module';\r\nimport { OptionsComponent } from './pages/options/options.component';\r\n\r\n@NgModule({\r\n  declarations: [OptionsComponent],\r\n  imports: [CommonModule, OptionsRoutingModule]\r\n})\r\nexport class OptionsModule {}\r\n"], "names": [], "sourceRoot": "webpack:///"}