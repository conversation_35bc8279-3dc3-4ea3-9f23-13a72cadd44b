(function () {
  function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }

  function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

  function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }

  function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }

  function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }

  function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

  function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }

  function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

  function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }

  function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }

  function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }

  function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }

  function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }

  function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }

  (self["webpackChunklinkedin"] = self["webpackChunklinkedin"] || []).push([["main"], {
    /***/
    403:
    /***/
    function _(module) {
      function webpackEmptyAsyncContext(req) {
        // Here Promise.resolve().then() is used instead of new Promise() to prevent
        // uncaught exception popping up in devtools
        return Promise.resolve().then(function () {
          var e = new Error("Cannot find module '" + req + "'");
          e.code = 'MODULE_NOT_FOUND';
          throw e;
        });
      }

      webpackEmptyAsyncContext.keys = function () {
        return [];
      };

      webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
      webpackEmptyAsyncContext.id = 403;
      module.exports = webpackEmptyAsyncContext;
      /***/
    },

    /***/
    2101:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "s": function s() {
          return (
            /* binding */
            ApiService
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_common_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3882);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);

      var ApiService = /*#__PURE__*/function () {
        function ApiService(httpClient) {
          _classCallCheck(this, ApiService);

          this.httpClient = httpClient;
        }

        return _createClass(ApiService, [{
          key: "put",
          value: function put(url, payload) {
            return this.httpClient.put(url, payload);
          }
        }, {
          key: "delete",
          value: function _delete(url, payload) {
            var options = {
              body: payload
            };
            return this.httpClient.request('delete', url, options);
          }
        }, {
          key: "post",
          value: function post(url, payload) {
            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM().set('Content-Type', 'application/json');
            var options = {
              headers: httpHeaders
            };
            return this.httpClient.post(url, payload, options);
          }
        }, {
          key: "patch",
          value: function patch(url, payload) {
            return this.httpClient.patch(url, payload);
          }
        }, {
          key: "get",
          value: function get(url, payload) {
            var options = {
              params: payload
            };
            return this.httpClient.get(url, options);
          }
        }, {
          key: "getUsingCsrfToken",
          value: function getUsingCsrfToken(url, payload, csrfToken) {
            var options = {
              params: payload,
              headers: {
                'csrf-token': csrfToken
              }
            };
            return this.httpClient.get(url, options);
          }
        }, {
          key: "sendGetFileRequest",
          value: function sendGetFileRequest(url) {
            var headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM();
            headers = headers.append('Accept', 'text/csv; charset=utf-8');
            return this.httpClient.get(url, {
              headers: headers,
              observe: 'response',
              responseType: 'text'
            });
          }
        }, {
          key: "putWithHeader",
          value: function putWithHeader(url, payload, dsmID) {
            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM().set('dsmID', dsmID);
            var options = {
              headers: httpHeaders
            };
            return this.httpClient.put(url, payload, options);
          }
        }, {
          key: "postWithHeader",
          value: function postWithHeader(url, payload, dsmID) {
            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM().set('dsmID', dsmID);
            var options = {
              headers: httpHeaders
            };
            return this.httpClient.post(url, payload, options);
          }
        }, {
          key: "getWithHeader",
          value: function getWithHeader(url, dsmID) {
            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM({
              'dsmID': dsmID
            });
            var options = {
              headers: httpHeaders
            };
            return this.httpClient.get(url, options);
          }
        }, {
          key: "getWithHeaderPagination",
          value: function getWithHeaderPagination(url, refererUrl, pageNo) {
            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__
            /* .HttpHeaders */
            .WM({
              'accept': 'application/vnd.linkedin.normalized+json+2.1' // "referer": refererUrl+"&page="+pageNo

            });
            var options = {
              headers: httpHeaders
            };
            return this.httpClient.get(url, options);
          }
        }, {
          key: "sendHttpRequest",
          value: function sendHttpRequest(type, url, headers) {
            var options = {
              headers: headers
            };
            return this.httpClient.request(type, url, options);
          }
        }]);
      }();

      ApiService.ɵfac = function ApiService_Factory(t) {
        return new (t || ApiService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__
        /* ["ɵɵinject"] */
        .LFG(_angular_common_http__WEBPACK_IMPORTED_MODULE_0__
        /* .HttpClient */
        .eN));
      };

      ApiService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__
      /* ["ɵɵdefineInjectable"] */
      .Yz7({
        token: ApiService,
        factory: ApiService.ɵfac,
        providedIn: 'root'
      });
      /***/
    },

    /***/
    8598:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "E": function E() {
          return (
            /* binding */
            SnackBarComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1900);
      /* harmony import */


      var _constant_value__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9818);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4364);
      /* harmony import */


      var _angular_material_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2529);

      function SnackBarComponent_div_1_Template(rf, ctx) {
        if (rf & 1) {
          var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵgetCurrentView"] */
          .EpF();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(0, "div", 5);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(1, "mat-icon", 6);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵlistener"] */
          .NdJ("click", function SnackBarComponent_div_1_Template_mat_icon_click_1_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵrestoreView"] */
            .CHM(_r4);

            var ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵnextContext"] */
            .oxw();

            return ctx_r3.dismiss();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵtext"] */
          ._uU(2, "clear");

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();
        }
      }

      function SnackBarComponent_div_2_Template(rf, ctx) {
        if (rf & 1) {
          var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵgetCurrentView"] */
          .EpF();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(0, "div", 7);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(1, "mat-icon", 6);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵlistener"] */
          .NdJ("click", function SnackBarComponent_div_2_Template_mat_icon_click_1_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵrestoreView"] */
            .CHM(_r6);

            var ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵnextContext"] */
            .oxw();

            return ctx_r5.dismiss();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵtext"] */
          ._uU(2, "done");

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();
        }
      }

      function SnackBarComponent_div_3_Template(rf, ctx) {
        if (rf & 1) {
          var _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵgetCurrentView"] */
          .EpF();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(0, "div", 8);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementStart"] */
          .TgZ(1, "mat-icon", 6);

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵlistener"] */
          .NdJ("click", function SnackBarComponent_div_3_Template_mat_icon_click_1_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵrestoreView"] */
            .CHM(_r8);

            var ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵnextContext"] */
            .oxw();

            return ctx_r7.dismiss();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵtext"] */
          ._uU(2, "warning");

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();

          _angular_core__WEBPACK_IMPORTED_MODULE_1__
          /* ["ɵɵelementEnd"] */
          .qZA();
        }
      }

      var SnackBarComponent = /*#__PURE__*/function () {
        function SnackBarComponent(data, snackbarRef) {
          _classCallCheck(this, SnackBarComponent);

          this.data = data;
          this.snackbarRef = snackbarRef;
          this.type = _constant_value__WEBPACK_IMPORTED_MODULE_0__
          /* .SNACK_BAR_TYPE */
          .cx;
        }

        return _createClass(SnackBarComponent, [{
          key: "ngOnInit",
          value: function ngOnInit() {}
        }, {
          key: "dismiss",
          value: function dismiss() {
            this.snackbarRef.dismiss();
          }
        }]);
      }();

      SnackBarComponent.ɵfac = function SnackBarComponent_Factory(t) {
        return new (t || SnackBarComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__
        /* ["ɵɵdirectiveInject"] */
        .Y36(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__
        /* .MAT_SNACK_BAR_DATA */
        .qD), _angular_core__WEBPACK_IMPORTED_MODULE_1__
        /* ["ɵɵdirectiveInject"] */
        .Y36(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__
        /* .MatSnackBarRef */
        .OX));
      };

      SnackBarComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__
      /* ["ɵɵdefineComponent"] */
      .Xpm({
        type: SnackBarComponent,
        selectors: [["app-snack-bar"]],
        decls: 6,
        vars: 5,
        consts: [[3, "ngSwitch"], ["class", "tick-background error", 4, "ngSwitchCase"], ["class", "tick-background success", 4, "ngSwitchCase"], ["class", "tick-background warn", 4, "ngSwitchCase"], [1, "message"], [1, "tick-background", "error"], [3, "click"], [1, "tick-background", "success"], [1, "tick-background", "warn"]],
        template: function SnackBarComponent_Template(rf, ctx) {
          if (rf & 1) {
            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵelementContainerStart"] */
            .ynx(0, 0);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵtemplate"] */
            .YNc(1, SnackBarComponent_div_1_Template, 3, 0, "div", 1);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵtemplate"] */
            .YNc(2, SnackBarComponent_div_2_Template, 3, 0, "div", 2);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵtemplate"] */
            .YNc(3, SnackBarComponent_div_3_Template, 3, 0, "div", 3);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵelementContainerEnd"] */
            .BQk();

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵelementStart"] */
            .TgZ(4, "p", 4);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵtext"] */
            ._uU(5);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵelementEnd"] */
            .qZA();
          }

          if (rf & 2) {
            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵproperty"] */
            .Q6J("ngSwitch", ctx.data.type);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵadvance"] */
            .xp6(1);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵproperty"] */
            .Q6J("ngSwitchCase", ctx.type.ERROR);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵadvance"] */
            .xp6(1);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵproperty"] */
            .Q6J("ngSwitchCase", ctx.type.SUCCESS);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵadvance"] */
            .xp6(1);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵproperty"] */
            .Q6J("ngSwitchCase", ctx.type.WARN);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵadvance"] */
            .xp6(2);

            _angular_core__WEBPACK_IMPORTED_MODULE_1__
            /* ["ɵɵtextInterpolate1"] */
            .hij(" ", ctx.data.message, "\n");
          }
        },
        directives: [_angular_common__WEBPACK_IMPORTED_MODULE_3__
        /* .NgSwitch */
        .RF, _angular_common__WEBPACK_IMPORTED_MODULE_3__
        /* .NgSwitchCase */
        .n9, _angular_material_icon__WEBPACK_IMPORTED_MODULE_4__
        /* .MatIcon */
        .Hw],
        styles: [".tick-background[_ngcontent-%COMP%]{border-radius:4px 0 0 4px;overflow:hidden;text-overflow:ellipsis;margin-right:14px;float:left}.tick-background.success[_ngcontent-%COMP%]{background-color:#7ed321}.tick-background.error[_ngcontent-%COMP%]{background-color:#d31f19}.tick-background.warn[_ngcontent-%COMP%]{background-color:#ffc058}.tick-background[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{padding:10px;margin-bottom:0;width:45px;min-height:60px;color:#fff;cursor:pointer;height:auto}.message[_ngcontent-%COMP%]{color:#000;font-size:12px;padding:12px;word-break:break-all;margin-bottom:0;white-space:pre-wrap}"]
      });
      /***/
    },

    /***/
    4486:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "Y": function Y() {
          return (
            /* binding */
            SnackBarModule
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _snack_bar_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8598);
      /* harmony import */


      var _angular_material_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2529);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4364);
      /* harmony import */


      var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1900);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);

      var SnackBarModule = /*#__PURE__*/_createClass(function SnackBarModule() {
        _classCallCheck(this, SnackBarModule);
      });

      SnackBarModule.ɵfac = function SnackBarModule_Factory(t) {
        return new (t || SnackBarModule)();
      };

      SnackBarModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__
      /* ["ɵɵdefineNgModule"] */
      .oAB({
        type: SnackBarModule
      });
      SnackBarModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__
      /* ["ɵɵdefineInjector"] */
      .cJS({
        imports: [[_angular_material_icon__WEBPACK_IMPORTED_MODULE_2__
        /* .MatIconModule */
        .Ps, _angular_common__WEBPACK_IMPORTED_MODULE_3__
        /* .CommonModule */
        .ez, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__
        /* .MatSnackBarModule */
        .ZX], _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__
        /* .MatSnackBarModule */
        .ZX]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__
        /* ["ɵɵsetNgModuleScope"] */
        .kYT(SnackBarModule, {
          declarations: [_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__
          /* .SnackBarComponent */
          .E],
          imports: [_angular_material_icon__WEBPACK_IMPORTED_MODULE_2__
          /* .MatIconModule */
          .Ps, _angular_common__WEBPACK_IMPORTED_MODULE_3__
          /* .CommonModule */
          .ez, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__
          /* .MatSnackBarModule */
          .ZX],
          exports: [_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__
          /* .SnackBarComponent */
          .E, _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_4__
          /* .MatSnackBarModule */
          .ZX]
        });
      })();
      /***/

    },

    /***/
    6718:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "o": function o() {
          return (
            /* binding */
            SnackbarService
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _snack_bar_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8598);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2316);
      /* harmony import */


      var _angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1900);

      var SnackbarService = /*#__PURE__*/function () {
        function SnackbarService(snackBar) {
          _classCallCheck(this, SnackbarService);

          this.snackBar = snackBar;
        }

        return _createClass(SnackbarService, [{
          key: "openSnackBar",
          value: function openSnackBar(message, time, type) {
            return this.snackBar.openFromComponent(_snack_bar_component__WEBPACK_IMPORTED_MODULE_0__
            /* .SnackBarComponent */
            .E, {
              duration: time,
              verticalPosition: "top",
              horizontalPosition: "center",
              panelClass: ["snackbar"],
              data: {
                message: message,
                type: type
              }
            });
          }
        }]);
      }();

      SnackbarService.ɵfac = function SnackbarService_Factory(t) {
        return new (t || SnackbarService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__
        /* ["ɵɵinject"] */
        .LFG(_angular_material_snack_bar__WEBPACK_IMPORTED_MODULE_2__
        /* .MatSnackBar */
        .ux));
      };

      SnackbarService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__
      /* ["ɵɵdefineInjectable"] */
      .Yz7({
        token: SnackbarService,
        factory: SnackbarService.ɵfac,
        providedIn: "root"
      });
      /***/
    },

    /***/
    5502:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "C2": function C2() {
          return (
            /* binding */
            LOGIN_API
          );
        },

        /* harmony export */
        "WH": function WH() {
          return (
            /* binding */
            LOGOUT_API
          );
        },

        /* harmony export */
        "Rl": function Rl() {
          return (
            /* binding */
            SIGNUP_API
          );
        },

        /* harmony export */
        "H5": function H5() {
          return (
            /* binding */
            IS_EMAIL_EXIST_API
          );
        },

        /* harmony export */
        "eB": function eB() {
          return (
            /* binding */
            VERIFY_EMAIL_API
          );
        },

        /* harmony export */
        "R5": function R5() {
          return (
            /* binding */
            USER_PROFILE_API
          );
        },

        /* harmony export */
        "cs": function cs() {
          return (
            /* binding */
            UPDATE_PASSWORD_API
          );
        },

        /* harmony export */
        "GF": function GF() {
          return (
            /* binding */
            FORGOT_PASSWORD_API
          );
        },

        /* harmony export */
        "TT": function TT() {
          return (
            /* binding */
            GET_USER_SETUP_DETAILS
          );
        },

        /* harmony export */
        "NB": function NB() {
          return (
            /* binding */
            GET_EMAIL_API
          );
        },

        /* harmony export */
        "_3": function _3() {
          return (
            /* binding */
            GET_EXECUTIVE_STATUS
          );
        },

        /* harmony export */
        "JE": function JE() {
          return (
            /* binding */
            GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API
          );
        },

        /* harmony export */
        "S4": function S4() {
          return (
            /* binding */
            LINKED_IN_URL
          );
        },

        /* harmony export */
        "xY": function xY() {
          return (
            /* binding */
            GMAIL_URL
          );
        },

        /* harmony export */
        "XT": function XT() {
          return (
            /* binding */
            PROFILE_VIEW_URL
          );
        },

        /* harmony export */
        "hT": function hT() {
          return (
            /* binding */
            COMPANY_URL
          );
        },

        /* harmony export */
        "xK": function xK() {
          return (
            /* binding */
            GET_COMPANY_DETAILS_API
          );
        },

        /* harmony export */
        "Z9": function Z9() {
          return (
            /* binding */
            GET_DROPDOWN_DATA
          );
        },

        /* harmony export */
        "Nk": function Nk() {
          return (
            /* binding */
            DEFAULT_COMPANY_LOGO
          );
        },

        /* harmony export */
        "_8": function _8() {
          return (
            /* binding */
            COMPANY_LOGO_URL
          );
        },

        /* harmony export */
        "gO": function gO() {
          return (
            /* binding */
            GET_BACK_TO_YOU
          );
        },

        /* harmony export */
        "Zf": function Zf() {
          return (
            /* binding */
            GET_EXECUTIVE_LIST_OPTIONS
          );
        },

        /* harmony export */
        "U$": function U$() {
          return (
            /* binding */
            CREATE_EXECUTIVE_LIST
          );
        },

        /* harmony export */
        "vz": function vz() {
          return (
            /* binding */
            GET_SAVED_EXECUTIVE_LIST
          );
        },

        /* harmony export */
        "E3": function E3() {
          return (
            /* binding */
            GET_PROFILE_DATA_LIMIT
          );
        },

        /* harmony export */
        "rc": function rc() {
          return (
            /* binding */
            GET_SEARCH_RESULT_API
          );
        },

        /* harmony export */
        "_H": function _H() {
          return (
            /* binding */
            GET_FIND_PHONE_EMAIL
          );
        },

        /* harmony export */
        "oP": function oP() {
          return (
            /* binding */
            GET_COMPANY_KEYEMP
          );
        }
        /* harmony export */

      });
      /* unused harmony exports ADMINSERVICEURL, SUBSCRIBEREURL, ADMINURL, VERIFY_UPDATE_MEMBER_DETAILS_API, GET_EXECUTIVE_API, PROFILE_CONTACT_INFO_URL, SALES_PROFILE */

      /* harmony import */


      var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(8135);

      var ADMINSERVICEURL = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/admin-service";
      var SUBSCRIBEREURL = ADMINSERVICEURL + "/subscriber";
      var ADMINURL = ADMINSERVICEURL + "/admin";
      var LOGIN_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/login";
      var LOGOUT_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/logout";
      var SIGNUP_API = SUBSCRIBEREURL + "/signup";
      var IS_EMAIL_EXIST_API = SUBSCRIBEREURL + "/isEmailExist";
      var VERIFY_EMAIL_API = SUBSCRIBEREURL + "/verifyEmail";
      var USER_PROFILE_API = ADMINURL + "/getLoggedInUserProfile";
      var UPDATE_PASSWORD_API = SUBSCRIBEREURL + "/updatePassword";
      var FORGOT_PASSWORD_API = SUBSCRIBEREURL + "/forgotPassword";
      var GET_USER_SETUP_DETAILS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/admin-service/onboard";
      var VERIFY_UPDATE_MEMBER_DETAILS_API = ADMINURL + "/verifyAndUpdateMemberDetails";
      var GET_EXECUTIVE_API = "getexecutive";
      var GET_EMAIL_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.BASE_URL */
      .N.BASE_URL + "email";
      var GET_EXECUTIVE_STATUS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/email/status";
      var GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/getAccessTokenByRefreshToken";
      var LINKED_IN_URL = "https://www.linkedin.com/uas/oauth2/authorization?response_type=code&client_id";
      var GMAIL_URL = "https://accounts.google.com/o/oauth2/v2/auth?response_type=code&access_type=offline&client_id";
      var PROFILE_CONTACT_INFO_URL = "https://www.linkedin.com/voyager/api/identity/profiles/tariq-haq-********/profileContactInfo";
      var PROFILE_VIEW_URL = "https://www.linkedin.com/voyager/api/identity/profiles/";
      var COMPANY_URL = "https://www.linkedin.com/voyager/api/organization/companies?decorationId=com.linkedin.voyager.deco.organization.web.WebFullCompanyMain-12&q=universalName&universalName=";
      var SALES_PROFILE = "https://www.linkedin.com/sales-api/salesApiProfiles/";
      var GET_COMPANY_DETAILS_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/companyDetails";
      var GET_DROPDOWN_DATA = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-service/filter/executive";
      var DEFAULT_COMPANY_LOGO = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.DEFULT_LOGO_URL */
      .N.DEFULT_LOGO_URL;
      var COMPANY_LOGO_URL = "https://logo.clearbit.com/";
      var GET_BACK_TO_YOU = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/getBackToYou";
      var GET_EXECUTIVE_LIST_OPTIONS = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/list-service/list/getAllLists?name=&pageIndex=0&sortBy=&sortOrder=&query=&category=";
      var CREATE_EXECUTIVE_LIST = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/createContactOrList";
      var GET_SAVED_EXECUTIVE_LIST = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/contact-service/contact/getRecentlyCreatedContacts/fromLinkedIn";
      var GET_PROFILE_DATA_LIMIT = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/admin-service/admin/checkLinkedinDataLimit";
      var GET_SEARCH_RESULT_API = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/list-service/list/isListNameAvailable?name=";
      var GET_FIND_PHONE_EMAIL = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-intelligent/email/findEmail";
      var GET_COMPANY_KEYEMP = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__
      /* .environment.SALEZCONNECT_BASE_API_URL */
      .N.SALEZCONNECT_BASE_API_URL + "/discover-service/company/executive";
      /***/
    },

    /***/
    7063:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "xT": function xT() {
          return (
            /* binding */
            ClientMessage
          );
        },

        /* harmony export */
        "n2": function n2() {
          return (
            /* binding */
            EMAIL_REQUIRED
          );
        },

        /* harmony export */
        "As": function As() {
          return (
            /* binding */
            INVALID_EMAIL
          );
        }
        /* harmony export */

      });
      /* unused harmony exports ServerMessage, NO_DATA_IMAGE_PATH, Message */


      var ServerMessage = {
        SUCCESS: 'Success!',
        UNAUTHORIZED: 'Invalid Credentials!'
      };
      var ClientMessage = {
        INTERNAL_SERVER_ERROR: 'Internal server error',
        SESSION_EXPIRED: 'Session Expired',
        ERROR: 'Error Occured!',
        PARSE_ERROR_MESSAGE: 'Error Occur while parsing page,please refresh the page',
        EMAIL_ERROR_MESSAGE: 'Error occured while getting email',
        NO_EMAIL_FOUND: 'No email found',
        REFRESH_MESSAGE: 'Please Refresh the page ',
        SERVER_ERROR: 'Server is down we will get back to you soon!!!',
        SERVER_ERROR_404: 'Not Found (404)',
        CONTACT: 'Your contact has been added',
        CREDITLIMIT: "You don't have enough credits",
        EMAIL_API_ERROR: 'Sorry something wrong at our end, Please try'
      };
      var NO_DATA_IMAGE_PATH = 'assets/images/No_Data_Illustration.svg';
      var Message = {
        SUCCESS: 'Success',
        FAILED: 'Failed',
        SESSION_EXPIRED: 'Session Expired'
      };
      var EMAIL_REQUIRED = 'Email is required';
      var INVALID_EMAIL = 'Invalid email';
      /***/
    },

    /***/
    1888:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "G": function G() {
          return (
            /* binding */
            StatusCode
          );
        }
        /* harmony export */

      });

      var StatusCode = {
        SUCCESS: 200,
        UNAUTHORIZED: 401,
        NOTFOUND: 404,
        INTERNALSERVERERROR: 500,
        CONTENTFOUND: 302,
        CREATED: 201,
        VALIDATIONFAILED: 422,
        INVALIDREQUEST: 406,
        CONFLICT: 409,
        LINKEXPIRED: 410,
        EMAILSENT: 250,
        UNKNOWN_ERROR: 0,
        NOTMODIFIED: 304,
        CONTACT_CREATION_LIMIT: 426,
        CONTACT_IMPORT_LIMIT_EXCEED: 413,
        CAMPAIGN_LIMIT_EXCEED: 417,
        NO_CONTENT: 204,
        UNPROCESSABLE: 422,
        GATEWAY_TIME_OUT: 504,
        BAD_GATEWAY: 502
      };
      /***/
    },

    /***/
    9818:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "xE": function xE() {
          return (
            /* binding */
            DEBOUNCE_TIME
          );
        },

        /* harmony export */
        "_Q": function _Q() {
          return (
            /* binding */
            SNACKBAR_TIME
          );
        },

        /* harmony export */
        "cx": function cx() {
          return (
            /* binding */
            SNACK_BAR_TYPE
          );
        },

        /* harmony export */
        "TK": function TK() {
          return (
            /* binding */
            CSRF_TOKEN
          );
        },

        /* harmony export */
        "ju": function ju() {
          return (
            /* binding */
            Event
          );
        },

        /* harmony export */
        "CS": function CS() {
          return (
            /* binding */
            SAMPLE_TEST
          );
        },

        /* harmony export */
        "rZ": function rZ() {
          return (
            /* binding */
            SAMPLE_DATA
          );
        },

        /* harmony export */
        "$C": function $C() {
          return (
            /* binding */
            LinkedInPages
          );
        },

        /* harmony export */
        "hH": function hH() {
          return (
            /* binding */
            LinkedInUrl
          );
        },

        /* harmony export */
        "me": function me() {
          return (
            /* binding */
            DEVICE_TYPE
          );
        },

        /* harmony export */
        "L$": function L$() {
          return (
            /* binding */
            ButtonType
          );
        },

        /* harmony export */
        "qE": function qE() {
          return (
            /* binding */
            ButtonSize
          );
        }
        /* harmony export */

      });
      /* unused harmony export DIALOG */


      var DEBOUNCE_TIME = 600;
      var SNACKBAR_TIME = {
        ONE_SECOND: 1000,
        TWO_SECOND: 2000,
        THREE_SECOND: 3000,
        FOUR_SECOND: 4000,
        FIVE_SECOND: 5000,
        TEN_SECOND: 10000
      };
      var DIALOG = {
        WIDTH_800: "800px",
        WIDTH_460: "460px",
        WIDTH_520: "520px",
        WIDTH_600: "600px",
        HEIGHT_500: "500px",
        WIDTH_950: "950px"
      };
      var SNACK_BAR_TYPE;

      (function (SNACK_BAR_TYPE) {
        SNACK_BAR_TYPE[SNACK_BAR_TYPE["SUCCESS"] = 0] = "SUCCESS";
        SNACK_BAR_TYPE[SNACK_BAR_TYPE["ERROR"] = 1] = "ERROR";
        SNACK_BAR_TYPE[SNACK_BAR_TYPE["WARN"] = 2] = "WARN";
      })(SNACK_BAR_TYPE || (SNACK_BAR_TYPE = {}));

      var CSRF_TOKEN = "csrfToken";
      var Event = {
        CONTENT_PAGE: "CONTENT_PAGE",
        POPUP: "POPUP",
        BACKGROUND: "BACKGROUND",
        GET_SALES_PROFILE: "GET_SALES_PROFILE",
        GET_NORMAL_PROFILE: "GET_NORMAL_PROFILE",
        SHOW_DOWNLOAD_CONNECTION: "SHOW_DOWNLOAD_CONNECTION"
      };
      var SAMPLE_TEST = "_qwert_12_90_32_blpy_qwerty_opq_";
      var SAMPLE_DATA = "linkedinextension";
      var LinkedInPages = {
        SALES_NAVIGATOR_LIST: "SALES_NAVIGATOR_PAGE",
        CONNECTION_PAGE: "CONNECTION_PAGE",
        USER_PROFILE: "USER_PROFILE",
        USER_FEED: "USER_FEED",
        COMPANY_PAGE: "COMPANY_PAGE",
        OTHER_PAGE: "OTHER_PAGE",
        CLEAR_ALL_EXECUTIVE: "CLEAR_ALL_EXECUTIVE",
        FACET_CONNECTION: "FACET_CONNECTION",
        PEOPLE: "PEOPLE",
        SEARCH: "SEARCH",
        SALES_NAVIGATOR_PROFILE: "SALES_NAVIGATOR_PROFILE"
      };
      var LinkedInUrl = {
        SALES_NAVIGATOR_LIST: "https://www.linkedin.com/sales/search/people",
        CONNECTION_URL: "https://www.linkedin.com/mynetwork/invite-connect/connections/",
        SEARCH_URL: "https://www.linkedin.com/search/results/all/",
        HOME: "https://www.linkedin.com/",
        FEED: "https://www.linkedin.com/feed/",
        USER_PROFILE: "https://www.linkedin.com/in/",
        COMPANY_URL: "https://www.linkedin.com/company",
        FACET_CONNECTION: "https://www.linkedin.com/search/results/people/?facetConnectionOf",
        PEOPLE: "https://www.linkedin.com/search/results/people/",
        SALES_NAVIGATOR_PROFILE: "https://www.linkedin.com/sales/people"
      };
      var DEVICE_TYPE = "extension";
      var ButtonType = {
        PRIMARY: "primary",
        SECONDARY: "secondary",
        TERTIARY: "tertiary",
        DELETE: "delete"
      };
      var ButtonSize = {
        SMALL: "small",
        STANDARD: "standard",
        LARGE: "large",
        MEDIUM: "medium"
      };
      /***/
    },

    /***/
    9198:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "Fs": function Fs() {
          return (
            /* binding */
            LoginWithEmailAndPassword
          );
        },

        /* harmony export */
        "Hw": function Hw() {
          return (
            /* binding */
            LoginWithEmailAndPasswordSuccess
          );
        },

        /* harmony export */
        "SO": function SO() {
          return (
            /* binding */
            LoginWithEmailAndPasswordFailed
          );
        },

        /* harmony export */
        "af": function af() {
          return (
            /* binding */
            SetLoggedIn
          );
        },

        /* harmony export */
        "RD": function RD() {
          return (
            /* binding */
            Logout
          );
        },

        /* harmony export */
        "ys": function ys() {
          return (
            /* binding */
            LogoutSuccess
          );
        },

        /* harmony export */
        "HR": function HR() {
          return (
            /* binding */
            SetAuthData
          );
        },

        /* harmony export */
        "yc": function yc() {
          return (
            /* binding */
            FetchProfileDetails
          );
        },

        /* harmony export */
        "G_": function G_() {
          return (
            /* binding */
            FetchProfileDetailsSuccess
          );
        },

        /* harmony export */
        "_S": function _S() {
          return (
            /* binding */
            FetchProfileDetailsFailed
          );
        },

        /* harmony export */
        "Wf": function Wf() {
          return (
            /* binding */
            GetNewAccessToken
          );
        },

        /* harmony export */
        "xL": function xL() {
          return (
            /* binding */
            GetNewAccessTokenSuccess
          );
        },

        /* harmony export */
        "cO": function cO() {
          return (
            /* binding */
            GetNewAccessTokenFailed
          );
        },

        /* harmony export */
        "yT": function yT() {
          return (
            /* binding */
            GetUserSetupDetails
          );
        },

        /* harmony export */
        "WM": function WM() {
          return (
            /* binding */
            GetUserSetupDetailsSuccess
          );
        },

        /* harmony export */
        "Ju": function Ju() {
          return (
            /* binding */
            GetUserSetupDetailsFailure
          );
        },

        /* harmony export */
        "$L": function $L() {
          return (
            /* binding */
            UpdateNewUserSetupDetails
          );
        }
        /* harmony export */

      });
      /* unused harmony export ResetAuthResponse */


      var LoginWithEmailAndPassword = /*#__PURE__*/_createClass(function LoginWithEmailAndPassword(payload, rememberMe) {
        _classCallCheck(this, LoginWithEmailAndPassword);

        this.payload = payload;
        this.rememberMe = rememberMe;
      });

      LoginWithEmailAndPassword.type = '[Login] LoginWithEmailAndPassword';

      var LoginWithEmailAndPasswordSuccess = /*#__PURE__*/_createClass(function LoginWithEmailAndPasswordSuccess(user) {
        _classCallCheck(this, LoginWithEmailAndPasswordSuccess);

        this.user = user;
      });

      LoginWithEmailAndPasswordSuccess.type = '[Login] LoginWithEmailAndPasswordSuccess';

      var LoginWithEmailAndPasswordFailed = /*#__PURE__*/_createClass(function LoginWithEmailAndPasswordFailed(error) {
        _classCallCheck(this, LoginWithEmailAndPasswordFailed);

        this.error = error;
      });

      LoginWithEmailAndPasswordFailed.type = '[Login] LoginWithEmailAndPasswordFailed';

      var SetLoggedIn = /*#__PURE__*/_createClass(function SetLoggedIn(isLoggedIn) {
        _classCallCheck(this, SetLoggedIn);

        this.isLoggedIn = isLoggedIn;
      });

      SetLoggedIn.type = '[Login] SetLoggedIn';

      var Logout = /*#__PURE__*/_createClass(function Logout(payload) {
        _classCallCheck(this, Logout);

        this.payload = payload;
      });

      Logout.type = '[Auth] Logout';

      var LogoutSuccess = /*#__PURE__*/_createClass(function LogoutSuccess() {
        _classCallCheck(this, LogoutSuccess);
      });

      LogoutSuccess.type = '[Auth] LogoutSuccess';

      var ResetAuthResponse = /*#__PURE__*/_createClass(function ResetAuthResponse() {
        _classCallCheck(this, ResetAuthResponse);
      });

      ResetAuthResponse.type = '[Auth] ResetAuthResponse';

      var SetAuthData = /*#__PURE__*/_createClass(function SetAuthData(authResponse) {
        _classCallCheck(this, SetAuthData);

        this.authResponse = authResponse;
      });

      SetAuthData.type = '[Auth] SetAuthData';

      var FetchProfileDetails = /*#__PURE__*/_createClass(function FetchProfileDetails() {
        _classCallCheck(this, FetchProfileDetails);
      });

      FetchProfileDetails.type = '[UserProfile] FetchProfileDetails';

      var FetchProfileDetailsSuccess = /*#__PURE__*/_createClass(function FetchProfileDetailsSuccess(userProfile) {
        _classCallCheck(this, FetchProfileDetailsSuccess);

        this.userProfile = userProfile;
      });

      FetchProfileDetailsSuccess.type = '[UserProfile] FetchProfileDetailsSuccess';

      var FetchProfileDetailsFailed = /*#__PURE__*/_createClass(function FetchProfileDetailsFailed() {
        _classCallCheck(this, FetchProfileDetailsFailed);
      });

      FetchProfileDetailsFailed.type = '[UserProfile] FetchProfileDetailsFailed';

      var GetNewAccessToken = /*#__PURE__*/_createClass(function GetNewAccessToken(payload) {
        _classCallCheck(this, GetNewAccessToken);

        this.payload = payload;
      });

      GetNewAccessToken.type = '[Auth] GetNewAccessToken';

      var GetNewAccessTokenSuccess = /*#__PURE__*/_createClass(function GetNewAccessTokenSuccess(user) {
        _classCallCheck(this, GetNewAccessTokenSuccess);

        this.user = user;
      });

      GetNewAccessTokenSuccess.type = '[Auth] GetNewAccessTokenSuccess';

      var GetNewAccessTokenFailed = /*#__PURE__*/_createClass(function GetNewAccessTokenFailed() {
        _classCallCheck(this, GetNewAccessTokenFailed);
      });

      GetNewAccessTokenFailed.type = '[Auth] GetNewAccessTokenFailed';

      var GetUserSetupDetails = /*#__PURE__*/_createClass(function GetUserSetupDetails() {
        _classCallCheck(this, GetUserSetupDetails);
      });

      GetUserSetupDetails.type = "[DASHBOARD] GetUserSetupDetails";

      var GetUserSetupDetailsSuccess = /*#__PURE__*/_createClass(function GetUserSetupDetailsSuccess(res) {
        _classCallCheck(this, GetUserSetupDetailsSuccess);

        this.res = res;
      });

      GetUserSetupDetailsSuccess.type = "[DASHBOARD] GetUserSetupDetailsSuccess";

      var GetUserSetupDetailsFailure = /*#__PURE__*/_createClass(function GetUserSetupDetailsFailure() {
        _classCallCheck(this, GetUserSetupDetailsFailure);
      });

      GetUserSetupDetailsFailure.type = "[DASHBOARD] GetUserSetupDetailsFailure";

      var UpdateNewUserSetupDetails = /*#__PURE__*/_createClass(function UpdateNewUserSetupDetails(id) {
        _classCallCheck(this, UpdateNewUserSetupDetails);

        this.id = id;
      });

      UpdateNewUserSetupDetails.type = "[DASHBOARD] UpdateNewUserSetupDetails";
      /***/
    },

    /***/
    8903:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "r": function r() {
          return (
            /* binding */
            LoginService
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _constant_api_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5502);
      /* harmony import */


      var _common_service_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2101);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2316);

      var LoginService = /*#__PURE__*/function () {
        function LoginService(apiService) {
          _classCallCheck(this, LoginService);

          this.apiService = apiService;
        }

        return _createClass(LoginService, [{
          key: "login",
          value: function login(payload) {
            return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .LOGIN_API */
            .C2, payload);
          }
        }, {
          key: "logout",
          value: function logout(payload) {
            return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .LOGOUT_API */
            .WH, payload);
          }
        }, {
          key: "fetchUserProfile",
          value: function fetchUserProfile() {
            return this.apiService.get(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .USER_PROFILE_API */
            .R5, {});
          }
        }, {
          key: "getNewAccessToken",
          value: function getNewAccessToken(refreshToken) {
            return this.apiService.post(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API */
            .JE, {
              refreshToken: refreshToken
            });
          }
        }, {
          key: "getUserSetupDetails",
          value: function getUserSetupDetails() {
            return this.apiService.get(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .GET_USER_SETUP_DETAILS */
            .TT, {});
          }
        }, {
          key: "updateNewUserSetupDetails",
          value: function updateNewUserSetupDetails(id) {
            return this.apiService.put(_constant_api_url__WEBPACK_IMPORTED_MODULE_0__
            /* .GET_USER_SETUP_DETAILS */
            .TT + '/' + id + '/true', {});
          }
        }]);
      }();

      LoginService.ɵfac = function LoginService_Factory(t) {
        return new (t || LoginService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__
        /* ["ɵɵinject"] */
        .LFG(_common_service_api_service__WEBPACK_IMPORTED_MODULE_1__
        /* .ApiService */
        .s));
      };

      LoginService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__
      /* ["ɵɵdefineInjectable"] */
      .Yz7({
        token: LoginService,
        factory: LoginService.ɵfac,
        providedIn: 'root'
      });
      /***/
    },

    /***/
    8341:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict"; // EXPORTS

      __webpack_require__.d(__webpack_exports__, {
        "U": function U() {
          return (
            /* binding */
            ScLoginState
          );
        }
      }); // EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs


      var tslib_es6 = __webpack_require__(2321); // EXTERNAL MODULE: ./node_modules/@ngxs/store/fesm2015/ngxs-store.js + 3 modules


      var ngxs_store = __webpack_require__(6090); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/action/login.action.ts


      var login_action = __webpack_require__(9198); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/tap.js + 1 modules


      var tap = __webpack_require__(4921); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/catchError.js


      var catchError = __webpack_require__(8293); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/service/login.service.ts


      var login_service = __webpack_require__(8903); // EXTERNAL MODULE: ./angular/src/app/constant/status-code.ts


      var status_code = __webpack_require__(1888); // EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.service.ts


      var snack_bar_service = __webpack_require__(6718); // EXTERNAL MODULE: ./angular/src/app/constant/message.ts


      var message = __webpack_require__(7063); // EXTERNAL MODULE: ./angular/src/app/constant/value.ts


      var value = __webpack_require__(9818); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/action/popup.action.ts


      var popup_action = __webpack_require__(1950);

      ; // CONCATENATED MODULE: ./angular/src/app/app.state.model.ts

      var AppState = {
        AUTH: 'auth'
      }; // EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js

      var core = __webpack_require__(2316);

      ; // CONCATENATED MODULE: ./angular/src/app/modules/popup/pages/login/store/state/login.state.ts

      var ScLoginState = /*#__PURE__*/function () {
        function ScLoginState(loginService, snackbarService) {
          _classCallCheck(this, ScLoginState);

          this.loginService = loginService;
          this.snackbarService = snackbarService;
        }

        return _createClass(ScLoginState, [{
          key: "getUserSetupDetails",
          value:
          /**Commands */
          function getUserSetupDetails(ctx) {
            return this.loginService.getUserSetupDetails().pipe((0, tap
            /* tap */
            .b)(function (response) {
              if (response.statusCode === status_code
              /* StatusCode.SUCCESS */
              .G.SUCCESS && response.data) {
                return ctx.dispatch(new login_action
                /* GetUserSetupDetailsSuccess */
                .WM(response));
              } else {
                return ctx.dispatch(new login_action
                /* GetUserSetupDetailsFailure */
                .Ju());
              }
            }));
          }
        }, {
          key: "updateNewUserSetupDetails",
          value: function updateNewUserSetupDetails(ctx, action) {
            return this.loginService.updateNewUserSetupDetails(action.id).pipe((0, tap
            /* tap */
            .b)(function (response) {
              if (response.statusCode === status_code
              /* StatusCode.SUCCESS */
              .G.SUCCESS) {}
            }));
          }
        }, {
          key: "loginWithEmailPassword",
          value: function loginWithEmailPassword(ctx, action) {
            var payload = {
              email: action.payload.email,
              password: action.payload.password,
              deviceType: value
              /* DEVICE_TYPE */
              .me
            };

            if (action.rememberMe) {
              ctx.patchState({
                email: action.payload.email
              });
            }

            ctx.patchState({
              isLoginLoading: true
            });
            return this.loginService.login(payload).pipe((0, tap
            /* tap */
            .b)(function (res) {
              if (res.statusCode === status_code
              /* StatusCode.SUCCESS */
              .G.SUCCESS && res.data !== null) {
                return ctx.dispatch(new login_action
                /* LoginWithEmailAndPasswordSuccess */
                .Hw(res.data));
              } else {
                return ctx.dispatch(new login_action
                /* LoginWithEmailAndPasswordFailed */
                .SO(res));
              }
            }), (0, catchError
            /* catchError */
            .K)(function (err) {
              return ctx.dispatch(new login_action
              /* LoginWithEmailAndPasswordFailed */
              .SO({
                message: 'Error Occured'
              }));
            }));
          }
        }, {
          key: "logout",
          value: function logout(ctx, action) {
            return this.loginService.logout(action.payload).pipe((0, tap
            /* tap */
            .b)(function (res) {
              return ctx.dispatch(new login_action
              /* LogoutSuccess */
              .ys());
            }));
          }
          /**Events */

        }, {
          key: "setUserStateOnLoginWithEmailAndPasswordSuccess",
          value: function setUserStateOnLoginWithEmailAndPasswordSuccess(ctx, event) {
            ctx.dispatch(new login_action
            /* FetchProfileDetails */
            .yc());
            ctx.patchState({
              authData: event.user,
              isLoggedIn: true,
              isLoginLoading: false
            });
            ctx.dispatch(new login_action
            /* GetUserSetupDetails */
            .yT());
          }
        }, {
          key: "setStateOnLogout",
          value: function setStateOnLogout(ctx) {
            ctx.patchState({
              isLoggedIn: false,
              authData: undefined,
              isLoginLoading: false
            });
            ctx.dispatch(new login_action
            /* SetLoggedIn */
            .af(false));
          }
        }, {
          key: "setStateOnGetNewAccessTokenSuccess",
          value: function setStateOnGetNewAccessTokenSuccess(ctx, event) {
            ctx.patchState({
              authData: event.authResponse.data,
              isLoginLoading: false
            });
          }
        }, {
          key: "setUserStateOnLoginWithEmailAndPasswordFailed",
          value: function setUserStateOnLoginWithEmailAndPasswordFailed(ctx, event) {
            ctx.patchState({
              authData: event.error,
              isLoggedIn: false,
              isLoginLoading: false
            });
          }
        }, {
          key: "setLoggedIn",
          value: function setLoggedIn(ctx, event) {
            if (!event.isLoggedIn) {
              ctx.patchState({
                authData: undefined,
                isLoggedIn: event.isLoggedIn
              });
              ctx.dispatch(new popup_action
              /* ResetExecutiveList */
              .x5());
            } else {
              ctx.patchState({
                isLoggedIn: event.isLoggedIn
              });
            }
          }
        }, {
          key: "fetchUserProfile",
          value: function fetchUserProfile(ctx) {
            return this.loginService.fetchUserProfile().pipe((0, tap
            /* tap */
            .b)(function (res) {
              if (res.statusCode === status_code
              /* StatusCode.SUCCESS */
              .G.SUCCESS && res.data !== null) {
                return ctx.dispatch(new login_action
                /* FetchProfileDetailsSuccess */
                .G_(res.data));
              }
            }), (0, catchError
            /* catchError */
            .K)(function (err) {
              return ctx.dispatch(new login_action
              /* FetchProfileDetailsFailed */
              ._S());
            }));
          }
        }, {
          key: "setStateOnUserProfileSuccess",
          value: function setStateOnUserProfileSuccess(ctx, event) {
            ctx.patchState({
              user: event.userProfile
            });
          }
        }, {
          key: "setStateOnFetchProfileDetailsFailed",
          value: function setStateOnFetchProfileDetailsFailed(ctx) {
            ctx.patchState({
              user: undefined
            });
          }
        }, {
          key: "getNewAccessToken",
          value: function getNewAccessToken(ctx, action) {
            return this.loginService.getNewAccessToken(action.payload).pipe((0, tap
            /* tap */
            .b)(function (res) {
              if (res.statusCode === status_code
              /* StatusCode.SUCCESS */
              .G.SUCCESS) {
                var successRes = {
                  accessToken: res.data.accessToken,
                  dsmID: res.data.dsmID,
                  refreshToken: res.data.refreshToken,
                  email: action.payload.email,
                  isRememberMe: action.payload.isRememberMe
                };
                return ctx.dispatch(new login_action
                /* GetNewAccessTokenSuccess */
                .xL(successRes));
              } else {
                return ctx.dispatch(new login_action
                /* GetNewAccessTokenFailed */
                .cO());
              }
            }), (0, catchError
            /* catchError */
            .K)(function (err) {
              return ctx.dispatch(new login_action
              /* GetNewAccessTokenFailed */
              .cO());
            }));
          }
        }, {
          key: "setUserStateOnGetNewAccessTokenSuccess",
          value: function setUserStateOnGetNewAccessTokenSuccess(ctx, event) {
            ctx.patchState({
              authData: event.user
            });
          }
        }, {
          key: "setUserStateOnGetNewAccessTokenFailed",
          value: function setUserStateOnGetNewAccessTokenFailed(ctx) {
            this.snackbarService.openSnackBar(message
            /* ClientMessage.SESSION_EXPIRED */
            .xT.SESSION_EXPIRED, value
            /* SNACKBAR_TIME.THREE_SECOND */
            ._Q.THREE_SECOND, value
            /* SNACK_BAR_TYPE.ERROR */
            .cx.ERROR);
            ctx.dispatch(new login_action
            /* SetLoggedIn */
            .af(false));
          }
        }, {
          key: "setStateOnGetUserSetupDetailsSuccess",
          value: function setStateOnGetUserSetupDetailsSuccess(ctx, event) {
            ctx.patchState({
              getUserSetupDetailsResponse: event.res
            });

            if ((event === null || event === void 0 ? void 0 : event.res) !== null) {
              for (var i = 0; i < (event === null || event === void 0 ? void 0 : (_event$res = event.res) === null || _event$res === void 0 ? void 0 : (_event$res$data = _event$res.data) === null || _event$res$data === void 0 ? void 0 : _event$res$data.length); i++) {
                var _event$res, _event$res$data, _event$res2, _event$res2$data$i, _event$res3, _event$res3$data$i;

                if ('Install Chrome Extension' === (event === null || event === void 0 ? void 0 : (_event$res2 = event.res) === null || _event$res2 === void 0 ? void 0 : (_event$res2$data$i = _event$res2.data[i]) === null || _event$res2$data$i === void 0 ? void 0 : _event$res2$data$i.name) && !(event !== null && event !== void 0 && (_event$res3 = event.res) !== null && _event$res3 !== void 0 && (_event$res3$data$i = _event$res3.data[i]) !== null && _event$res3$data$i !== void 0 && _event$res3$data$i.status)) {
                  var _event$res4, _event$res4$data$i;

                  ctx.dispatch(new login_action
                  /* UpdateNewUserSetupDetails */
                  .$L(event === null || event === void 0 ? void 0 : (_event$res4 = event.res) === null || _event$res4 === void 0 ? void 0 : (_event$res4$data$i = _event$res4.data[i]) === null || _event$res4$data$i === void 0 ? void 0 : _event$res4$data$i.id));
                  break;
                }
              }
            }
          }
        }, {
          key: "setStateOnGetUserSetupDetailsFailure",
          value: function setStateOnGetUserSetupDetailsFailure(ctx) {
            ctx.patchState({
              getUserSetupDetailsResponse: undefined
            });
          }
        }], [{
          key: "getUserSetupDetailsResponse",
          value: function getUserSetupDetailsResponse(state) {
            return state.getUserSetupDetailsResponse;
          }
        }, {
          key: "getUserProfile",
          value: function getUserProfile(state) {
            return state.user;
          }
        }, {
          key: "getLoginUserDetails",
          value: function getLoginUserDetails(state) {
            return state.authData;
          }
        }, {
          key: "isLoggedIn",
          value: function isLoggedIn(state) {
            return state.isLoggedIn;
          }
        }, {
          key: "isLoginLoading",
          value: function isLoginLoading(state) {
            return state.isLoginLoading;
          }
        }]);
      }();

      ScLoginState.ɵfac = function ScLoginState_Factory(t) {
        return new (t || ScLoginState)(core
        /* ɵɵinject */
        .LFG(login_service
        /* LoginService */
        .r), core
        /* ɵɵinject */
        .LFG(snack_bar_service
        /* SnackbarService */
        .o));
      };

      ScLoginState.ɵprov = /*@__PURE__*/core
      /* ɵɵdefineInjectable */
      .Yz7({
        token: ScLoginState,
        factory: ScLoginState.ɵfac
      });
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetUserSetupDetails */
      .yT), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "getUserSetupDetails", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* UpdateNewUserSetupDetails */
      .$L), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* UpdateNewUserSetupDetails */
      .$L]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "updateNewUserSetupDetails", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* LoginWithEmailAndPassword */
      .Fs), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* LoginWithEmailAndPassword */
      .Fs]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "loginWithEmailPassword", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* Logout */
      .RD), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* Logout */
      .RD]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "logout", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* LoginWithEmailAndPasswordSuccess */
      .Hw), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* LoginWithEmailAndPasswordSuccess */
      .Hw]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setUserStateOnLoginWithEmailAndPasswordSuccess", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* LogoutSuccess */
      .ys), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnLogout", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* SetAuthData */
      .HR), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* SetAuthData */
      .HR]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnGetNewAccessTokenSuccess", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* LoginWithEmailAndPasswordFailed */
      .SO), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* LoginWithEmailAndPasswordFailed */
      .SO]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setUserStateOnLoginWithEmailAndPasswordFailed", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* SetLoggedIn */
      .af), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* SetLoggedIn */
      .af]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setLoggedIn", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* FetchProfileDetails */
      .yc), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "fetchUserProfile", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* FetchProfileDetailsSuccess */
      .G_), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* FetchProfileDetailsSuccess */
      .G_]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnUserProfileSuccess", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* FetchProfileDetailsFailed */
      ._S), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnFetchProfileDetailsFailed", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetNewAccessToken */
      .Wf), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* GetNewAccessToken */
      .Wf]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "getNewAccessToken", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetNewAccessTokenSuccess */
      .xL), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* GetNewAccessTokenSuccess */
      .xL]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setUserStateOnGetNewAccessTokenSuccess", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetNewAccessTokenFailed */
      .cO), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setUserStateOnGetNewAccessTokenFailed", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetUserSetupDetailsSuccess */
      .WM), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object, login_action
      /* GetUserSetupDetailsSuccess */
      .WM]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnGetUserSetupDetailsSuccess", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Action */
      .aU)(login_action
      /* GetUserSetupDetailsFailure */
      .Ju), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState.prototype, "setStateOnGetUserSetupDetailsFailure", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Selector */
      .Qf)(), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState, "getUserSetupDetailsResponse", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Selector */
      .Qf)(), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState, "getUserProfile", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Selector */
      .Qf)(), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState, "getLoginUserDetails", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Selector */
      .Qf)(), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState, "isLoggedIn", null);
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Selector */
      .Qf)(), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Function), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib_es6
      /* __metadata */
      .w6)("design:returntype", void 0)], ScLoginState, "isLoginLoading", null);
      ScLoginState = (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* State */
      .ZM)({
        name: AppState.AUTH,
        defaults: {
          email: '',
          authData: undefined,
          isLoggedIn: false,
          isLoginLoading: false,
          user: undefined,
          getUserSetupDetailsResponse: undefined
        }
      }), (0, tslib_es6
      /* __metadata */
      .w6)("design:paramtypes", [login_service
      /* LoginService */
      .r, snack_bar_service
      /* SnackbarService */
      .o])], ScLoginState);
      /***/
    },

    /***/
    8233:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "mA": function mA() {
          return (
            /* binding */
            GetCompanyDetails
          );
        },

        /* harmony export */
        "lQ": function lQ() {
          return (
            /* binding */
            SetCompanyId
          );
        },

        /* harmony export */
        "tc": function tc() {
          return (
            /* binding */
            GetExecutiveFilterOptions
          );
        },

        /* harmony export */
        "ds": function ds() {
          return (
            /* binding */
            GetExecutiveListOptions
          );
        },

        /* harmony export */
        "uw": function uw() {
          return (
            /* binding */
            GetCompanyKeyEmp
          );
        },

        /* harmony export */
        "ct": function ct() {
          return (
            /* binding */
            FetchEmail
          );
        },

        /* harmony export */
        "Vx": function Vx() {
          return (
            /* binding */
            FetchPhone
          );
        },

        /* harmony export */
        "L5": function L5() {
          return (
            /* binding */
            SetSourceId
          );
        },

        /* harmony export */
        "jQ": function jQ() {
          return (
            /* binding */
            SetCompanyExecutives
          );
        },

        /* harmony export */
        "Vt": function Vt() {
          return (
            /* binding */
            FetchLogo
          );
        },

        /* harmony export */
        "HD": function HD() {
          return (
            /* binding */
            GetBackToYou
          );
        },

        /* harmony export */
        "J0": function J0() {
          return (
            /* binding */
            SearchListName
          );
        },

        /* harmony export */
        "f2": function f2() {
          return (
            /* binding */
            CreateExecutiveList
          );
        },

        /* harmony export */
        "aR": function aR() {
          return (
            /* binding */
            GetSavedExecutiveList
          );
        },

        /* harmony export */
        "x6": function x6() {
          return (
            /* binding */
            GetProfileEmailLimit
          );
        },

        /* harmony export */
        "Z$": function Z$() {
          return (
            /* binding */
            GetChromeStorageData
          );
        },

        /* harmony export */
        "nY": function nY() {
          return (
            /* binding */
            GetChromeCompanyStorageData
          );
        },

        /* harmony export */
        "sQ": function sQ() {
          return (
            /* binding */
            IsGetBackToYou
          );
        },

        /* harmony export */
        "ve": function ve() {
          return (
            /* binding */
            ClearChromeCompanyStorageData
          );
        },

        /* harmony export */
        "c3": function c3() {
          return (
            /* binding */
            GetAllTheExecutiveId
          );
        },

        /* harmony export */
        "GE": function GE() {
          return (
            /* binding */
            ClearOldUrl
          );
        },

        /* harmony export */
        "PR": function PR() {
          return (
            /* binding */
            seniorityFilters
          );
        },

        /* harmony export */
        "cz": function cz() {
          return (
            /* binding */
            departmentFilters
          );
        },

        /* harmony export */
        "LS": function LS() {
          return (
            /* binding */
            searchFilters
          );
        }
        /* harmony export */

      });

      var GetCompanyDetails = /*#__PURE__*/_createClass(function GetCompanyDetails(companySourceId, website, companyName, departmentId, executiveLevelId, searchTerm) {
        _classCallCheck(this, GetCompanyDetails);

        this.companySourceId = companySourceId;
        this.website = website;
        this.companyName = companyName;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
      });

      GetCompanyDetails.type = "[Company] Get Details";

      var SetCompanyId = /*#__PURE__*/_createClass(function SetCompanyId(companyId) {
        _classCallCheck(this, SetCompanyId);

        this.companyId = companyId;
      });

      SetCompanyId.type = "[Company] Set Company ID";

      var GetExecutiveFilterOptions = /*#__PURE__*/_createClass(function GetExecutiveFilterOptions() {
        _classCallCheck(this, GetExecutiveFilterOptions);
      });

      GetExecutiveFilterOptions.type = "[Company] Get Executive Filter Options";

      var GetExecutiveListOptions = /*#__PURE__*/_createClass(function GetExecutiveListOptions() {
        _classCallCheck(this, GetExecutiveListOptions);
      });

      GetExecutiveListOptions.type = "[Company] Get Executive List Options";

      var GetCompanyKeyEmp = /*#__PURE__*/_createClass(function GetCompanyKeyEmp(companyId, departmentId, executiveLevelId) {
        var searchTerm = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";

        _classCallCheck(this, GetCompanyKeyEmp);

        this.companyId = companyId;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
      });

      GetCompanyKeyEmp.type = "[Company] Get Executives";

      var FetchEmail = /*#__PURE__*/_createClass(function FetchEmail(payload) {
        _classCallCheck(this, FetchEmail);

        this.payload = payload;
      });

      FetchEmail.type = "[Company] Fetch Email";

      var FetchPhone = /*#__PURE__*/_createClass(function FetchPhone(payload) {
        _classCallCheck(this, FetchPhone);

        this.payload = payload;
      });

      FetchPhone.type = "[Company] Fetch Phone";

      var SetSourceId = /*#__PURE__*/_createClass(function SetSourceId(sourceId) {
        var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
        var sourceName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
        var firstName = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
        var lastName = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;
        var domain = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : null;
        var staffCount = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : null;

        _classCallCheck(this, SetSourceId);

        this.sourceId = sourceId;
        this.source = source;
        this.sourceName = sourceName;
        this.firstName = firstName;
        this.lastName = lastName;
        this.domain = domain;
        this.staffCount = staffCount;
      });

      SetSourceId.type = "[Company] Set Source ID";

      var SetCompanyExecutives = /*#__PURE__*/_createClass(function SetCompanyExecutives(executives) {
        _classCallCheck(this, SetCompanyExecutives);

        this.executives = executives;
      });

      SetCompanyExecutives.type = "[Company] Set Company Executives";

      var FetchLogo = /*#__PURE__*/_createClass(function FetchLogo(payload) {
        _classCallCheck(this, FetchLogo);

        this.payload = payload;
      });

      FetchLogo.type = "[Company] Fetch Logo";

      var GetBackToYou = /*#__PURE__*/_createClass(function GetBackToYou(request) {
        _classCallCheck(this, GetBackToYou);

        this.request = request;
      });

      GetBackToYou.type = "[Company] Get Back To You";

      var SearchListName = /*#__PURE__*/_createClass(function SearchListName(name) {
        _classCallCheck(this, SearchListName);

        this.name = name;
      });

      SearchListName.type = "[Company] Search List Name";

      var CreateExecutiveList = /*#__PURE__*/_createClass(function CreateExecutiveList(payload) {
        _classCallCheck(this, CreateExecutiveList);

        this.payload = payload;
      });

      CreateExecutiveList.type = "[company] create executive list";

      var GetSavedExecutiveList = /*#__PURE__*/_createClass(function GetSavedExecutiveList() {
        _classCallCheck(this, GetSavedExecutiveList);
      });

      GetSavedExecutiveList.type = "[Company] Get Saved Executive List ";

      var GetProfileEmailLimit = /*#__PURE__*/_createClass(function GetProfileEmailLimit() {
        _classCallCheck(this, GetProfileEmailLimit);
      });

      GetProfileEmailLimit.type = "[Company] Get Profile Email Limit";

      var GetChromeStorageData = /*#__PURE__*/_createClass(function GetChromeStorageData(data) {
        _classCallCheck(this, GetChromeStorageData);

        this.data = data;
      });

      GetChromeStorageData.type = "[Company] GetChromeStorageData ";

      var GetChromeCompanyStorageData = /*#__PURE__*/_createClass(function GetChromeCompanyStorageData(data) {
        _classCallCheck(this, GetChromeCompanyStorageData);

        this.data = data;
      });

      GetChromeCompanyStorageData.type = "[Company] GetChromeCompanyStorageData ";

      var IsGetBackToYou = /*#__PURE__*/_createClass(function IsGetBackToYou(request) {
        _classCallCheck(this, IsGetBackToYou);

        this.request = request;
      });

      IsGetBackToYou.type = "[Company] IsGetBackToYou";

      var ClearChromeCompanyStorageData = /*#__PURE__*/_createClass(function ClearChromeCompanyStorageData() {
        _classCallCheck(this, ClearChromeCompanyStorageData);
      });

      ClearChromeCompanyStorageData.type = "[Company] Clear Chrome Company Storage Data";

      var GetAllTheExecutiveId = /*#__PURE__*/_createClass(function GetAllTheExecutiveId(request) {
        _classCallCheck(this, GetAllTheExecutiveId);

        this.request = request;
      });

      GetAllTheExecutiveId.type = "[company] GetAllTheExecutiveId";

      var ClearOldUrl = /*#__PURE__*/_createClass(function ClearOldUrl(request) {
        _classCallCheck(this, ClearOldUrl);

        this.request = request;
      });

      ClearOldUrl.type = "[company] ClearOldUrl";

      var seniorityFilters = /*#__PURE__*/_createClass(function seniorityFilters(request) {
        _classCallCheck(this, seniorityFilters);

        this.request = request;
      });

      seniorityFilters.type = "[company] seniorityFilters";

      var departmentFilters = /*#__PURE__*/_createClass(function departmentFilters(request) {
        _classCallCheck(this, departmentFilters);

        this.request = request;
      });

      departmentFilters.type = "[company] departmentFilters";

      var searchFilters = /*#__PURE__*/_createClass(function searchFilters(request) {
        _classCallCheck(this, searchFilters);

        this.request = request;
      });

      searchFilters.type = "[company] type";
      /***/
    },

    /***/
    6136:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "LE": function LE() {
          return (
            /* binding */
            ExtractCompanyDetails
          );
        },

        /* harmony export */
        "b6": function b6() {
          return (
            /* binding */
            GetCompanyKeyEmpInExtractAny
          );
        },

        /* harmony export */
        "ct": function ct() {
          return (
            /* binding */
            FetchEmail
          );
        },

        /* harmony export */
        "Vx": function Vx() {
          return (
            /* binding */
            FetchPhone
          );
        },

        /* harmony export */
        "yX": function yX() {
          return (
            /* binding */
            SetExtractCompanyExecutives
          );
        },

        /* harmony export */
        "Vt": function Vt() {
          return (
            /* binding */
            FetchLogo
          );
        },

        /* harmony export */
        "Sf": function Sf() {
          return (
            /* binding */
            CallAPI
          );
        }
        /* harmony export */

      });

      var ExtractCompanyDetails = /*#__PURE__*/_createClass(function ExtractCompanyDetails(request) {
        _classCallCheck(this, ExtractCompanyDetails);

        this.request = request;
      });

      ExtractCompanyDetails.type = "[ExtractCompany] Extract Company Details";

      var GetCompanyKeyEmpInExtractAny = /*#__PURE__*/_createClass(function GetCompanyKeyEmpInExtractAny(companyId, departmentId, executiveLevelId) {
        var searchTerm = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";

        _classCallCheck(this, GetCompanyKeyEmpInExtractAny);

        this.companyId = companyId;
        this.departmentId = departmentId;
        this.executiveLevelId = executiveLevelId;
        this.searchTerm = searchTerm;
      });

      GetCompanyKeyEmpInExtractAny.type = "[ExtractCompany] Get Executives";

      var FetchEmail = /*#__PURE__*/_createClass(function FetchEmail(payload) {
        _classCallCheck(this, FetchEmail);

        this.payload = payload;
      });

      FetchEmail.type = "[ExtractCompany] FetchEmail";

      var FetchPhone = /*#__PURE__*/_createClass(function FetchPhone(payload) {
        _classCallCheck(this, FetchPhone);

        this.payload = payload;
      });

      FetchPhone.type = "[ExtractCompany] FetchPhone";

      var SetExtractCompanyExecutives = /*#__PURE__*/_createClass(function SetExtractCompanyExecutives(executives) {
        _classCallCheck(this, SetExtractCompanyExecutives);

        this.executives = executives;
      });

      SetExtractCompanyExecutives.type = "[ExtractCompany] Set Company Executives";

      var FetchLogo = /*#__PURE__*/_createClass(function FetchLogo(payload) {
        _classCallCheck(this, FetchLogo);

        this.payload = payload;
      });

      FetchLogo.type = "[ExtractCompany] Fetch Logo";

      var CallAPI = /*#__PURE__*/_createClass(function CallAPI(payload) {
        _classCallCheck(this, CallAPI);

        this.payload = payload;
      });

      CallAPI.type = "[ExtractCompany] CallAPI";
      /***/
    },

    /***/
    1950:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "jq": function jq() {
          return (
            /* binding */
            StartCollectingData
          );
        },

        /* harmony export */
        "PQ": function PQ() {
          return (
            /* binding */
            ShowExecutiveList
          );
        },

        /* harmony export */
        "Mr": function Mr() {
          return (
            /* binding */
            ShowExecutiveListInBulk
          );
        },

        /* harmony export */
        "hi": function hi() {
          return (
            /* binding */
            CurrentPageUrl
          );
        },

        /* harmony export */
        "py": function py() {
          return (
            /* binding */
            ShowExecutiveEmailId
          );
        },

        /* harmony export */
        "SI": function SI() {
          return (
            /* binding */
            ShowExecutiveEmailIdSuccess
          );
        },

        /* harmony export */
        "KQ": function KQ() {
          return (
            /* binding */
            ShowExecutiveEmailIdFailed
          );
        },

        /* harmony export */
        "x5": function x5() {
          return (
            /* binding */
            ResetExecutiveList
          );
        },

        /* harmony export */
        "Ae": function Ae() {
          return (
            /* binding */
            UpdateExecutiveList
          );
        },

        /* harmony export */
        "w6": function w6() {
          return (
            /* binding */
            ResetDailyLimit
          );
        },

        /* harmony export */
        "A7": function A7() {
          return (
            /* binding */
            ResetDailyLimitSuccess
          );
        },

        /* harmony export */
        "T2": function T2() {
          return (
            /* binding */
            ResetDailyLimitFailed
          );
        },

        /* harmony export */
        "xS": function xS() {
          return (
            /* binding */
            ShowLinkedSalesNavigator
          );
        },

        /* harmony export */
        "rz": function rz() {
          return (
            /* binding */
            ShowLinkedSearchPage
          );
        },

        /* harmony export */
        "OI": function OI() {
          return (
            /* binding */
            ShowLinkedPeoplePage
          );
        },

        /* harmony export */
        "pc": function pc() {
          return (
            /* binding */
            GetExecutiveList
          );
        },

        /* harmony export */
        "Sh": function Sh() {
          return (
            /* binding */
            StoreExecutiveResponse
          );
        },

        /* harmony export */
        "HD": function HD() {
          return (
            /* binding */
            GetProfileView
          );
        },

        /* harmony export */
        "ZW": function ZW() {
          return (
            /* binding */
            GetProfileViewSuccess
          );
        },

        /* harmony export */
        "iu": function iu() {
          return (
            /* binding */
            GetProfileViewFailed
          );
        },

        /* harmony export */
        "Me": function Me() {
          return (
            /* binding */
            GetCompanyDetail
          );
        },

        /* harmony export */
        "fA": function fA() {
          return (
            /* binding */
            ShowExecutiveEmailIdLoader
          );
        },

        /* harmony export */
        "hr": function hr() {
          return (
            /* binding */
            ShowExecutiveEmailIdLoaderClose
          );
        },

        /* harmony export */
        "kQ": function kQ() {
          return (
            /* binding */
            ShowMessage
          );
        },

        /* harmony export */
        "Hk": function Hk() {
          return (
            /* binding */
            ShowDownloadConnectionButton
          );
        },

        /* harmony export */
        "MV": function MV() {
          return (
            /* binding */
            AddExecutive
          );
        },

        /* harmony export */
        "JN": function JN() {
          return (
            /* binding */
            RemoveExecutive
          );
        },

        /* harmony export */
        "qP": function qP() {
          return (
            /* binding */
            ClearExecutives
          );
        },

        /* harmony export */
        "i_": function i_() {
          return (
            /* binding */
            FetchEmailExecutive
          );
        },

        /* harmony export */
        "uy": function uy() {
          return (
            /* binding */
            FetchPhoneExecutive
          );
        },

        /* harmony export */
        "rs": function rs() {
          return (
            /* binding */
            ExecutiveChecked
          );
        },

        /* harmony export */
        "_N": function _N() {
          return (
            /* binding */
            GetExecutivesFromCompany
          );
        },

        /* harmony export */
        "pT": function pT() {
          return (
            /* binding */
            UpdateExecutivesSource
          );
        },

        /* harmony export */
        "dc": function dc() {
          return (
            /* binding */
            ExecutiveCheckBox
          );
        },

        /* harmony export */
        "Vt": function Vt() {
          return (
            /* binding */
            FetchLogo
          );
        }
        /* harmony export */

      });
      /* unused harmony exports GetAlreadyAdded, GetCompanyDetailSuccess, GetCompanyDetailFailed, GetPaginationaData, GETLatestSelectedExecutives */


      var StartCollectingData = /*#__PURE__*/_createClass(function StartCollectingData(isCollecting) {
        _classCallCheck(this, StartCollectingData);

        this.isCollecting = isCollecting;
      });

      StartCollectingData.type = "[PopUpAction] StartCollectingData";

      var ShowExecutiveList = /*#__PURE__*/_createClass(function ShowExecutiveList(executives, fromPage) {
        _classCallCheck(this, ShowExecutiveList);

        this.executives = executives;
        this.fromPage = fromPage;
      });

      ShowExecutiveList.type = "[PopUpAction] ShowExecutiveList";

      var ShowExecutiveListInBulk = /*#__PURE__*/_createClass(function ShowExecutiveListInBulk(executives, fromPage) {
        _classCallCheck(this, ShowExecutiveListInBulk);

        this.executives = executives;
        this.fromPage = fromPage;
      });

      ShowExecutiveListInBulk.type = "[PopUpAction] ShowExecutiveListInBulk";

      var CurrentPageUrl = /*#__PURE__*/_createClass(function CurrentPageUrl(url) {
        _classCallCheck(this, CurrentPageUrl);

        this.url = url;
      });

      CurrentPageUrl.type = "[PopUpAction] CurrentPageUrl";

      var GetAlreadyAdded = /*#__PURE__*/_createClass(function GetAlreadyAdded(executiveIdList) {
        _classCallCheck(this, GetAlreadyAdded);

        this.executiveIdList = executiveIdList;
      });

      GetAlreadyAdded.type = "[PopUpAction] GetAlreadyAdded";

      var ShowExecutiveEmailId = /*#__PURE__*/_createClass(function ShowExecutiveEmailId(emailRequest) {
        _classCallCheck(this, ShowExecutiveEmailId);

        this.emailRequest = emailRequest;
      });

      ShowExecutiveEmailId.type = "[PopUpAction] ShowExecutiveEmailId";

      var ShowExecutiveEmailIdSuccess = /*#__PURE__*/_createClass(function ShowExecutiveEmailIdSuccess(emailResponse) {
        _classCallCheck(this, ShowExecutiveEmailIdSuccess);

        this.emailResponse = emailResponse;
      });

      ShowExecutiveEmailIdSuccess.type = "[PopUpAction] ShowExecutiveEmailIdSuccess";

      var ShowExecutiveEmailIdFailed = /*#__PURE__*/_createClass(function ShowExecutiveEmailIdFailed(emailResponse) {
        _classCallCheck(this, ShowExecutiveEmailIdFailed);

        this.emailResponse = emailResponse;
      });

      ShowExecutiveEmailIdFailed.type = "[PopUpAction] ShowExecutiveEmailIdFailed";

      var ResetExecutiveList = /*#__PURE__*/_createClass(function ResetExecutiveList() {
        _classCallCheck(this, ResetExecutiveList);
      });

      ResetExecutiveList.type = "[PopUpAction] ResetExecutiveList";

      var UpdateExecutiveList = /*#__PURE__*/_createClass(function UpdateExecutiveList() {
        _classCallCheck(this, UpdateExecutiveList);
      });

      UpdateExecutiveList.type = "[PopUpAction] UpdateExecutiveList";

      var ResetDailyLimit = /*#__PURE__*/_createClass(function ResetDailyLimit(from) {
        _classCallCheck(this, ResetDailyLimit);

        this.from = from;
      });

      ResetDailyLimit.type = "[PopUpAction] ResetDailyLimit";

      var ResetDailyLimitSuccess = /*#__PURE__*/_createClass(function ResetDailyLimitSuccess(response) {
        _classCallCheck(this, ResetDailyLimitSuccess);

        this.response = response;
      });

      ResetDailyLimitSuccess.type = "[PopUpAction] ResetDailyLimitSuccess";

      var ResetDailyLimitFailed = /*#__PURE__*/_createClass(function ResetDailyLimitFailed(response) {
        _classCallCheck(this, ResetDailyLimitFailed);

        this.response = response;
      });

      ResetDailyLimitFailed.type = "[PopUpAction] ResetDailyLimitFailed";

      var ShowLinkedSalesNavigator = /*#__PURE__*/_createClass(function ShowLinkedSalesNavigator(isShown) {
        _classCallCheck(this, ShowLinkedSalesNavigator);

        this.isShown = isShown;
      });

      ShowLinkedSalesNavigator.type = "[PopUpAction] ShowLinkedSalesNavigator";

      var ShowLinkedSearchPage = /*#__PURE__*/_createClass(function ShowLinkedSearchPage(isShown) {
        _classCallCheck(this, ShowLinkedSearchPage);

        this.isShown = isShown;
      });

      ShowLinkedSearchPage.type = "[PopUpAction] ShowLinkedSearchPage";

      var ShowLinkedPeoplePage = /*#__PURE__*/_createClass(function ShowLinkedPeoplePage(isShown) {
        _classCallCheck(this, ShowLinkedPeoplePage);

        this.isShown = isShown;
      });

      ShowLinkedPeoplePage.type = "[PopUpAction] ShowLinkedPeoplePage";

      var GetExecutiveList = /*#__PURE__*/_createClass(function GetExecutiveList(executiveIdList, isFilterByPhone, isFilterByEmail, isMissingInfoRequested, companyName) {
        _classCallCheck(this, GetExecutiveList);

        this.executiveIdList = executiveIdList;
        this.isFilterByPhone = isFilterByPhone;
        this.isFilterByEmail = isFilterByEmail;
        this.isMissingInfoRequested = isMissingInfoRequested;
        this.companyName = companyName;
      });

      GetExecutiveList.type = "[PopUpAction] GetExecutiveList";

      var StoreExecutiveResponse = /*#__PURE__*/_createClass(function StoreExecutiveResponse(response) {
        _classCallCheck(this, StoreExecutiveResponse);

        this.response = response;
      });

      StoreExecutiveResponse.type = "[PopUpAction] StoreExecutiveResponse";

      var GetProfileView = /*#__PURE__*/_createClass(function GetProfileView(userID, salesResponse, companyProfileCode, executive, csrfToken, filters) {
        _classCallCheck(this, GetProfileView);

        this.userID = userID;
        this.salesResponse = salesResponse;
        this.companyProfileCode = companyProfileCode;
        this.executive = executive;
        this.csrfToken = csrfToken;
        this.filters = filters;
      });

      GetProfileView.type = "[PopUpAction] GetProfileView";

      var GetProfileViewSuccess = /*#__PURE__*/_createClass(function GetProfileViewSuccess(response) {
        _classCallCheck(this, GetProfileViewSuccess);

        this.response = response;
      });

      GetProfileViewSuccess.type = "[PopUpAction] GetProfileViewSuccess";

      var GetProfileViewFailed = /*#__PURE__*/_createClass(function GetProfileViewFailed() {
        _classCallCheck(this, GetProfileViewFailed);
      });

      GetProfileViewFailed.type = "[PopUpAction] GetProfileViewFailed";

      var GetCompanyDetail = /*#__PURE__*/_createClass(function GetCompanyDetail(universalName, executiveData, executive, contactInfo, salesProfileResponse, filters, exeutiveSkill, csrfToken) {
        _classCallCheck(this, GetCompanyDetail);

        this.universalName = universalName;
        this.executiveData = executiveData;
        this.executive = executive;
        this.contactInfo = contactInfo;
        this.salesProfileResponse = salesProfileResponse;
        this.filters = filters;
        this.exeutiveSkill = exeutiveSkill;
        this.csrfToken = csrfToken;
      });

      GetCompanyDetail.type = "[PopUpAction] GetCompanyDetail";

      var GetCompanyDetailSuccess = /*#__PURE__*/_createClass(function GetCompanyDetailSuccess(res) {
        _classCallCheck(this, GetCompanyDetailSuccess);

        this.res = res;
      });

      GetCompanyDetailSuccess.type = "[PopUpAction] GetCompanyDetailSuccess";

      var GetCompanyDetailFailed = /*#__PURE__*/_createClass(function GetCompanyDetailFailed() {
        _classCallCheck(this, GetCompanyDetailFailed);
      });

      GetCompanyDetailFailed.type = "[PopUpAction] GetCompanyDetailFailed";

      var ShowExecutiveEmailIdLoader = /*#__PURE__*/_createClass(function ShowExecutiveEmailIdLoader(executiveId) {
        _classCallCheck(this, ShowExecutiveEmailIdLoader);

        this.executiveId = executiveId;
      });

      ShowExecutiveEmailIdLoader.type = "[PopUpAction] ShowExecutiveEmailIdLoader";

      var ShowExecutiveEmailIdLoaderClose = /*#__PURE__*/_createClass(function ShowExecutiveEmailIdLoaderClose(executiveId, message) {
        _classCallCheck(this, ShowExecutiveEmailIdLoaderClose);

        this.executiveId = executiveId;
        this.message = message;
      });

      ShowExecutiveEmailIdLoaderClose.type = "[PopUpAction] ShowExecutiveEmailIdLoaderClose";

      var ShowMessage = /*#__PURE__*/_createClass(function ShowMessage(error) {
        _classCallCheck(this, ShowMessage);

        this.error = error;
      });

      ShowMessage.type = "[PopUpAction] ShowMessage";

      var ShowDownloadConnectionButton = /*#__PURE__*/_createClass(function ShowDownloadConnectionButton(isShown) {
        _classCallCheck(this, ShowDownloadConnectionButton);

        this.isShown = isShown;
      });

      ShowDownloadConnectionButton.type = "[PopUpAction] ShowDownloadConnectionButton";

      var GetPaginationaData = /*#__PURE__*/_createClass(function GetPaginationaData(count, refererUrl, pageNo, keyword) {
        _classCallCheck(this, GetPaginationaData);

        this.count = count;
        this.refererUrl = refererUrl;
        this.pageNo = pageNo;
        this.keyword = keyword;
      });

      GetPaginationaData.type = "[PopUpAction] GetPaginationaData";

      var AddExecutive = /*#__PURE__*/_createClass(function AddExecutive(payload) {
        _classCallCheck(this, AddExecutive);

        this.payload = payload;
      });

      AddExecutive.type = "[Executive] Add";

      var RemoveExecutive = /*#__PURE__*/_createClass(function RemoveExecutive(payload) {
        _classCallCheck(this, RemoveExecutive);

        this.payload = payload;
      });

      RemoveExecutive.type = "[Executive] Remove";

      var ClearExecutives = /*#__PURE__*/_createClass(function ClearExecutives() {
        _classCallCheck(this, ClearExecutives);
      });

      ClearExecutives.type = "[Executive] Clear";

      var FetchEmailExecutive = /*#__PURE__*/_createClass(function FetchEmailExecutive(payload) {
        _classCallCheck(this, FetchEmailExecutive);

        this.payload = payload;
      });

      FetchEmailExecutive.type = "[PopUpAction] Fetch Email";

      var FetchPhoneExecutive = /*#__PURE__*/_createClass(function FetchPhoneExecutive(payload) {
        _classCallCheck(this, FetchPhoneExecutive);

        this.payload = payload;
      });

      FetchPhoneExecutive.type = "[PopUpAction] Fetch Phone";

      var ExecutiveChecked = /*#__PURE__*/_createClass(function ExecutiveChecked(payload, checked) {
        _classCallCheck(this, ExecutiveChecked);

        this.payload = payload;
        this.checked = checked;
      });

      ExecutiveChecked.type = "[Executive] ExecutiveChecked";

      var GetExecutivesFromCompany = /*#__PURE__*/_createClass(function GetExecutivesFromCompany(payload) {
        _classCallCheck(this, GetExecutivesFromCompany);

        this.payload = payload;
      });

      GetExecutivesFromCompany.type = "[PopUpAction] GetExecutivesFromCompany";

      var GETLatestSelectedExecutives = /*#__PURE__*/_createClass(function GETLatestSelectedExecutives(paylaod) {
        _classCallCheck(this, GETLatestSelectedExecutives);

        this.paylaod = paylaod;
      });

      GETLatestSelectedExecutives.type = "[PopUpAction] GETLatestSelectedExecutives";

      var UpdateExecutivesSource = /*#__PURE__*/_createClass(function UpdateExecutivesSource(paylaod) {
        _classCallCheck(this, UpdateExecutivesSource);

        this.paylaod = paylaod;
      });

      UpdateExecutivesSource.type = "[PopUpAction] UpdateExecutivesSource";

      var ExecutiveCheckBox = /*#__PURE__*/_createClass(function ExecutiveCheckBox(payload) {
        _classCallCheck(this, ExecutiveCheckBox);

        this.payload = payload;
      });

      ExecutiveCheckBox.type = "[PopUpAction] ExecutiveCheckBox";

      var FetchLogo = /*#__PURE__*/_createClass(function FetchLogo(payload) {
        _classCallCheck(this, FetchLogo);

        this.payload = payload;
      });

      FetchLogo.type = "[PopUpAction] Fetch Logo";
      /***/
    },

    /***/
    6549:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "J": function J() {
          return (
            /* binding */
            CompanyService
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_common_http__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(3882);
      /* harmony import */


      var _ngxs_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6090);
      /* harmony import */


      var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8117);
      /* harmony import */


      var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1134);
      /* harmony import */


      var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(5871);
      /* harmony import */


      var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(4921);
      /* harmony import */


      var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(8293);
      /* harmony import */


      var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(3927);
      /* harmony import */


      var _login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8341);
      /* harmony import */


      var _action_company_action__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8233);
      /* harmony import */


      var src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5502);
      /* harmony import */


      var _action_popup_action__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1950);
      /* harmony import */


      var _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6136);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(2316);

      var CompanyService = /*#__PURE__*/function () {
        function CompanyService(http, store) {
          _classCallCheck(this, CompanyService);

          this.http = http;
          this.store = store;
        }

        return _createClass(CompanyService, [{
          key: "getCompanyDetails",
          value: function getCompanyDetails(companySourceId, website, companyName) {
            var _this = this;

            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var requestBody = {
              companySourceId: companySourceId // website: website,
              // companyName: companyName,

            };
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_COMPANY_DETAILS_API */
            .xK, requestBody, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data, _result$data$data;

              var companyId = result === null || result === void 0 ? void 0 : (_result$data = result.data) === null || _result$data === void 0 ? void 0 : (_result$data$data = _result$data.data) === null || _result$data$data === void 0 ? void 0 : _result$data$data.companyId;

              if (companyId) {
                _this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(companyId));
              } else {
                _this.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(null));
              }
            }));
          }
        }, {
          key: "getExecutiveFilterOptions",
          value: function getExecutiveFilterOptions() {
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken)) {
              return;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            return this.http.get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_DROPDOWN_DATA */
            .Z9, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (response) {}));
          }
        }, {
          key: "extractCompanyDetails",
          value: function extractCompanyDetails(request) {
            var _this2 = this;

            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var requestBody = {
              website: request === null || request === void 0 ? void 0 : request.website,
              companyName: request === null || request === void 0 ? void 0 : request.companyName
            };
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_COMPANY_DETAILS_API */
            .xK, requestBody, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data2, _result$data2$data, _request$data, _request$data$data;

              var companyId = result === null || result === void 0 ? void 0 : (_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : (_result$data2$data = _result$data2.data) === null || _result$data2$data === void 0 ? void 0 : _result$data2$data.companyId;
              var websiteLogo = request === null || request === void 0 ? void 0 : (_request$data = request.data) === null || _request$data === void 0 ? void 0 : (_request$data$data = _request$data.data) === null || _request$data$data === void 0 ? void 0 : _request$data$data.website;

              if (companyId) {
                _this2.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(companyId));

                _this2.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .FetchLogo */
                .Vt(websiteLogo));
              } else {
                _this2.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(null));
              }
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }));
          }
        }, {
          key: "findEmailOrPhone",
          value: function findEmailOrPhone(payload) {
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken),
              "Content-Type": "application/json"
            });
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_FIND_PHONE_EMAIL */
            ._H, payload, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (response) {}));
          }
        }, {
          key: "fetchLogo",
          value: function fetchLogo(website) {
            if (!website) {
              return (0, rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
              /* .DEFAULT_COMPANY_LOGO */
              .Nk);
            }

            var logoUrl = "".concat(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .COMPANY_LOGO_URL */
            ._8).concat(website);
            return this.http.get(logoUrl, {
              responseType: "blob"
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_11__
            /* .map */
            .U)(function () {
              return logoUrl;
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              // If the status is 404, return the default logo
              if (error.status === 404) {
                return (0, rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
                /* .DEFAULT_COMPANY_LOGO */
                .Nk);
              } // Handle other errors or rethrow them


              return (0, rxjs__WEBPACK_IMPORTED_MODULE_10__.of)(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
              /* .DEFAULT_COMPANY_LOGO */
              .Nk);
            }));
          }
        }, {
          key: "getCompanyExecutives",
          value: function getCompanyExecutives(companyId, departmentId, executiveLevelId) {
            var _this3 = this;

            var searchTerm = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var url = "".concat(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_COMPANY_KEYEMP */
            .oP, "/").concat(companyId, "/").concat(departmentId, "/").concat(executiveLevelId, "?searchTerm=").concat(encodeURIComponent(searchTerm));
            return this.http.get(url, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data3;

              var executives = (result === null || result === void 0 ? void 0 : (_result$data3 = result.data) === null || _result$data3 === void 0 ? void 0 : _result$data3.emailPhoneResponses) || [];

              _this3.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
              /* .SetCompanyExecutives */
              .jQ(executives));
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              _this3.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
              /* .SetCompanyExecutives */
              .jQ([])); // Clear the list on error


              return (0, rxjs__WEBPACK_IMPORTED_MODULE_12__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "getCompanyExecutivesLeyEmp",
          value: function getCompanyExecutivesLeyEmp(companyId, departmentId, executiveLevelId) {
            var _this4 = this;

            var searchTerm = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            }); // Append the searchTerm to the URL as a query parameter

            var url = "".concat(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_COMPANY_KEYEMP */
            .oP, "/").concat(companyId, "/").concat(departmentId, "/").concat(executiveLevelId, "?searchTerm=").concat(encodeURIComponent(searchTerm));
            return this.http.get(url, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data4;

              var executives = (result === null || result === void 0 ? void 0 : (_result$data4 = result.data) === null || _result$data4 === void 0 ? void 0 : _result$data4.emailPhoneResponses) || [];

              _this4.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .SetExtractCompanyExecutives */
              .yX(executives));
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              _this4.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .SetExtractCompanyExecutives */
              .yX([])); // Clear the list on error


              return (0, rxjs__WEBPACK_IMPORTED_MODULE_12__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "getBackToYouAPI",
          value: function getBackToYouAPI(request) {
            var _this5 = this;

            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var requestBody = {
              firstName: request === null || request === void 0 ? void 0 : request.firstName,
              lastName: request === null || request === void 0 ? void 0 : request.lastName,
              designation: request === null || request === void 0 ? void 0 : request.designation,
              linkedInId: request === null || request === void 0 ? void 0 : request.linkedInId,
              companyLinkedInId: request === null || request === void 0 ? void 0 : request.companyLinkedInId,
              companyName: request === null || request === void 0 ? void 0 : request.companyName
            };
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_BACK_TO_YOU */
            .gO, requestBody, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data5, _result$data5$data;

              var companyId = result === null || result === void 0 ? void 0 : (_result$data5 = result.data) === null || _result$data5 === void 0 ? void 0 : (_result$data5$data = _result$data5.data) === null || _result$data5$data === void 0 ? void 0 : _result$data5$data.companyId;

              if (companyId) {
                _this5.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(companyId));
              } else {}
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }));
          }
        }, {
          key: "fetchExecutiveListOptions",
          value: function fetchExecutiveListOptions() {
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            return this.http.get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_EXECUTIVE_LIST_OPTIONS */
            .Zf, {
              headers: httpHeaders
            });
          }
        }, {
          key: "getSavedEvecutiveList",
          value: function getSavedEvecutiveList() {
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            return this.http.get(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_SAVED_EXECUTIVE_LIST */
            .vz, {
              headers: httpHeaders
            });
          }
        }, {
          key: "searchListName",
          value: function searchListName(request) {
            var _this6 = this;

            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var url = "".concat(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_SEARCH_RESULT_API */
            .rc).concat(request === null || request === void 0 ? void 0 : request.name);
            return this.http.get(url, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data6, _result$data6$data;

              var companyId = result === null || result === void 0 ? void 0 : (_result$data6 = result.data) === null || _result$data6 === void 0 ? void 0 : (_result$data6$data = _result$data6.data) === null || _result$data6$data === void 0 ? void 0 : _result$data6$data.companyId;

              if (companyId) {
                _this6.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(companyId));
              } else {}
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }));
          }
        }, {
          key: "createExecutiveList",
          value: function createExecutiveList(request) {
            var _this7 = this;

            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            var requestBody = {
              myContacts: request === null || request === void 0 ? void 0 : request.myContacts,
              listName: request === null || request === void 0 ? void 0 : request.listName,
              listId: request === null || request === void 0 ? void 0 : request.listId,
              isListExist: request === null || request === void 0 ? void 0 : request.isListExist,
              campaignList: request === null || request === void 0 ? void 0 : request.campaignList,
              isBulkView: request === null || request === void 0 ? void 0 : request.isBulkView
            };
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .CREATE_EXECUTIVE_LIST */
            .U$, requestBody, {
              headers: httpHeaders
            }).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_8__
            /* .tap */
            .b)(function (result) {
              var _result$data7, _result$data7$data;

              var companyId = result === null || result === void 0 ? void 0 : (_result$data7 = result.data) === null || _result$data7 === void 0 ? void 0 : (_result$data7$data = _result$data7.data) === null || _result$data7$data === void 0 ? void 0 : _result$data7$data.companyId;

              if (companyId) {
                _this7.store.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_2__
                /* .SetCompanyId */
                .lQ(companyId));

                _this7.store.dispatch(new _action_popup_action__WEBPACK_IMPORTED_MODULE_4__
                /* .ClearExecutives */
                .qP());
              } else {}
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_9__
            /* .catchError */
            .K)(function (error) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }));
          }
        }, {
          key: "getProfileDataLimit",
          value: function getProfileDataLimit() {
            var authData = this.store.selectSnapshot(_login_store_state_login_state__WEBPACK_IMPORTED_MODULE_1__
            /* .ScLoginState.getLoginUserDetails */
            .U.getLoginUserDetails);

            if (!(authData !== null && authData !== void 0 && authData.accessToken) || !(authData !== null && authData !== void 0 && authData.dsmID)) {
              return rxjs__WEBPACK_IMPORTED_MODULE_6__
              /* .EMPTY */
              .E;
            }

            var httpHeaders = new _angular_common_http__WEBPACK_IMPORTED_MODULE_7__
            /* .HttpHeaders */
            .WM({
              dsmID: authData.dsmID,
              Authorization: "Bearer ".concat(authData.accessToken)
            });
            return this.http.post(src_app_constant_api_url__WEBPACK_IMPORTED_MODULE_3__
            /* .GET_PROFILE_DATA_LIMIT */
            .E3, null, {
              headers: httpHeaders
            });
          }
        }]);
      }();

      CompanyService.ɵfac = function CompanyService_Factory(t) {
        return new (t || CompanyService)(_angular_core__WEBPACK_IMPORTED_MODULE_13__
        /* ["ɵɵinject"] */
        .LFG(_angular_common_http__WEBPACK_IMPORTED_MODULE_7__
        /* .HttpClient */
        .eN), _angular_core__WEBPACK_IMPORTED_MODULE_13__
        /* ["ɵɵinject"] */
        .LFG(_ngxs_store__WEBPACK_IMPORTED_MODULE_0__
        /* .Store */
        .yh));
      };

      CompanyService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_13__
      /* ["ɵɵdefineInjectable"] */
      .Yz7({
        token: CompanyService,
        factory: CompanyService.ɵfac,
        providedIn: "root"
      });
      /***/
    },

    /***/
    1333:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "L": function L() {
          return (
            /* binding */
            ExtractCompanyState
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var tslib__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(2321);
      /* harmony import */


      var _ngxs_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6090);
      /* harmony import */


      var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4921);
      /* harmony import */


      var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8293);
      /* harmony import */


      var _service_company_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6549);
      /* harmony import */


      var _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6136);
      /* harmony import */


      var rxjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5871);
      /* harmony import */


      var src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6718);
      /* harmony import */


      var src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9818);
      /* harmony import */


      var _action_company_action__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(8233);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2316);

      var ExtractCompanyState = /*#__PURE__*/function () {
        function ExtractCompanyState(companyService, store, snackbarService) {
          _classCallCheck(this, ExtractCompanyState);

          this.companyService = companyService;
          this.store = store;
          this.snackbarService = snackbarService;
        }

        return _createClass(ExtractCompanyState, [{
          key: "CallAPI",
          value: function CallAPI(ctx, action) {
            ctx.patchState({
              extractedUrl: null
            });
          }
        }, {
          key: "getCompanyKeyEmp",
          value: function getCompanyKeyEmp(ctx, action) {
            ctx.patchState({
              loading: true,
              extractEmPLoading: true
            });
            var state = ctx.getState();
            return this.companyService.getCompanyExecutivesLeyEmp(action.companyId, action.departmentId, action.executiveLevelId, action.searchTerm).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_6__
            /* .tap */
            .b)(function (response) {
              var executives = response.data.emailPhoneResponses || [];

              if (executives.length === 0) {
                ctx.patchState({
                  extractKeyEmp: [],
                  loading: false,
                  extractEmPLoading: false
                });
              } else {
                ctx.patchState({
                  extractKeyEmp: executives,
                  loading: false,
                  extractEmPLoading: false
                });
              }
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_7__
            /* .catchError */
            .K)(function (error) {
              ctx.patchState({
                extractKeyEmp: [],
                loading: false,
                extractEmPLoading: false
              });
              return (0, rxjs__WEBPACK_IMPORTED_MODULE_8__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "setCompanyExecutives",
          value: function setCompanyExecutives(ctx, action) {
            ctx.patchState({
              extractKeyEmp: action.executives
            });
          }
        }, {
          key: "extractCompanyDetails",
          value: function extractCompanyDetails(ctx, action) {
            var _this8 = this;

            var state = ctx.getState();
            var newUrl = action.request.website;

            if (state.extractedUrl === newUrl) {
              return;
            }

            ctx.patchState({
              loading: true,
              extractedUrl: newUrl
            });
            return this.companyService.extractCompanyDetails(action.request).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_6__
            /* .tap */
            .b)(function (result) {
              var _result$data8, _result$data9, _result$data9$data, _result$data0, _result$data0$data;

              ctx.patchState({
                extractedCompanyDetails: (result === null || result === void 0 ? void 0 : (_result$data8 = result.data) === null || _result$data8 === void 0 ? void 0 : _result$data8.data) || null // Adjust based on your API response

              });
              var companyid = result === null || result === void 0 ? void 0 : (_result$data9 = result.data) === null || _result$data9 === void 0 ? void 0 : (_result$data9$data = _result$data9.data) === null || _result$data9$data === void 0 ? void 0 : _result$data9$data.companyId;

              _this8.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
              /* .GetCompanyKeyEmpInExtractAny */
              .b6(companyid, action.request.departmentId || 0, action.request.executiveLevelId || 0, "")); // this.store.dispatch(new FetchLogo(result?.data?.data?.website));


              ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .ClearOldUrl */
              .GE("clearold "));
              ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .searchFilters */
              .LS(null));
              ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .departmentFilters */
              .cz(null));
              ctx.dispatch(new _action_company_action__WEBPACK_IMPORTED_MODULE_5__
              /* .seniorityFilters */
              .PR(null)); // this.store.dispatch(new searchFilters(this.searchTerm));

              if ((result === null || result === void 0 ? void 0 : result.message) === "company details not found!") {
                ctx.patchState({
                  extractKeyEmp: [],
                  logoUrl: null,
                  loading: false
                });
              }

              _this8.store.dispatch(new _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
              /* .FetchLogo */
              .Vt(result === null || result === void 0 ? void 0 : (_result$data0 = result.data) === null || _result$data0 === void 0 ? void 0 : (_result$data0$data = _result$data0.data) === null || _result$data0$data === void 0 ? void 0 : _result$data0$data.website));
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_7__
            /* .catchError */
            .K)(function (error) {
              ctx.patchState({
                extractedCompanyDetails: null,
                logoUrl: null,
                loading: false
              });
              return (0, rxjs__WEBPACK_IMPORTED_MODULE_8__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "fetchEmail",
          value: function fetchEmail(ctx, action) {
            var _this9 = this;

            var payload = _objectSpread(_objectSpread({}, action.payload), {}, {
              sourceName: "LINKEDIN",
              staffCount: 0,
              source: Array.isArray(action.payload.source) ? action.payload.source : [action.payload.source]
            });

            ctx.patchState({
              isFetchingEmail: true
            });
            return this.companyService.findEmailOrPhone(payload).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_6__
            /* .tap */
            .b)(function (response) {
              if (response.message === "Email Limit is exhausted") {
                _this9.snackbarService.openSnackBar(response.message, src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__
                /* .SNACKBAR_TIME.THREE_SECOND */
                ._Q.THREE_SECOND, response);
              }

              var extractcompanyKeyEmp = _toConsumableArray(ctx.getState().extractKeyEmp);

              var updatedextractCompanyKeyEmp = extractcompanyKeyEmp.map(function (emp) {
                var _response$data;

                if (emp.sourceId === (response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.sourceId)) {
                  var _response$data2, _response$data3;

                  return _objectSpread(_objectSpread({}, emp), {}, {
                    email: response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.email,
                    isFetchingEmail: false,
                    error: (response === null || response === void 0 ? void 0 : (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.email) === "Not available" ? true : false // error: !!response.error

                  });
                } else {
                  var _response$data4;

                  return _objectSpread(_objectSpread({}, emp), {}, {
                    isFetchingEmail: false,
                    // error: !!response.error
                    error: (response === null || response === void 0 ? void 0 : (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.email) === "Not available" ? true : false
                  });
                }
              });
              ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingEmail: false
              });
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_7__
            /* .catchError */
            .K)(function (error) {
              var extractKeyEmp = ctx.getState().extractKeyEmp.length > 0 ? _toConsumableArray(ctx.getState().extractKeyEmp) : [];
              var updatedextractCompanyKeyEmp = extractKeyEmp.map(function (emp) {
                if (emp.executiveId === action.payload.sourceId) {
                  return _objectSpread({}, emp);
                }

                return emp;
              });
              ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingEmail: false
              });
              return (0, rxjs__WEBPACK_IMPORTED_MODULE_8__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "fetchPhone",
          value: function fetchPhone(ctx, action) {
            var _this0 = this;

            var payload = _objectSpread(_objectSpread({}, action.payload), {}, {
              sourceName: "LINKEDIN",
              staffCount: 0,
              source: Array.isArray(action.payload.source) ? action.payload.source : [action.payload.source]
            }); // Start fetching state update


            ctx.patchState({
              isFetchingPhone: true
            });
            return this.companyService.findEmailOrPhone(payload).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_6__
            /* .tap */
            .b)(function (response) {
              var extractcompanyKeyEmp = _toConsumableArray(ctx.getState().extractKeyEmp);

              if (response.message === "Phone Limit is exhausted") {
                _this0.snackbarService.openSnackBar(response.message, src_app_constant_value__WEBPACK_IMPORTED_MODULE_4__
                /* .SNACKBAR_TIME.THREE_SECOND */
                ._Q.THREE_SECOND, response);
              } // Update state with the fetched phone data


              var updatedextractCompanyKeyEmp = extractcompanyKeyEmp.map(function (emp) {
                if (emp.sourceId === action.payload.sourceId) {
                  return _objectSpread(_objectSpread({}, emp), {}, {
                    mobileNumber: response.data.mobileNumber,
                    isFetchingPhone: false // Stop fetching
                    // error: !!response.error, // Set error flag if needed

                  });
                } else {
                  return _objectSpread(_objectSpread({}, emp), {}, {
                    // phone: response.phone || emp.phone, // Update with fetched phone
                    isFetchingPhone: false // Stop fetching
                    // error: !!response.error, // Set error flag if needed

                  });
                }
              });
              ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingPhone: false
              });
            }), (0, rxjs_operators__WEBPACK_IMPORTED_MODULE_7__
            /* .catchError */
            .K)(function (error) {
              // Handle error and stop loading state
              var updatedextractCompanyKeyEmp = ctx.getState().extractKeyEmp.map(function (emp) {
                if (emp.executiveId === action.payload.sourceId) {
                  return _objectSpread(_objectSpread({}, emp), {}, {
                    isFetchingPhone: false,
                    error: true // Mark error state

                  });
                }

                return emp;
              });
              ctx.patchState({
                extractKeyEmp: updatedextractCompanyKeyEmp,
                isFetchingPhone: false
              });
              return (0, rxjs__WEBPACK_IMPORTED_MODULE_8__
              /* .throwError */
              ._)(error);
            }));
          }
        }, {
          key: "fetchLogo",
          value: function fetchLogo(ctx, action) {
            ctx.patchState({
              loading: true
            });
            return this.companyService.fetchLogo(action.payload).pipe((0, rxjs_operators__WEBPACK_IMPORTED_MODULE_6__
            /* .tap */
            .b)(function (logoUrl) {
              ctx.patchState({
                logoUrl: logoUrl,
                loading: false
              });
            }));
          }
        }], [{
          key: "getExtractedCompanyDetails",
          value: function getExtractedCompanyDetails(state) {
            return state.extractedCompanyDetails;
          }
        }, {
          key: "getExtractCompanyKeyemp",
          value: function getExtractCompanyKeyemp(state) {
            return state.extractKeyEmp;
          }
        }, {
          key: "getLogoUrl",
          value: function getLogoUrl(state) {
            return state.logoUrl;
          }
        }, {
          key: "isLoading",
          value: function isLoading(state) {
            return state.loading;
          }
        }, {
          key: "extractEmPLoading",
          value: function extractEmPLoading(state) {
            return state.extractEmPLoading;
          }
        }]);
      }();

      ExtractCompanyState.ɵfac = function ExtractCompanyState_Factory(t) {
        return new (t || ExtractCompanyState)(_angular_core__WEBPACK_IMPORTED_MODULE_9__
        /* ["ɵɵinject"] */
        .LFG(_service_company_service__WEBPACK_IMPORTED_MODULE_1__
        /* .CompanyService */
        .J), _angular_core__WEBPACK_IMPORTED_MODULE_9__
        /* ["ɵɵinject"] */
        .LFG(_ngxs_store__WEBPACK_IMPORTED_MODULE_0__
        /* .Store */
        .yh), _angular_core__WEBPACK_IMPORTED_MODULE_9__
        /* ["ɵɵinject"] */
        .LFG(src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__
        /* .SnackbarService */
        .o));
      };

      ExtractCompanyState.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__
      /* ["ɵɵdefineInjectable"] */
      .Yz7({
        token: ExtractCompanyState,
        factory: ExtractCompanyState.ɵfac
      });
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .CallAPI */
      .Sf), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .CallAPI */
      .Sf]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "CallAPI", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .GetCompanyKeyEmpInExtractAny */
      .b6), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .GetCompanyKeyEmpInExtractAny */
      .b6]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "getCompanyKeyEmp", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .SetExtractCompanyExecutives */
      .yX), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .SetExtractCompanyExecutives */
      .yX]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "setCompanyExecutives", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .ExtractCompanyDetails */
      .LE), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .ExtractCompanyDetails */
      .LE]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "extractCompanyDetails", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchEmail */
      .ct), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchEmail */
      .ct]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "fetchEmail", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchPhone */
      .Vx), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchPhone */
      .Vx]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "fetchPhone", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Action */
      .aU)(_action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchLogo */
      .Vt), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object, _action_extract_company_action__WEBPACK_IMPORTED_MODULE_2__
      /* .FetchLogo */
      .Vt]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState.prototype, "fetchLogo", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Selector */
      .Qf)(), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState, "getExtractedCompanyDetails", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Selector */
      .Qf)(), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", void 0)], ExtractCompanyState, "getExtractCompanyKeyemp", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Selector */
      .Qf)(), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", String)], ExtractCompanyState, "getLogoUrl", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Selector */
      .Qf)(), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", Boolean)], ExtractCompanyState, "isLoading", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Selector */
      .Qf)(), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:type", Function), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [Object]), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:returntype", Boolean)], ExtractCompanyState, "extractEmPLoading", null);
      ExtractCompanyState = (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__decorate */
      .gn)([(0, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .State */
      .ZM)({
        name: "extractCompany",
        defaults: {
          extractedCompanyDetails: null,
          extractKeyEmp: [],
          companyKeyEmp: null,
          loading: false,
          logoUrl: null,
          extractedUrl: null,
          extractEmPLoading: false
        }
      }), (0, tslib__WEBPACK_IMPORTED_MODULE_10__
      /* .__metadata */
      .w6)("design:paramtypes", [_service_company_service__WEBPACK_IMPORTED_MODULE_1__
      /* .CompanyService */
      .J, _ngxs_store__WEBPACK_IMPORTED_MODULE_0__
      /* .Store */
      .yh, src_app_common_snack_bar_snack_bar_service__WEBPACK_IMPORTED_MODULE_3__
      /* .SnackbarService */
      .o])], ExtractCompanyState);
      /***/
    },

    /***/
    3154:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "$": function $() {
          return (
            /* binding */
            TAB_ID
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2316);
      /**
       * provides the currently opened tab id
       */


      var TAB_ID = new _angular_core__WEBPACK_IMPORTED_MODULE_0__
      /* .InjectionToken */
      .OlP('CHROME_TAB_ID');
      /***/
    },

    /***/
    8135:
    /***/
    function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      "use strict";
      /* harmony export */

      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "N": function N() {
          return (
            /* binding */
            environment
          );
        }
        /* harmony export */

      });

      var environment = {
        production: false,
        devToolsEnabled: false,
        BASE_URL: "https://app.salezshark.com/linkedinextensions/",
        SALEZCONNECT_BASE_API_URL: "https://www.salezshark.com/connect/app/gateway",
        // SALEZCONNECT_BASE_API_URL: "https://qa.salezshark.io/connect/app/gateway",
        APP_NAME: "Saleshark Connect +",
        linkedinClientId: "86i65bsc5clxfq",
        googleClientId: "266877872832-1ibf2cmgb91r2hhm7s9n17tcn31gojb4.apps.googleusercontent.com",
        linkedinSignIn: "https://www.salezshark.com/connect/app/auth/linkedinsignin",
        linkedinSignup: "https://www.salezshark.com/connect/app/auth/linkedinsignup",
        gmailSignIn: "https://www.salezshark.com/connect/app/auth/gmailsignin",
        gmailSignup: "https://www.salezshark.com/connect/app/auth/gmailsignup",
        connectPlusUrl: "https://qa.salezshark.io/wa/app/app",
        DEFULT_LOGO_URL: "https://qa.salezshark.io/wa/app/assets/images/company_default.svg"
      };
      /***/
    },

    /***/
    3182:
    /***/
    function _(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {
      "use strict"; // EXTERNAL MODULE: ./node_modules/@angular/platform-browser/__ivy_ngcc__/fesm2015/platform-browser.js

      var platform_browser = __webpack_require__(1570); // EXTERNAL MODULE: ./node_modules/@angular/core/__ivy_ngcc__/fesm2015/core.js


      var core = __webpack_require__(2316); // EXTERNAL MODULE: ./node_modules/@angular/router/__ivy_ngcc__/fesm2015/router.js + 7 modules


      var router = __webpack_require__(24);

      ; // CONCATENATED MODULE: ./angular/src/app/app-routing.module.ts

      var routes = [{
        path: "popup",
        pathMatch: "full",
        loadChildren: function loadChildren() {
          return Promise.all(
          /* import() */
          [__webpack_require__.e("default-angular_src_app_modules_popup_common_module_ts"), __webpack_require__.e("angular_src_app_modules_popup_popup_module_ts")]).then(__webpack_require__.bind(__webpack_require__, 1224)).then(function (m) {
            return m.PopupModule;
          });
        }
      }, {
        path: "tab",
        pathMatch: "full",
        loadChildren: function loadChildren() {
          return __webpack_require__.e(
          /* import() */
          "angular_src_app_modules_tab_tab_module_ts").then(__webpack_require__.bind(__webpack_require__, 9651)).then(function (m) {
            return m.TabModule;
          });
        }
      }, {
        path: "options",
        pathMatch: "full",
        loadChildren: function loadChildren() {
          return __webpack_require__.e(
          /* import() */
          "angular_src_app_modules_options_options_module_ts").then(__webpack_require__.bind(__webpack_require__, 4157)).then(function (m) {
            return m.OptionsModule;
          });
        }
      }, {
        path: "company",
        pathMatch: "full",
        loadChildren: function loadChildren() {
          return Promise.all(
          /* import() */
          [__webpack_require__.e("default-angular_src_app_modules_popup_common_module_ts"), __webpack_require__.e("angular_src_app_modules_popup_popup1_module_ts")]).then(__webpack_require__.bind(__webpack_require__, 4971)).then(function (m) {
            return m.Popup1Module;
          });
        }
      }];

      var AppRoutingModule = /*#__PURE__*/_createClass(function AppRoutingModule() {
        _classCallCheck(this, AppRoutingModule);
      });

      AppRoutingModule.ɵfac = function AppRoutingModule_Factory(t) {
        return new (t || AppRoutingModule)();
      };

      AppRoutingModule.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: AppRoutingModule
      });
      AppRoutingModule.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        imports: [[router
        /* RouterModule.forRoot */
        .Bz.forRoot(routes, {
          useHash: true
        })], router
        /* RouterModule */
        .Bz]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(AppRoutingModule, {
          imports: [router
          /* RouterModule */
          .Bz],
          exports: [router
          /* RouterModule */
          .Bz]
        });
      })(); // EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs


      var tslib_es6 = __webpack_require__(2321); // EXTERNAL MODULE: ./node_modules/@ngxs/store/fesm2015/ngxs-store.js + 3 modules


      var ngxs_store = __webpack_require__(6090); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/state/login.state.ts + 1 modules


      var login_state = __webpack_require__(8341);

      ; // CONCATENATED MODULE: ./angular/src/app/app.component.ts

      var AppComponent = /*#__PURE__*/_createClass(function AppComponent() {
        _classCallCheck(this, AppComponent);
      });

      AppComponent.ɵfac = function AppComponent_Factory(t) {
        return new (t || AppComponent)();
      };

      AppComponent.ɵcmp = /*@__PURE__*/core
      /* ɵɵdefineComponent */
      .Xpm({
        type: AppComponent,
        selectors: [["app-root"]],
        decls: 1,
        vars: 0,
        template: function AppComponent_Template(rf, ctx) {
          if (rf & 1) {
            core
            /* ɵɵelement */
            ._UZ(0, "router-outlet");
          }
        },
        directives: [router
        /* RouterOutlet */
        .lC],
        styles: [""]
      });
      (0, tslib_es6
      /* __decorate */
      .gn)([(0, ngxs_store
      /* Select */
      .Ph)(login_state
      /* ScLoginState.isLoggedIn */
      .U.isLoggedIn), (0, tslib_es6
      /* __metadata */
      .w6)("design:type", Object)], AppComponent.prototype, "isLoggedIn$", void 0); // EXTERNAL MODULE: ./node_modules/@ngxs/router-plugin/fesm2015/ngxs-router-plugin.js

      var ngxs_router_plugin = __webpack_require__(3942); // EXTERNAL MODULE: ./node_modules/@ngxs/storage-plugin/fesm2015/ngxs-storage-plugin.js


      var ngxs_storage_plugin = __webpack_require__(3552); // EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/http.js


      var http = __webpack_require__(3882); // EXTERNAL MODULE: ./node_modules/@angular/platform-browser/__ivy_ngcc__/fesm2015/animations.js + 1 modules


      var animations = __webpack_require__(1918); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/BehaviorSubject.js


      var BehaviorSubject = __webpack_require__(6491); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/observable/throwError.js


      var throwError = __webpack_require__(5871); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/catchError.js


      var catchError = __webpack_require__(8293); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/switchMap.js


      var switchMap = __webpack_require__(9902); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/filter.js


      var filter = __webpack_require__(9170); // EXTERNAL MODULE: ./node_modules/rxjs/_esm2015/internal/operators/take.js


      var take = __webpack_require__(3466); // EXTERNAL MODULE: ./angular/src/app/constant/api.url.ts


      var api_url = __webpack_require__(5502); // EXTERNAL MODULE: ./angular/src/app/constant/status-code.ts


      var status_code = __webpack_require__(1888); // EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.service.ts


      var snack_bar_service = __webpack_require__(6718); // EXTERNAL MODULE: ./angular/src/app/constant/message.ts


      var message = __webpack_require__(7063); // EXTERNAL MODULE: ./angular/src/app/constant/value.ts


      var constant_value = __webpack_require__(9818); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/action/login.action.ts


      var login_action = __webpack_require__(9198); // EXTERNAL MODULE: ./node_modules/tweetnacl/nacl-fast.js


      var nacl_fast = __webpack_require__(7097); // EXTERNAL MODULE: ./node_modules/tweetnacl-util/nacl-util.js


      var nacl_util = __webpack_require__(7163); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/login/store/service/login.service.ts


      var login_service = __webpack_require__(8903); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/action/popup.action.ts


      var popup_action = __webpack_require__(1950);

      ; // CONCATENATED MODULE: ./angular/src/app/interceptor/request.interceptor.ts

      window.global = window; // @ts-ignore

      window.Buffer = window.Buffer || __webpack_require__(2574)
      /* .Buffer */
      .lW;

      var RequestInterceptor = /*#__PURE__*/function () {
        function RequestInterceptor(store, snackbarService, authService) {
          _classCallCheck(this, RequestInterceptor);

          this.store = store;
          this.snackbarService = snackbarService;
          this.authService = authService;
          this.refreshTokenInProgress = false;
          this.refreshTokenSubject = new BehaviorSubject
          /* BehaviorSubject */
          .X(null);
        }

        return _createClass(RequestInterceptor, [{
          key: "intercept",
          value: function intercept(request, next) {
            var _this1 = this;

            return next.handle(this.addAuthenticationToken(request)).pipe((0, catchError
            /* catchError */
            .K)(function (error) {
              switch (error.status) {
                case status_code
                /* StatusCode.UNAUTHORIZED */
                .G.UNAUTHORIZED:
                  return _this1.handle401Error(next, request);

                case status_code
                /* StatusCode.UNKNOWN_ERROR */
                .G.UNKNOWN_ERROR:
                  if (request.url.includes("email")) {
                    var excutiveJsion = JSON.parse(request.body);

                    _this1.store.dispatch(new popup_action
                    /* ShowExecutiveEmailIdLoaderClose */
                    .hr(excutiveJsion.id, message
                    /* ClientMessage.NO_EMAIL_FOUND */
                    .xT.NO_EMAIL_FOUND));
                  } else {
                    _this1.snackbarService.openSnackBar(message
                    /* ClientMessage.SERVER_ERROR */
                    .xT.SERVER_ERROR, constant_value
                    /* SNACKBAR_TIME.THREE_SECOND */
                    ._Q.THREE_SECOND, constant_value
                    /* SNACK_BAR_TYPE.ERROR */
                    .cx.ERROR);
                  }

                  break;

                case status_code
                /* StatusCode.INTERNALSERVERERROR */
                .G.INTERNALSERVERERROR:
                  _this1.snackbarService.openSnackBar(error.message, constant_value
                  /* SNACKBAR_TIME.THREE_SECOND */
                  ._Q.THREE_SECOND, constant_value
                  /* SNACK_BAR_TYPE.ERROR */
                  .cx.ERROR);

                  break;

                case status_code
                /* StatusCode.NOTFOUND */
                .G.NOTFOUND:
                  // this.snackbarService.openSnackBar(
                  //   ClientMessage.SERVER_ERROR_404,
                  //   SNACKBAR_TIME.THREE_SECOND,
                  //   SNACK_BAR_TYPE.ERROR
                  // );
                  break;

                case status_code
                /* StatusCode.BAD_GATEWAY */
                .G.BAD_GATEWAY:
                  _this1.snackbarService.openSnackBar(message
                  /* ClientMessage.SERVER_ERROR */
                  .xT.SERVER_ERROR, constant_value
                  /* SNACKBAR_TIME.THREE_SECOND */
                  ._Q.THREE_SECOND, constant_value
                  /* SNACK_BAR_TYPE.ERROR */
                  .cx.ERROR);

                  break;
              }

              return (0, throwError
              /* throwError */
              ._)(error);
            }));
          }
        }, {
          key: "handle401Error",
          value: function handle401Error(next, request) {
            var _this10 = this;

            if (!this.refreshTokenInProgress) {
              this.refreshTokenInProgress = true;
              this.refreshTokenSubject.next(null);
              return this.authService.getNewAccessToken(this.getRefreshToken()).pipe((0, switchMap
              /* switchMap */
              .w)(function (response) {
                if (response.statusCode === status_code
                /* StatusCode.SUCCESS */
                .G.SUCCESS && response !== null && response !== void 0 && response.data) {
                  var _response$data5;

                  _this10.store.dispatch(new login_action
                  /* SetAuthData */
                  .HR(response));

                  _this10.refreshTokenSubject.next(response === null || response === void 0 ? void 0 : (_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.accessToken);

                  _this10.refreshTokenInProgress = false;
                  return next.handle(_this10.addAuthenticationToken(request));
                } else {
                  return _this10.refreshTokenExpired();
                }
              }), (0, catchError
              /* catchError */
              .K)(function (error) {
                return _this10.refreshTokenExpired();
              }));
            } else {
              return this.refreshTokenSubject.pipe((0, filter
              /* filter */
              .h)(function (token) {
                return token !== null;
              }), (0, take
              /* take */
              .q)(1), (0, switchMap
              /* switchMap */
              .w)(function () {
                return next.handle(_this10.addAuthenticationToken(request));
              }));
            }
          }
        }, {
          key: "refreshTokenExpired",
          value: function refreshTokenExpired() {
            this.refreshTokenInProgress = false;
            this.snackbarService.openSnackBar(message
            /* ClientMessage.SESSION_EXPIRED */
            .xT.SESSION_EXPIRED, constant_value
            /* SNACKBAR_TIME.THREE_SECOND */
            ._Q.THREE_SECOND, constant_value
            /* SNACK_BAR_TYPE.ERROR */
            .cx.ERROR);
            return this.store.dispatch(new login_action
            /* LogoutSuccess */
            .ys());
          }
        }, {
          key: "encrypt",
          value: function encrypt(value) {
            var encodeBase64 = nacl_util.encodeBase64;
            var nonce = nacl_fast.randomBytes(24);
            var sampleValue = Buffer.from(constant_value
            /* SAMPLE_TEST */
            .CS, 'utf8');
            var sampleData = Buffer.from(value, 'utf8');
            var sampleValueE = nacl_fast.secretbox(sampleData, nonce, sampleValue);
            var result = "".concat(encodeBase64(nonce), ":").concat(encodeBase64(sampleValueE)); // const wordArray = crypto.enc.Utf8.parse(value);
            // return crypto.enc.Base64.stringify(wordArray);

            return result;
          }
        }, {
          key: "addAuthenticationToken",
          value: function addAuthenticationToken(request) {
            if (!(request.url.includes(api_url
            /* LOGIN_API */
            .C2) || request.url.includes(api_url
            /* VERIFY_EMAIL_API */
            .eB) || request.url.includes(api_url
            /* SIGNUP_API */
            .Rl) || request.url.includes(api_url
            /* IS_EMAIL_EXIST_API */
            .H5) || request.url.includes(api_url
            /* UPDATE_PASSWORD_API */
            .cs) || request.url.includes(api_url
            /* FORGOT_PASSWORD_API */
            .GF) || request.url.includes(api_url
            /* GET_ACCESS_TOKEN_BY_REFRESH_TOKEN_API */
            .JE))) {
              // if (request.url.includes('voyager') || request.url.includes('sales-api')) {
              //   return request.clone({
              //     setHeaders: {
              //       'csrf-token': getCsrfToken()
              //     }
              //   });
              // } else 
              if (request.url.includes('/linkedinextension')) {
                return request.clone({
                  setHeaders: {
                    auth: this.encrypt(constant_value
                    /* SAMPLE_DATA */
                    .rZ),
                    Authorization: 'bearer ' + this.getAccessToken(),
                    dsmID: this.getDsmId()
                  }
                });
              } else if (request.url.includes('/linkedinParser')) {
                return request.clone({
                  setHeaders: {
                    auth: this.encrypt(constant_value
                    /* SAMPLE_DATA */
                    .rZ)
                  }
                });
              } else {
                return request.clone({
                  setHeaders: {
                    Authorization: 'bearer ' + this.getAccessToken(),
                    dsmID: this.getDsmId(),
                    timeZone: this.getTimeZone()
                  }
                });
              }
            } else {
              return request;
            }
          }
        }, {
          key: "getAccessToken",
          value: function getAccessToken() {
            return this.store.selectSnapshot(function (state) {
              var _state$auth, _state$auth$authData;

              return state === null || state === void 0 ? void 0 : (_state$auth = state.auth) === null || _state$auth === void 0 ? void 0 : (_state$auth$authData = _state$auth.authData) === null || _state$auth$authData === void 0 ? void 0 : _state$auth$authData.accessToken;
            });
          }
        }, {
          key: "getDsmId",
          value: function getDsmId() {
            return this.store.selectSnapshot(function (state) {
              var _state$auth2, _state$auth2$authData;

              return state === null || state === void 0 ? void 0 : (_state$auth2 = state.auth) === null || _state$auth2 === void 0 ? void 0 : (_state$auth2$authData = _state$auth2.authData) === null || _state$auth2$authData === void 0 ? void 0 : _state$auth2$authData.dsmID;
            });
          }
        }, {
          key: "getRefreshToken",
          value: function getRefreshToken() {
            return this.store.selectSnapshot(function (state) {
              var _state$auth3, _state$auth3$authData;

              return state === null || state === void 0 ? void 0 : (_state$auth3 = state.auth) === null || _state$auth3 === void 0 ? void 0 : (_state$auth3$authData = _state$auth3.authData) === null || _state$auth3$authData === void 0 ? void 0 : _state$auth3$authData.refreshToken;
            });
          }
        }, {
          key: "getTimeZone",
          value: function getTimeZone() {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
          }
        }]);
      }();

      RequestInterceptor.ɵfac = function RequestInterceptor_Factory(t) {
        return new (t || RequestInterceptor)(core
        /* ɵɵinject */
        .LFG(ngxs_store
        /* Store */
        .yh), core
        /* ɵɵinject */
        .LFG(snack_bar_service
        /* SnackbarService */
        .o), core
        /* ɵɵinject */
        .LFG(login_service
        /* LoginService */
        .r));
      };

      RequestInterceptor.ɵprov = /*@__PURE__*/core
      /* ɵɵdefineInjectable */
      .Yz7({
        token: RequestInterceptor,
        factory: RequestInterceptor.ɵfac
      }); // EXTERNAL MODULE: ./angular/src/app/common/snack-bar/snack-bar.module.ts

      var snack_bar_module = __webpack_require__(4486); // EXTERNAL MODULE: ./node_modules/@angular/material/__ivy_ngcc__/fesm2015/button.js


      var fesm2015_button = __webpack_require__(781); // EXTERNAL MODULE: ./node_modules/@angular/material/__ivy_ngcc__/fesm2015/menu.js + 2 modules


      var menu = __webpack_require__(1303); // EXTERNAL MODULE: ./node_modules/@angular/common/__ivy_ngcc__/fesm2015/common.js


      var common = __webpack_require__(4364); // EXTERNAL MODULE: ./angular/src/app/modules/popup/pages/popup/store/state/extract-company.state.ts


      var extract_company_state = __webpack_require__(1333);

      ; // CONCATENATED MODULE: ./angular/src/app/app.module.ts
      //import { NgxsLoggerPluginModule } from '@ngxs/logger-plugin';

      var AppModule = /*#__PURE__*/_createClass(function AppModule() {
        _classCallCheck(this, AppModule);
      });

      AppModule.ɵfac = function AppModule_Factory(t) {
        return new (t || AppModule)();
      };

      AppModule.ɵmod = /*@__PURE__*/core
      /* ɵɵdefineNgModule */
      .oAB({
        type: AppModule,
        bootstrap: [AppComponent]
      });
      AppModule.ɵinj = /*@__PURE__*/core
      /* ɵɵdefineInjector */
      .cJS({
        providers: [{
          provide: http
          /* HTTP_INTERCEPTORS */
          .TP,
          useClass: RequestInterceptor,
          multi: true
        }],
        imports: [[platform_browser
        /* BrowserModule */
        .b2, fesm2015_button
        /* MatButtonModule */
        .ot, menu
        /* MatMenuModule */
        .Tx, common
        /* CommonModule */
        .ez, AppRoutingModule, ngxs_store
        /* NgxsModule.forRoot */
        .$l.forRoot([extract_company_state
        /* ExtractCompanyState */
        .L], {
          compatibility: {
            strictContentSecurityPolicy: true
          }
        }), ngxs_storage_plugin
        /* NgxsStoragePluginModule.forRoot */
        .x.forRoot({
          key: ['auth.authData', 'executives', "popup.dailyLimit", "popup.dailyTime", "auth"]
        }), //NgxsLoggerPluginModule.forRoot({ disabled: environment.production }),
        ngxs_router_plugin
        /* NgxsRouterPluginModule.forRoot */
        .G1.forRoot(), http
        /* HttpClientModule */
        .JF, animations
        /* BrowserAnimationsModule */
        .PW, snack_bar_module
        /* SnackBarModule */
        .Y]]
      });

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && core
        /* ɵɵsetNgModuleScope */
        .kYT(AppModule, {
          declarations: [AppComponent],
          imports: [platform_browser
          /* BrowserModule */
          .b2, fesm2015_button
          /* MatButtonModule */
          .ot, menu
          /* MatMenuModule */
          .Tx, common
          /* CommonModule */
          .ez, AppRoutingModule, ngxs_store
          /* ɵNgxsRootModule */
          .gr, ngxs_storage_plugin
          /* NgxsStoragePluginModule */
          .x, ngxs_router_plugin
          /* NgxsRouterPluginModule */
          .G1, http
          /* HttpClientModule */
          .JF, animations
          /* BrowserAnimationsModule */
          .PW, snack_bar_module
          /* SnackBarModule */
          .Y]
        });
      })(); // EXTERNAL MODULE: ./angular/src/environments/environment.ts


      var environment = __webpack_require__(8135); // EXTERNAL MODULE: ./angular/src/app/providers/tab-id.provider.ts


      var tab_id_provider = __webpack_require__(3154);

      ; // CONCATENATED MODULE: ./package.json

      var package_namespaceObject = {
        "i8": "1.0.1"
      };
      ; // CONCATENATED MODULE: ./angular/src/main.ts

      chrome.tabs.query({
        active: true,
        currentWindow: true
      }, function (tabs) {
        if (environment
        /* environment.production */
        .N.production) {
          (0, core
          /* enableProdMode */
          .G48)();
        }

        var tab = _toConsumableArray(tabs).pop();

        var tabId = tab.id;
        var appVersion = package_namespaceObject.i8 || '0.0.0';
        var metaTag = document.createElement('meta');
        metaTag.name = 'app-version';
        metaTag.content = appVersion;
        document.head.prepend(metaTag); // provides the current Tab ID so you can send messages to the content page

        platform_browser
        /* platformBrowser */
        .q6([{
          provide: tab_id_provider
          /* TAB_ID */
          .$,
          useValue: tabId
        }]).bootstrapModule(AppModule)["catch"](function (error) {
          return console.error(error);
        });
      });
      /***/
    },

    /***/
    5024:
    /***/
    function _() {
      /* (ignored) */

      /***/
    }
  },
  /******/
  function (__webpack_require__) {
    // webpackRuntimeModules

    /******/
    var __webpack_exec__ = function __webpack_exec__(moduleId) {
      return __webpack_require__(__webpack_require__.s = moduleId);
    };
    /******/


    __webpack_require__.O(0, ["vendor"], function () {
      return __webpack_exec__(3182);
    });
    /******/


    var __webpack_exports__ = __webpack_require__.O();
    /******/

  }]);
})();
//# sourceMappingURL=main-es5.js.map