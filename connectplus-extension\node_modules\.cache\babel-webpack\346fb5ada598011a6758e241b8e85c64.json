{"ast": null, "code": "import { __decorate, __metadata } from \"tslib\";\nimport { Store, Select } from \"@ngxs/store\";\nimport { LoginWithEmailAndPassword } from \"../store/action/login.action\";\nimport { UserResponse, UserRequest } from \"../store/model/login.model\";\nimport { FormGroup, FormControl, Validators } from \"@angular/forms\";\nimport { EMAIL_PATTERN } from \"src/app/common/helpers/pattern.helper\";\nimport { ScLoginState } from \"../store/state/login.state\";\nimport { Observable } from \"rxjs\";\nimport { EMAIL_REQUIRED, INVALID_EMAIL } from \"src/app/constant/message\";\nimport { LINKED_IN_URL, GMAIL_URL } from \"../../../../../constant/api.url\";\nimport { environment } from \"../../../../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngxs/store\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../../../../../ss-ui/input/component/input.component\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../../../../ss-ui/checkbox/component/checkbox.component\";\nimport * as i6 from \"../../../../../../ss-ui/button/component/button.component\";\nimport * as i7 from \"../../../../../common/custom-loader/customloader.component\";\nimport * as i8 from \"../../../../../common/pipe/FormControlPipe\";\n\nfunction SCLoginComponent_p_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const auth_r3 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", auth_r3.message, \" \");\n  }\n}\n\nfunction SCLoginComponent_app_custom_loader_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-custom-loader\");\n  }\n}\n\nexport class SCLoginComponent {\n  constructor(store) {\n    this.store = store;\n    this.hide = true;\n    this.errorMessage = \"\";\n  }\n\n  ngOnInit() {\n    this.initForm();\n  }\n\n  initForm() {\n    this.loginForm = new FormGroup({\n      email: new FormControl(\"\", [Validators.required, Validators.pattern(EMAIL_PATTERN)]),\n      password: new FormControl(\"\", [Validators.required]),\n      isRememberMe: new FormControl(false)\n    });\n    const email = this.store.selectSnapshot(state => state.auth.email);\n\n    if (email) {\n      this.email.setValue(email);\n    }\n  }\n\n  authenticateUser(provider) {\n    if (provider === \"gmail\") {\n      const scope = [\"profile\", \"email\"].join(\" \");\n      chrome.tabs.create({\n        url: `${GMAIL_URL}=${environment.googleClientId}&redirect_uri=${environment.gmailSignIn}&scope=${scope}`,\n        selected: true\n      });\n    } else if (provider === \"linkedin\") {\n      chrome.tabs.create({\n        url: `${LINKED_IN_URL}=${environment.linkedinClientId}&redirect_uri=${environment.linkedinSignIn}&scope=r_liteprofile%20r_emailaddress`,\n        selected: true\n      });\n    } else {\n      const payload = {\n        email: this.email.value,\n        password: this.password.value\n      };\n      this.store.dispatch(new LoginWithEmailAndPassword(payload, this.isRememberMe.value));\n    }\n  }\n\n  get isRememberMe() {\n    return this.loginForm.controls.isRememberMe;\n  }\n\n  get email() {\n    return this.loginForm.controls.email;\n  }\n\n  get password() {\n    return this.loginForm.controls.password;\n  }\n\n  get isEmailFieldValid() {\n    if (this.email.hasError(\"required\")) {\n      this.errorMessage = EMAIL_REQUIRED;\n      return true;\n    } else {\n      if (this.email.hasError(\"pattern\")) {\n        this.errorMessage = INVALID_EMAIL;\n        return true;\n      }\n    }\n  }\n\n}\n\nSCLoginComponent.ɵfac = function SCLoginComponent_Factory(t) {\n  return new (t || SCLoginComponent)(i0.ɵɵdirectiveInject(i1.Store));\n};\n\nSCLoginComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SCLoginComponent,\n  selectors: [[\"app-sc-login\"]],\n  decls: 62,\n  vars: 29,\n  consts: [[1, \"form-heading\"], [1, \"form-container\"], [1, \"login-container\"], [1, \"connectForm\"], [1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"field-container\", \"social-icon-wraper\", \"mt-0\", 2, \"margin-bottom\", \"20px\"], [1, \"text-color-black\", \"primary-font-family\", \"heading-sm\", \"col-sm-8\", \"pl-0\"], [1, \"social-login-container\", \"mb-0\", \"col-sm-4\", \"pl-0\", \"pr-0\"], [1, \"social-login\", \"d-block\", \"text-right\"], [\"href\", \"javascript:void(0);\", \"role\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"google-login-btn\", \"w-50\", \"social-icon-tag\", 2, \"margin-right\", \"10px\", 3, \"click\"], [\"googleBtn\", \"\"], [1, \"inline-block\", \"vm\"], [\"width\", \"16px\", \"alt\", \"Google sign-in\", \"src\", \"assets/img/google-icon.svg\"], [1, \"show-mobile-text\"], [\"href\", \" javascript:void(0);\", \"role\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"w-50\", \"social-icon-tag\", 2, \"text-transform\", \"none\", 3, \"click\"], [\"width\", \"16px\", \"alt\", \"linkedin sign-in\", \"src\", \"assets/img/linkedin-icon.svg\"], [1, \"social-signup-text\"], [1, \"field-container\"], [\"placeholder\", \"Email\", 3, \"label\", \"formControl\", \"autofocus\", \"required\", \"errorMsg\"], [\"placeholder\", \"Password\", 3, \"label\", \"isError\", \"formControl\", \"autofocus\", \"required\", \"type\", \"errorMsg\"], [\"class\", \"form-error\", 4, \"ngIf\"], [1, \"remember-me-container\"], [1, \"d-inline-block\"], [3, \"formControl\", \"label\"], [1, \"d-inline-block\", \"text-right\"], [\"href\", \"https://www.salezshark.com/connect/app/auth/forgotPassword\", \"target\", \"_blank\", 1, \"forgot-password\", \"text-weight-bold\", \"text-body-xs\", \"text-quaternary-color\"], [1, \"login-forgotBox\", \"clearfix\"], [1, \"float-right\"], [\"type\", \"submit\", \"ss-button\", \"\", 2, \"width\", \"100%\", 3, \"disabled\", \"type\"], [1, \"login-termCondition\", 2, \"display\", \"none\"], [\"href\", \"\"], [1, \"login-bgImg\"], [\"src\", \"assets/img/pattern.svg\", \"alt\", \"pattern\"], [1, \"signInOption\"], [\"href\", \"https://www.salezshark.com/connect/app/auth/sign-up\", \"target\", \"_blank\", 1, \"signInlink\"], [1, \"signupIntext\"], [4, \"ngIf\"], [1, \"form-error\"]],\n  template: function SCLoginComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵelementStart(3, \"div\", 3);\n      i0.ɵɵelementStart(4, \"form\", 4);\n      i0.ɵɵlistener(\"ngSubmit\", function SCLoginComponent_Template_form_ngSubmit_4_listener() {\n        return ctx.authenticateUser();\n      });\n      i0.ɵɵelementStart(5, \"div\", 5);\n      i0.ɵɵelementStart(6, \"h1\", 6);\n      i0.ɵɵtext(7, \"Login using\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 7);\n      i0.ɵɵelementStart(9, \"div\", 8);\n      i0.ɵɵelementStart(10, \"a\", 9, 10);\n      i0.ɵɵlistener(\"click\", function SCLoginComponent_Template_a_click_10_listener() {\n        return ctx.authenticateUser(\"gmail\");\n      });\n      i0.ɵɵelementStart(12, \"span\", 11);\n      i0.ɵɵelement(13, \"img\", 12);\n      i0.ɵɵelementStart(14, \"span\", 13);\n      i0.ɵɵtext(15, \"Sign in with Google\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"a\", 14);\n      i0.ɵɵlistener(\"click\", function SCLoginComponent_Template_a_click_16_listener() {\n        return ctx.authenticateUser(\"linkedin\");\n      });\n      i0.ɵɵelementStart(17, \"span\", 11);\n      i0.ɵɵelement(18, \"img\", 15);\n      i0.ɵɵelementStart(19, \"span\", 13);\n      i0.ɵɵtext(20, \"Sign in with LinkedIn\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"section\", 16);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Or Login With\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 17);\n      i0.ɵɵelementStart(25, \"section\");\n      i0.ɵɵelement(26, \"ss-input\", 18);\n      i0.ɵɵpipe(27, \"formControl\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"section\");\n      i0.ɵɵelement(29, \"ss-input\", 19);\n      i0.ɵɵpipe(30, \"formControl\");\n      i0.ɵɵtemplate(31, SCLoginComponent_p_31_Template, 2, 1, \"p\", 20);\n      i0.ɵɵpipe(32, \"async\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"div\", 21);\n      i0.ɵɵelementStart(34, \"div\", 22);\n      i0.ɵɵelement(35, \"ss-checkbox\", 23);\n      i0.ɵɵpipe(36, \"formControl\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 24);\n      i0.ɵɵelementStart(38, \"span\");\n      i0.ɵɵelementStart(39, \"a\", 25);\n      i0.ɵɵtext(40, \"Forgot Password?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"div\", 26);\n      i0.ɵɵelementStart(42, \"div\", 27);\n      i0.ɵɵelementStart(43, \"button\", 28);\n      i0.ɵɵtext(44, \"Login\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"div\", 29);\n      i0.ɵɵelementStart(46, \"span\");\n      i0.ɵɵtext(47, \"By using Salezconnect, you are agree to our \");\n      i0.ɵɵelementStart(48, \"a\", 30);\n      i0.ɵɵtext(49, \"Terms of services\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(50, \" and \");\n      i0.ɵɵelementStart(51, \"a\", 30);\n      i0.ɵɵtext(52, \"Privacy policy\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"span\", 31);\n      i0.ɵɵelement(54, \"img\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"div\", 33);\n      i0.ɵɵtext(56, \"DON\\u2019T HAVE AN ACCOUNT? \");\n      i0.ɵɵelementStart(57, \"a\", 34);\n      i0.ɵɵtext(58, \" SIGN UP\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(59, \"div\", 35);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(60, SCLoginComponent_app_custom_loader_60_Template, 1, 0, \"app-custom-loader\", 36);\n      i0.ɵɵpipe(61, \"async\");\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n      i0.ɵɵadvance(22);\n      i0.ɵɵproperty(\"label\", \"Email\")(\"formControl\", i0.ɵɵpipeBind1(27, 19, ctx.email))(\"autofocus\", true)(\"required\", true)(\"errorMsg\", ctx.isEmailFieldValid ? ctx.errorMessage : \"\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"label\", \"Password\")(\"isError\", ctx.password.invalid && ctx.password.touched)(\"formControl\", i0.ɵɵpipeBind1(30, 21, ctx.password))(\"autofocus\", true)(\"required\", true)(\"type\", ctx.hide ? \"password\" : \"text\")(\"errorMsg\", \" Password is required\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(32, 23, ctx.users$));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"formControl\", i0.ɵɵpipeBind1(36, 25, ctx.isRememberMe))(\"label\", \"Remember me\");\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"disabled\", !ctx.loginForm.valid)(\"type\", \"primary\");\n      i0.ɵɵadvance(17);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(61, 27, ctx.isLoginLoading$));\n    }\n  },\n  directives: [i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i3.InputComponent, i2.NgControlStatus, i2.FormControlDirective, i2.RequiredValidator, i4.NgIf, i5.CheckboxComponent, i6.ButtonComponent, i7.CustomloaderComponent],\n  pipes: [i8.FormControlPipe, i4.AsyncPipe],\n  styles: [\"[_ngcontent-%COMP%]:root{--blue-subTitle: #99bbff;--blue-E8EEFD: #e8eefd;--tabBgColor: #e5e8ed;--color-ff6b6b: #ff6b6b;--color-f0f6ff: #f0f6ff;--color-cacdd6: #cacdd6;--color-f5f5f5: #f5f5f5;--color-eee: #eee;--color-ddd: #ddd;--color-b8d5ff: #b8d5ff;--color-4ecdc4: #4ecdc4;--color-f6f7f8: #f6f7f8;--color-d8dce3: #d8dce3;--color-f6d0d4: #f6d0d4;--color-e7f0f9: #e7f0f9;--color-c3cad6: #c3cad6;--color-d2d9ef: #d2d9ef;--color-c6d9ff: #c6d9ff;--color-D8DBE2: #d8dbe2;--color-CCDDFF: #ccddff;--color-6699ff: #6699ff;--color-9dcaff: #9dcaff;--color-seagreen: seagreen;--color-ca98f5: #ca98f5;--color-b3bcc9: #b3bcc9;--color-e8e8e8: #e8e8e8;--color-dae9ff: #dae9ff;--color-dcdfe7: #dcdfe7;--color-e7ebf2: #e7ebf2;--color-dbeaff: #dbeaff;--color-ecf4ff: #ecf4ff;--color-f9f9fb: #f9f9fb;--color-f8f8f8: #f8f8f8;--color-EBFFD7: #ebffd7;--color-f3f3f3: #f3f3f3;--color-f5f8ff: #f5f8ff;--color-f1f1f1: #f1f1f1;--color-E8EAEF: #e8eaef;--color-efeeea: #efeeea;--color-a0a8b9: #a0a8b9;--color-808fa5: #808fa5;--color-66666: #666666;--color-c5ccd8: #969fb2;--color-9AA1AF: #9aa1af;--color-6a7386: #6a7386;--color-999999: #999999;--color-333435: #333435;--color-ef9da7: #ef9da7;--color-e0c59a: #e0c59a}[_ngcontent-%COMP%]:root{--ss-background-default-shadow: 0 2px 12px 0 #E0E5EC;--ss-background-hoverd-shadow: 0 10px 24px 0 #E0E5EC}header[_ngcontent-%COMP%]{text-align:center;padding:12px 20px;line-height:35px;background-color:var(--ss-header-bg-color)}header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:40px;width:150px}.main-form-container[_ngcontent-%COMP%]{height:calc(100% - 95px);position:relative}.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]{border-radius:4px;margin:0 auto}.signupIntext[_ngcontent-%COMP%]{padding-top:30px;padding-bottom:5px;text-align:center}.signupIntext[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--secondary-font);font-weight:bold;line-height:14px;font-size:12px;text-transform:uppercase}.signupIntext[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-family:var(--ss-secondary-font-family)}.form-heading[_ngcontent-%COMP%]{margin-top:56px}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:24px;font-weight:300}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:1px}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]{float:right}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]{margin:0 auto}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]{max-width:500px;margin:auto;position:relative;padding:25px 58px 70px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container.signup-container[_ngcontent-%COMP%]{max-width:576px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";left:0px;top:10px;width:73px;height:73px;border-radius:4px;background:#ebffd7;background:var(--color-EBFFD7);z-index:1}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .login-bgImg[_ngcontent-%COMP%]{position:absolute;right:0px;bottom:0px;width:128px;height:275px;z-index:1}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]{background-color:var(--ss-white-color);border-radius:2px;box-shadow:0 2px 12px #e0e5ec;box-shadow:var(--ss-background-default-shadow);transition:all .3s ease-out;-o-transition:all .3s ease-out;width:auto;margin:auto;position:relative;z-index:2}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]{display:block;font-size:13px;color:var(--ss-label-text-color);font-family:var(--ss-secondary-font-family);letter-spacing:0;line-height:16px;text-align:left;font-size:.8125rem;padding-bottom:5px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]{margin-bottom:1rem}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]{text-align:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]{display:flex;justify-content:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .google-login-btn[_ngcontent-%COMP%]{margin-right:.5rem}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]{margin:0 auto}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:2.4px;text-align:center;position:relative;overflow:hidden}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;vertical-align:baseline;zoom:1;position:relative;padding:2px 8px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{content:\\\"\\\";display:block;width:1000px;position:absolute;top:.73em;width:116px;border:1px solid #eaeaea}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before{right:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{left:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .sign-in-error-container[_ngcontent-%COMP%]{min-height:500px;display:flex;flex-direction:column;justify-content:center}.form-heading[_ngcontent-%COMP%]   .form-container.register-container[_ngcontent-%COMP%]{padding:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]{padding-bottom:65px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]{width:200px;height:50px;border:1px solid #ccddff;border-radius:4px;background-color:var(--ss-white-color);float:left;transition:box-shadow .3s ease-out,transform .3s ease-out,opacity .2s ease-out;box-shadow:0 2px 20px #0000000d;cursor:pointer}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]:hover{box-shadow:#2d2d2d0d 0 1px 1px,#3131310d 0 2px 2px,#2a2a2a0d 0 4px 4px,#2020200d 0 8px 8px,#3131310d 0 16px 16px,#2323230d 0 32px 32px;transform:translateY(-2px)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]   .social-img[_ngcontent-%COMP%]{height:40px;width:40px;box-sizing:content-box;margin-left:36px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]   .social-p[_ngcontent-%COMP%]{padding-top:12px;color:var(--ss-black-color);font-size:13px;float:right;margin-right:56px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social.login[_ngcontent-%COMP%]{margin-right:12px;margin-left:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{padding:20px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{border:none;width:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field.less[_ngcontent-%COMP%]{width:50%;vertical-align:top}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:var(--ss-error-color);margin:4px;font-weight:normal;font-size:12px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .remember-me-container[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--primary-font);line-height:18px;font-size:12px;font-weight:700;cursor:pointer}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .remember-me-container[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover{text-decoration:none}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]{margin:0 auto;border-radius:4px;background-color:var(--ss-primary-color);color:var(--ss-white-color);border:none;cursor:pointer;min-width:110px;text-transform:uppercase;font-family:var(--secondary-font);line-height:16px;font-size:13px;opacity:1!important;height:38px;font-family:var(--ss-secondary-font-family);border:1px solid transparent}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:hover{background-color:var(--ss-white-color);color:var(--ss-primary-color)!important;border:1px solid var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;padding-top:8px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:relative}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   div.btn-submit[_ngcontent-%COMP%]{position:absolute;right:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{margin:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]:before{left:24px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-termCondition[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--secondary-font);font-size:12px;line-height:18px;text-align:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-termCondition[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-family:var(--ss-secondary-font-family);color:var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;color:#f9f9fb;color:var(--color-f9f9fb)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:disabled:hover{box-shadow:0 2px 20px #0000000d}.terms[_ngcontent-%COMP%]{padding-top:60px;text-align:center;margin:0 auto}.terms[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:1px;line-height:14px}.terms[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ss-primary-color);cursor:pointer}.form-error[_ngcontent-%COMP%]{padding-top:unset!important}.ss-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px!important;width:20px!important;border:2px solid var(--ss-quinary-color);background-color:var(--ss-white-color);border-radius:2px}  .mat-checkbox .mat-checkbox-frame{border-color:var(--ss-black-color)}  .mat-checkbox-checked .mat-checkbox-background{background-color:#808fa5!important;background-color:var(--color-808fa5)!important}.mat-icon[_ngcontent-%COMP%]{cursor:pointer;margin-right:10px}.img-container[_ngcontent-%COMP%]{margin:auto}.top[_ngcontent-%COMP%]{margin-top:54px}.material-icons[_ngcontent-%COMP%]{display:inline-flex;vertical-align:middle}.login-container[_ngcontent-%COMP%]{padding:45px 20px!important}.float-right[_ngcontent-%COMP%]{width:100%!important}.signInOption[_ngcontent-%COMP%]{font-size:13px;font-family:var(--ss-secondary-font-family);font-weight:700;margin-top:10px}.signInOption[_ngcontent-%COMP%]   .signInlink[_ngcontent-%COMP%]{color:var(--ss-primary-color);cursor:pointer;text-decoration:none}.signInOption[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{text-decoration:none}.social-signup-text[_ngcontent-%COMP%]{margin-top:-15px;margin-bottom:15px;width:100%;position:relative;text-align:center;font:700 14px/20px Roboto,\\\"Helvetica Neue\\\",sans-serif}.social-signup-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{text-decoration:none;padding:0 7px;background-color:#fff;display:inline-block;position:relative}.social-signup-text[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;width:100%;height:.5px;background-color:#e5e8ed;top:10px;left:0;z-index:0}.btn-outline-dark[_ngcontent-%COMP%]:hover{background-color:transparent!important;border-color:var(--ss-primary-color)!important}.social-icon-wraper[_ngcontent-%COMP%]{display:flex}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:34px!important;height:34px!important;line-height:14px!important}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-icon-tag[_ngcontent-%COMP%]{padding:8px 6px;border-radius:2px!important;border:1.5px solid var(--ss-quinary-color);font-family:var(--ss-secondary-font-family);font-weight:700}.show-mobile-text[_ngcontent-%COMP%]{display:none}.login-form[_ngcontent-%COMP%]{width:100%}.remember-me-container[_ngcontent-%COMP%]   .d-inline-block[_ngcontent-%COMP%]{width:50%;font-weight:700}.error-message[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}@media (max-width: 1023px){.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]{max-width:600px}.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .create-account-steps[_ngcontent-%COMP%]{border-radius:4px;float:right;margin-top:222px;width:250px!important;right:-226px!important}.form-heading[_ngcontent-%COMP%]{padding:10px;margin-top:56px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field.less[_ngcontent-%COMP%]{width:100%}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{text-align:center}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   .signup[_ngcontent-%COMP%]{width:100%}}@media (max-width: 1440px){.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .create-account-steps[_ngcontent-%COMP%]{width:260px;right:-236px}}\"]\n});\n\n__decorate([Select(ScLoginState.getLoginUserDetails), __metadata(\"design:type\", Observable)], SCLoginComponent.prototype, \"users$\", void 0);\n\n__decorate([Select(ScLoginState.isLoginLoading), __metadata(\"design:type\", Observable)], SCLoginComponent.prototype, \"isLoginLoading$\", void 0);", "map": {"version": 3, "sources": ["C:/Users/<USER>/Downloads/connectplus-extension/connectplus-extension/angular/src/app/modules/popup/pages/login/component/login.component.ts"], "names": ["__decorate", "__metadata", "Store", "Select", "LoginWithEmailAndPassword", "UserResponse", "UserRequest", "FormGroup", "FormControl", "Validators", "EMAIL_PATTERN", "ScLoginState", "Observable", "EMAIL_REQUIRED", "INVALID_EMAIL", "LINKED_IN_URL", "GMAIL_URL", "environment", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "SCLoginComponent_p_31_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "auth_r3", "$implicit", "ɵɵadvance", "ɵɵtextInterpolate1", "message", "SCLoginComponent_app_custom_loader_60_Template", "ɵɵelement", "SCLoginComponent", "constructor", "store", "hide", "errorMessage", "ngOnInit", "initForm", "loginForm", "email", "required", "pattern", "password", "isRememberMe", "selectSnapshot", "state", "auth", "setValue", "authenticateUser", "provider", "scope", "join", "chrome", "tabs", "create", "url", "googleClientId", "gmailSignIn", "selected", "linkedinClientId", "linkedinSignIn", "payload", "value", "dispatch", "controls", "isEmailFieldValid", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "SCLoginComponent_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "consts", "template", "SCLoginComponent_Template", "ɵɵlistener", "SCLoginComponent_Template_form_ngSubmit_4_listener", "SCLoginComponent_Template_a_click_10_listener", "SCLoginComponent_Template_a_click_16_listener", "ɵɵpipe", "ɵɵtemplate", "ɵɵproperty", "ɵɵpipeBind1", "invalid", "touched", "users$", "valid", "isLoginLoading$", "directives", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "InputComponent", "NgControlStatus", "FormControlDirective", "RequiredValidator", "NgIf", "CheckboxComponent", "ButtonComponent", "CustomloaderComponent", "pipes", "FormControlPipe", "AsyncPipe", "styles", "getLoginUserDetails", "prototype", "isLoginLoading"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,UAArB,QAAuC,OAAvC;AACA,SAASC,KAAT,EAAgBC,MAAhB,QAA8B,aAA9B;AACA,SAASC,yBAAT,QAA0C,8BAA1C;AACA,SAASC,YAAT,EAAuBC,WAAvB,QAA2C,4BAA3C;AACA,SAASC,SAAT,EAAoBC,WAApB,EAAiCC,UAAjC,QAAmD,gBAAnD;AACA,SAASC,aAAT,QAA8B,uCAA9B;AACA,SAASC,YAAT,QAA6B,4BAA7B;AACA,SAASC,UAAT,QAA2B,MAA3B;AACA,SAASC,cAAT,EAAyBC,aAAzB,QAA8C,0BAA9C;AACA,SAASC,aAAT,EAAwBC,SAAxB,QAAyC,iCAAzC;AACA,SAASC,WAAT,QAA4B,4CAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,yDAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+DAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,2DAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4DAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4CAApB;;AACA,SAASC,8BAAT,CAAwCC,EAAxC,EAA4CC,GAA5C,EAAiD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3DV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV;AACAb,IAAAA,EAAE,CAACc,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMK,OAAO,GAAGJ,GAAG,CAACK,SAApB;AACAhB,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,IAAAA,EAAE,CAACkB,kBAAH,CAAsB,GAAtB,EAA2BH,OAAO,CAACI,OAAnC,EAA4C,GAA5C;AACH;AAAE;;AACH,SAASC,8CAAT,CAAwDV,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EV,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb,EAAgB,mBAAhB;AACH;AAAE;;AACH,OAAO,MAAMC,gBAAN,CAAuB;AAC1BC,EAAAA,WAAW,CAACC,KAAD,EAAQ;AACf,SAAKA,KAAL,GAAaA,KAAb;AACA,SAAKC,IAAL,GAAY,IAAZ;AACA,SAAKC,YAAL,GAAoB,EAApB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKC,QAAL;AACH;;AACDA,EAAAA,QAAQ,GAAG;AACP,SAAKC,SAAL,GAAiB,IAAIxC,SAAJ,CAAc;AAC3ByC,MAAAA,KAAK,EAAE,IAAIxC,WAAJ,CAAgB,EAAhB,EAAoB,CACvBC,UAAU,CAACwC,QADY,EAEvBxC,UAAU,CAACyC,OAAX,CAAmBxC,aAAnB,CAFuB,CAApB,CADoB;AAK3ByC,MAAAA,QAAQ,EAAE,IAAI3C,WAAJ,CAAgB,EAAhB,EAAoB,CAACC,UAAU,CAACwC,QAAZ,CAApB,CALiB;AAM3BG,MAAAA,YAAY,EAAE,IAAI5C,WAAJ,CAAgB,KAAhB;AANa,KAAd,CAAjB;AAQA,UAAMwC,KAAK,GAAG,KAAKN,KAAL,CAAWW,cAAX,CAA2BC,KAAD,IAAWA,KAAK,CAACC,IAAN,CAAWP,KAAhD,CAAd;;AACA,QAAIA,KAAJ,EAAW;AACP,WAAKA,KAAL,CAAWQ,QAAX,CAAoBR,KAApB;AACH;AACJ;;AACDS,EAAAA,gBAAgB,CAACC,QAAD,EAAW;AACvB,QAAIA,QAAQ,KAAK,OAAjB,EAA0B;AACtB,YAAMC,KAAK,GAAG,CAAC,SAAD,EAAY,OAAZ,EAAqBC,IAArB,CAA0B,GAA1B,CAAd;AACAC,MAAAA,MAAM,CAACC,IAAP,CAAYC,MAAZ,CAAmB;AACfC,QAAAA,GAAG,EAAG,GAAEhD,SAAU,IAAGC,WAAW,CAACgD,cAAe,iBAAgBhD,WAAW,CAACiD,WAAY,UAASP,KAAM,EADxF;AAEfQ,QAAAA,QAAQ,EAAE;AAFK,OAAnB;AAIH,KAND,MAOK,IAAIT,QAAQ,KAAK,UAAjB,EAA6B;AAC9BG,MAAAA,MAAM,CAACC,IAAP,CAAYC,MAAZ,CAAmB;AACfC,QAAAA,GAAG,EAAG,GAAEjD,aAAc,IAAGE,WAAW,CAACmD,gBAAiB,iBAAgBnD,WAAW,CAACoD,cAAe,uCADlF;AAEfF,QAAAA,QAAQ,EAAE;AAFK,OAAnB;AAIH,KALI,MAMA;AACD,YAAMG,OAAO,GAAG;AACZtB,QAAAA,KAAK,EAAE,KAAKA,KAAL,CAAWuB,KADN;AAEZpB,QAAAA,QAAQ,EAAE,KAAKA,QAAL,CAAcoB;AAFZ,OAAhB;AAIA,WAAK7B,KAAL,CAAW8B,QAAX,CAAoB,IAAIpE,yBAAJ,CAA8BkE,OAA9B,EAAuC,KAAKlB,YAAL,CAAkBmB,KAAzD,CAApB;AACH;AACJ;;AACe,MAAZnB,YAAY,GAAG;AACf,WAAO,KAAKL,SAAL,CAAe0B,QAAf,CAAwBrB,YAA/B;AACH;;AACQ,MAALJ,KAAK,GAAG;AACR,WAAO,KAAKD,SAAL,CAAe0B,QAAf,CAAwBzB,KAA/B;AACH;;AACW,MAARG,QAAQ,GAAG;AACX,WAAO,KAAKJ,SAAL,CAAe0B,QAAf,CAAwBtB,QAA/B;AACH;;AACoB,MAAjBuB,iBAAiB,GAAG;AACpB,QAAI,KAAK1B,KAAL,CAAW2B,QAAX,CAAoB,UAApB,CAAJ,EAAqC;AACjC,WAAK/B,YAAL,GAAoB/B,cAApB;AACA,aAAO,IAAP;AACH,KAHD,MAIK;AACD,UAAI,KAAKmC,KAAL,CAAW2B,QAAX,CAAoB,SAApB,CAAJ,EAAoC;AAChC,aAAK/B,YAAL,GAAoB9B,aAApB;AACA,eAAO,IAAP;AACH;AACJ;AACJ;;AAjEyB;;AAmE9B0B,gBAAgB,CAACoC,IAAjB,GAAwB,SAASC,wBAAT,CAAkCC,CAAlC,EAAqC;AAAE,SAAO,KAAKA,CAAC,IAAItC,gBAAV,EAA4BtB,EAAE,CAAC6D,iBAAH,CAAqB5D,EAAE,CAACjB,KAAxB,CAA5B,CAAP;AAAqE,CAApI;;AACAsC,gBAAgB,CAACwC,IAAjB,GAAwB,aAAc9D,EAAE,CAAC+D,iBAAH,CAAqB;AAAEC,EAAAA,IAAI,EAAE1C,gBAAR;AAA0B2C,EAAAA,SAAS,EAAE,CAAC,CAAC,cAAD,CAAD,CAArC;AAAyDC,EAAAA,KAAK,EAAE,EAAhE;AAAoEC,EAAAA,IAAI,EAAE,EAA1E;AAA8EC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,cAAJ,CAAD,EAAsB,CAAC,CAAD,EAAI,gBAAJ,CAAtB,EAA6C,CAAC,CAAD,EAAI,iBAAJ,CAA7C,EAAqE,CAAC,CAAD,EAAI,aAAJ,CAArE,EAAyF,CAAC,CAAD,EAAI,YAAJ,EAAkB,CAAlB,EAAqB,WAArB,EAAkC,UAAlC,CAAzF,EAAwI,CAAC,CAAD,EAAI,iBAAJ,EAAuB,oBAAvB,EAA6C,MAA7C,EAAqD,CAArD,EAAwD,eAAxD,EAAyE,MAAzE,CAAxI,EAA0N,CAAC,CAAD,EAAI,kBAAJ,EAAwB,qBAAxB,EAA+C,YAA/C,EAA6D,UAA7D,EAAyE,MAAzE,CAA1N,EAA4S,CAAC,CAAD,EAAI,wBAAJ,EAA8B,MAA9B,EAAsC,UAAtC,EAAkD,MAAlD,EAA0D,MAA1D,CAA5S,EAA+W,CAAC,CAAD,EAAI,cAAJ,EAAoB,SAApB,EAA+B,YAA/B,CAA/W,EAA6Z,CAAC,MAAD,EAAS,qBAAT,EAAgC,MAAhC,EAAwC,QAAxC,EAAkD,CAAlD,EAAqD,KAArD,EAA4D,kBAA5D,EAAgF,kBAAhF,EAAoG,MAApG,EAA4G,iBAA5G,EAA+H,CAA/H,EAAkI,cAAlI,EAAkJ,MAAlJ,EAA0J,CAA1J,EAA6J,OAA7J,CAA7Z,EAAokB,CAAC,WAAD,EAAc,EAAd,CAApkB,EAAulB,CAAC,CAAD,EAAI,cAAJ,EAAoB,IAApB,CAAvlB,EAAknB,CAAC,OAAD,EAAU,MAAV,EAAkB,KAAlB,EAAyB,gBAAzB,EAA2C,KAA3C,EAAkD,4BAAlD,CAAlnB,EAAmsB,CAAC,CAAD,EAAI,kBAAJ,CAAnsB,EAA4tB,CAAC,MAAD,EAAS,sBAAT,EAAiC,MAAjC,EAAyC,QAAzC,EAAmD,CAAnD,EAAsD,KAAtD,EAA6D,kBAA7D,EAAiF,MAAjF,EAAyF,iBAAzF,EAA4G,CAA5G,EAA+G,gBAA/G,EAAiI,MAAjI,EAAyI,CAAzI,EAA4I,OAA5I,CAA5tB,EAAk3B,CAAC,OAAD,EAAU,MAAV,EAAkB,KAAlB,EAAyB,kBAAzB,EAA6C,KAA7C,EAAoD,8BAApD,CAAl3B,EAAu8B,CAAC,CAAD,EAAI,oBAAJ,CAAv8B,EAAk+B,CAAC,CAAD,EAAI,iBAAJ,CAAl+B,EAA0/B,CAAC,aAAD,EAAgB,OAAhB,EAAyB,CAAzB,EAA4B,OAA5B,EAAqC,aAArC,EAAoD,WAApD,EAAiE,UAAjE,EAA6E,UAA7E,CAA1/B,EAAolC,CAAC,aAAD,EAAgB,UAAhB,EAA4B,CAA5B,EAA+B,OAA/B,EAAwC,SAAxC,EAAmD,aAAnD,EAAkE,WAAlE,EAA+E,UAA/E,EAA2F,MAA3F,EAAmG,UAAnG,CAAplC,EAAosC,CAAC,OAAD,EAAU,YAAV,EAAwB,CAAxB,EAA2B,MAA3B,CAApsC,EAAwuC,CAAC,CAAD,EAAI,uBAAJ,CAAxuC,EAAswC,CAAC,CAAD,EAAI,gBAAJ,CAAtwC,EAA6xC,CAAC,CAAD,EAAI,aAAJ,EAAmB,OAAnB,CAA7xC,EAA0zC,CAAC,CAAD,EAAI,gBAAJ,EAAsB,YAAtB,CAA1zC,EAA+1C,CAAC,MAAD,EAAS,4DAAT,EAAuE,QAAvE,EAAiF,QAAjF,EAA2F,CAA3F,EAA8F,iBAA9F,EAAiH,kBAAjH,EAAqI,cAArI,EAAqJ,uBAArJ,CAA/1C,EAA8gD,CAAC,CAAD,EAAI,iBAAJ,EAAuB,UAAvB,CAA9gD,EAAkjD,CAAC,CAAD,EAAI,aAAJ,CAAljD,EAAskD,CAAC,MAAD,EAAS,QAAT,EAAmB,WAAnB,EAAgC,EAAhC,EAAoC,CAApC,EAAuC,OAAvC,EAAgD,MAAhD,EAAwD,CAAxD,EAA2D,UAA3D,EAAuE,MAAvE,CAAtkD,EAAspD,CAAC,CAAD,EAAI,qBAAJ,EAA2B,CAA3B,EAA8B,SAA9B,EAAyC,MAAzC,CAAtpD,EAAwsD,CAAC,MAAD,EAAS,EAAT,CAAxsD,EAAstD,CAAC,CAAD,EAAI,aAAJ,CAAttD,EAA0uD,CAAC,KAAD,EAAQ,wBAAR,EAAkC,KAAlC,EAAyC,SAAzC,CAA1uD,EAA+xD,CAAC,CAAD,EAAI,cAAJ,CAA/xD,EAAozD,CAAC,MAAD,EAAS,qDAAT,EAAgE,QAAhE,EAA0E,QAA1E,EAAoF,CAApF,EAAuF,YAAvF,CAApzD,EAA05D,CAAC,CAAD,EAAI,cAAJ,CAA15D,EAA+6D,CAAC,CAAD,EAAI,MAAJ,CAA/6D,EAA47D,CAAC,CAAD,EAAI,YAAJ,CAA57D,CAAtF;AAAsiEC,EAAAA,QAAQ,EAAE,SAASC,yBAAT,CAAmC5D,EAAnC,EAAuCC,GAAvC,EAA4C;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAC7pEV,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAACuE,UAAH,CAAc,UAAd,EAA0B,SAASC,kDAAT,GAA8D;AAAE,eAAO7D,GAAG,CAAC4B,gBAAJ,EAAP;AAAgC,OAA1H;AACAvC,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,CAA3B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,aAAb;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,CAA3B,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACuE,UAAH,CAAc,OAAd,EAAuB,SAASE,6CAAT,GAAyD;AAAE,eAAO9D,GAAG,CAAC4B,gBAAJ,CAAqB,OAArB,CAAP;AAAuC,OAAzH;AACAvC,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACArB,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,qBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,EAA3B;AACAZ,MAAAA,EAAE,CAACuE,UAAH,CAAc,OAAd,EAAuB,SAASG,6CAAT,GAAyD;AAAE,eAAO/D,GAAG,CAAC4B,gBAAJ,CAAqB,UAArB,CAAP;AAA0C,OAA5H;AACAvC,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACArB,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,uBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,SAAtB,EAAiC,EAAjC;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,eAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,SAAtB;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAAC2E,MAAH,CAAU,EAAV,EAAc,aAAd;AACA3E,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,SAAtB;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAAC2E,MAAH,CAAU,EAAV,EAAc,aAAd;AACA3E,MAAAA,EAAE,CAAC4E,UAAH,CAAc,EAAd,EAAkBnE,8BAAlB,EAAkD,CAAlD,EAAqD,CAArD,EAAwD,GAAxD,EAA6D,EAA7D;AACAT,MAAAA,EAAE,CAAC2E,MAAH,CAAU,EAAV,EAAc,OAAd;AACA3E,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,aAAjB,EAAgC,EAAhC;AACArB,MAAAA,EAAE,CAAC2E,MAAH,CAAU,EAAV,EAAc,aAAd;AACA3E,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,EAA3B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,kBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,OAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,8CAAd;AACAb,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,EAA3B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,mBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,OAAd;AACAb,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,EAA3B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACArB,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,8BAAd;AACAb,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,GAAtB,EAA2B,EAA3B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,UAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACArB,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAAC4E,UAAH,CAAc,EAAd,EAAkBxD,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,mBAAxE,EAA6F,EAA7F;AACApB,MAAAA,EAAE,CAAC2E,MAAH,CAAU,EAAV,EAAc,OAAd;AACH;;AAAC,QAAIjE,EAAE,GAAG,CAAT,EAAY;AACVV,MAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,WAAd,EAA2BlE,GAAG,CAACkB,SAA/B;AACA7B,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,OAAd,EAAuB,OAAvB,EAAgC,aAAhC,EAA+C7E,EAAE,CAAC8E,WAAH,CAAe,EAAf,EAAmB,EAAnB,EAAuBnE,GAAG,CAACmB,KAA3B,CAA/C,EAAkF,WAAlF,EAA+F,IAA/F,EAAqG,UAArG,EAAiH,IAAjH,EAAuH,UAAvH,EAAmInB,GAAG,CAAC6C,iBAAJ,GAAwB7C,GAAG,CAACe,YAA5B,GAA2C,EAA9K;AACA1B,MAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,OAAd,EAAuB,UAAvB,EAAmC,SAAnC,EAA8ClE,GAAG,CAACsB,QAAJ,CAAa8C,OAAb,IAAwBpE,GAAG,CAACsB,QAAJ,CAAa+C,OAAnF,EAA4F,aAA5F,EAA2GhF,EAAE,CAAC8E,WAAH,CAAe,EAAf,EAAmB,EAAnB,EAAuBnE,GAAG,CAACsB,QAA3B,CAA3G,EAAiJ,WAAjJ,EAA8J,IAA9J,EAAoK,UAApK,EAAgL,IAAhL,EAAsL,MAAtL,EAA8LtB,GAAG,CAACc,IAAJ,GAAW,UAAX,GAAwB,MAAtN,EAA8N,UAA9N,EAA0O,uBAA1O;AACAzB,MAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,MAAd,EAAsB7E,EAAE,CAAC8E,WAAH,CAAe,EAAf,EAAmB,EAAnB,EAAuBnE,GAAG,CAACsE,MAA3B,CAAtB;AACAjF,MAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,aAAd,EAA6B7E,EAAE,CAAC8E,WAAH,CAAe,EAAf,EAAmB,EAAnB,EAAuBnE,GAAG,CAACuB,YAA3B,CAA7B,EAAuE,OAAvE,EAAgF,aAAhF;AACAlC,MAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,UAAd,EAA0B,CAAClE,GAAG,CAACkB,SAAJ,CAAcqD,KAAzC,EAAgD,MAAhD,EAAwD,SAAxD;AACAlF,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb;AACAjB,MAAAA,EAAE,CAAC6E,UAAH,CAAc,MAAd,EAAsB7E,EAAE,CAAC8E,WAAH,CAAe,EAAf,EAAmB,EAAnB,EAAuBnE,GAAG,CAACwE,eAA3B,CAAtB;AACH;AAAE,GAnHoD;AAmHlDC,EAAAA,UAAU,EAAE,CAAClF,EAAE,CAACmF,aAAJ,EAAmBnF,EAAE,CAACoF,oBAAtB,EAA4CpF,EAAE,CAACqF,kBAA/C,EAAmEpF,EAAE,CAACqF,cAAtE,EAAsFtF,EAAE,CAACuF,eAAzF,EAA0GvF,EAAE,CAACwF,oBAA7G,EAAmIxF,EAAE,CAACyF,iBAAtI,EAAyJvF,EAAE,CAACwF,IAA5J,EAAkKvF,EAAE,CAACwF,iBAArK,EAAwLvF,EAAE,CAACwF,eAA3L,EAA4MvF,EAAE,CAACwF,qBAA/M,CAnHsC;AAmHiMC,EAAAA,KAAK,EAAE,CAACxF,EAAE,CAACyF,eAAJ,EAAqB7F,EAAE,CAAC8F,SAAxB,CAnHxM;AAmH4OC,EAAAA,MAAM,EAAE,CAAC,2xiBAAD;AAnHpP,CAArB,CAAtC;;AAoHArH,UAAU,CAAC,CACPG,MAAM,CAACQ,YAAY,CAAC2G,mBAAd,CADC,EAEPrH,UAAU,CAAC,aAAD,EAAgBW,UAAhB,CAFH,CAAD,EAGP4B,gBAAgB,CAAC+E,SAHV,EAGqB,QAHrB,EAG+B,KAAK,CAHpC,CAAV;;AAIAvH,UAAU,CAAC,CACPG,MAAM,CAACQ,YAAY,CAAC6G,cAAd,CADC,EAEPvH,UAAU,CAAC,aAAD,EAAgBW,UAAhB,CAFH,CAAD,EAGP4B,gBAAgB,CAAC+E,SAHV,EAGqB,iBAHrB,EAGwC,KAAK,CAH7C,CAAV", "sourcesContent": ["import { __decorate, __metadata } from \"tslib\";\r\nimport { Store, Select } from \"@ngxs/store\";\r\nimport { LoginWithEmailAndPassword } from \"../store/action/login.action\";\r\nimport { UserResponse, UserRequest, } from \"../store/model/login.model\";\r\nimport { FormGroup, FormControl, Validators } from \"@angular/forms\";\r\nimport { EMAIL_PATTERN } from \"src/app/common/helpers/pattern.helper\";\r\nimport { ScLoginState } from \"../store/state/login.state\";\r\nimport { Observable } from \"rxjs\";\r\nimport { EMAIL_REQUIRED, INVALID_EMAIL } from \"src/app/constant/message\";\r\nimport { LINKED_IN_URL, GMAIL_URL } from \"../../../../../constant/api.url\";\r\nimport { environment } from \"../../../../../../environments/environment\";\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@ngxs/store\";\r\nimport * as i2 from \"@angular/forms\";\r\nimport * as i3 from \"../../../../../../ss-ui/input/component/input.component\";\r\nimport * as i4 from \"@angular/common\";\r\nimport * as i5 from \"../../../../../../ss-ui/checkbox/component/checkbox.component\";\r\nimport * as i6 from \"../../../../../../ss-ui/button/component/button.component\";\r\nimport * as i7 from \"../../../../../common/custom-loader/customloader.component\";\r\nimport * as i8 from \"../../../../../common/pipe/FormControlPipe\";\r\nfunction SCLoginComponent_p_31_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"p\", 37);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const auth_r3 = ctx.$implicit;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", auth_r3.message, \" \");\r\n} }\r\nfunction SCLoginComponent_app_custom_loader_60_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"app-custom-loader\");\r\n} }\r\nexport class SCLoginComponent {\r\n    constructor(store) {\r\n        this.store = store;\r\n        this.hide = true;\r\n        this.errorMessage = \"\";\r\n    }\r\n    ngOnInit() {\r\n        this.initForm();\r\n    }\r\n    initForm() {\r\n        this.loginForm = new FormGroup({\r\n            email: new FormControl(\"\", [\r\n                Validators.required,\r\n                Validators.pattern(EMAIL_PATTERN),\r\n            ]),\r\n            password: new FormControl(\"\", [Validators.required]),\r\n            isRememberMe: new FormControl(false),\r\n        });\r\n        const email = this.store.selectSnapshot((state) => state.auth.email);\r\n        if (email) {\r\n            this.email.setValue(email);\r\n        }\r\n    }\r\n    authenticateUser(provider) {\r\n        if (provider === \"gmail\") {\r\n            const scope = [\"profile\", \"email\"].join(\" \");\r\n            chrome.tabs.create({\r\n                url: `${GMAIL_URL}=${environment.googleClientId}&redirect_uri=${environment.gmailSignIn}&scope=${scope}`,\r\n                selected: true,\r\n            });\r\n        }\r\n        else if (provider === \"linkedin\") {\r\n            chrome.tabs.create({\r\n                url: `${LINKED_IN_URL}=${environment.linkedinClientId}&redirect_uri=${environment.linkedinSignIn}&scope=r_liteprofile%20r_emailaddress`,\r\n                selected: true,\r\n            });\r\n        }\r\n        else {\r\n            const payload = {\r\n                email: this.email.value,\r\n                password: this.password.value,\r\n            };\r\n            this.store.dispatch(new LoginWithEmailAndPassword(payload, this.isRememberMe.value));\r\n        }\r\n    }\r\n    get isRememberMe() {\r\n        return this.loginForm.controls.isRememberMe;\r\n    }\r\n    get email() {\r\n        return this.loginForm.controls.email;\r\n    }\r\n    get password() {\r\n        return this.loginForm.controls.password;\r\n    }\r\n    get isEmailFieldValid() {\r\n        if (this.email.hasError(\"required\")) {\r\n            this.errorMessage = EMAIL_REQUIRED;\r\n            return true;\r\n        }\r\n        else {\r\n            if (this.email.hasError(\"pattern\")) {\r\n                this.errorMessage = INVALID_EMAIL;\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n}\r\nSCLoginComponent.ɵfac = function SCLoginComponent_Factory(t) { return new (t || SCLoginComponent)(i0.ɵɵdirectiveInject(i1.Store)); };\r\nSCLoginComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: SCLoginComponent, selectors: [[\"app-sc-login\"]], decls: 62, vars: 29, consts: [[1, \"form-heading\"], [1, \"form-container\"], [1, \"login-container\"], [1, \"connectForm\"], [1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"field-container\", \"social-icon-wraper\", \"mt-0\", 2, \"margin-bottom\", \"20px\"], [1, \"text-color-black\", \"primary-font-family\", \"heading-sm\", \"col-sm-8\", \"pl-0\"], [1, \"social-login-container\", \"mb-0\", \"col-sm-4\", \"pl-0\", \"pr-0\"], [1, \"social-login\", \"d-block\", \"text-right\"], [\"href\", \"javascript:void(0);\", \"role\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"google-login-btn\", \"w-50\", \"social-icon-tag\", 2, \"margin-right\", \"10px\", 3, \"click\"], [\"googleBtn\", \"\"], [1, \"inline-block\", \"vm\"], [\"width\", \"16px\", \"alt\", \"Google sign-in\", \"src\", \"assets/img/google-icon.svg\"], [1, \"show-mobile-text\"], [\"href\", \" javascript:void(0);\", \"role\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"w-50\", \"social-icon-tag\", 2, \"text-transform\", \"none\", 3, \"click\"], [\"width\", \"16px\", \"alt\", \"linkedin sign-in\", \"src\", \"assets/img/linkedin-icon.svg\"], [1, \"social-signup-text\"], [1, \"field-container\"], [\"placeholder\", \"Email\", 3, \"label\", \"formControl\", \"autofocus\", \"required\", \"errorMsg\"], [\"placeholder\", \"Password\", 3, \"label\", \"isError\", \"formControl\", \"autofocus\", \"required\", \"type\", \"errorMsg\"], [\"class\", \"form-error\", 4, \"ngIf\"], [1, \"remember-me-container\"], [1, \"d-inline-block\"], [3, \"formControl\", \"label\"], [1, \"d-inline-block\", \"text-right\"], [\"href\", \"https://www.salezshark.com/connect/app/auth/forgotPassword\", \"target\", \"_blank\", 1, \"forgot-password\", \"text-weight-bold\", \"text-body-xs\", \"text-quaternary-color\"], [1, \"login-forgotBox\", \"clearfix\"], [1, \"float-right\"], [\"type\", \"submit\", \"ss-button\", \"\", 2, \"width\", \"100%\", 3, \"disabled\", \"type\"], [1, \"login-termCondition\", 2, \"display\", \"none\"], [\"href\", \"\"], [1, \"login-bgImg\"], [\"src\", \"assets/img/pattern.svg\", \"alt\", \"pattern\"], [1, \"signInOption\"], [\"href\", \"https://www.salezshark.com/connect/app/auth/sign-up\", \"target\", \"_blank\", 1, \"signInlink\"], [1, \"signupIntext\"], [4, \"ngIf\"], [1, \"form-error\"]], template: function SCLoginComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"div\", 0);\r\n        i0.ɵɵelementStart(1, \"div\", 1);\r\n        i0.ɵɵelementStart(2, \"div\", 2);\r\n        i0.ɵɵelementStart(3, \"div\", 3);\r\n        i0.ɵɵelementStart(4, \"form\", 4);\r\n        i0.ɵɵlistener(\"ngSubmit\", function SCLoginComponent_Template_form_ngSubmit_4_listener() { return ctx.authenticateUser(); });\r\n        i0.ɵɵelementStart(5, \"div\", 5);\r\n        i0.ɵɵelementStart(6, \"h1\", 6);\r\n        i0.ɵɵtext(7, \"Login using\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(8, \"div\", 7);\r\n        i0.ɵɵelementStart(9, \"div\", 8);\r\n        i0.ɵɵelementStart(10, \"a\", 9, 10);\r\n        i0.ɵɵlistener(\"click\", function SCLoginComponent_Template_a_click_10_listener() { return ctx.authenticateUser(\"gmail\"); });\r\n        i0.ɵɵelementStart(12, \"span\", 11);\r\n        i0.ɵɵelement(13, \"img\", 12);\r\n        i0.ɵɵelementStart(14, \"span\", 13);\r\n        i0.ɵɵtext(15, \"Sign in with Google\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(16, \"a\", 14);\r\n        i0.ɵɵlistener(\"click\", function SCLoginComponent_Template_a_click_16_listener() { return ctx.authenticateUser(\"linkedin\"); });\r\n        i0.ɵɵelementStart(17, \"span\", 11);\r\n        i0.ɵɵelement(18, \"img\", 15);\r\n        i0.ɵɵelementStart(19, \"span\", 13);\r\n        i0.ɵɵtext(20, \"Sign in with LinkedIn\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(21, \"section\", 16);\r\n        i0.ɵɵelementStart(22, \"span\");\r\n        i0.ɵɵtext(23, \"Or Login With\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(24, \"div\", 17);\r\n        i0.ɵɵelementStart(25, \"section\");\r\n        i0.ɵɵelement(26, \"ss-input\", 18);\r\n        i0.ɵɵpipe(27, \"formControl\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(28, \"section\");\r\n        i0.ɵɵelement(29, \"ss-input\", 19);\r\n        i0.ɵɵpipe(30, \"formControl\");\r\n        i0.ɵɵtemplate(31, SCLoginComponent_p_31_Template, 2, 1, \"p\", 20);\r\n        i0.ɵɵpipe(32, \"async\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(33, \"div\", 21);\r\n        i0.ɵɵelementStart(34, \"div\", 22);\r\n        i0.ɵɵelement(35, \"ss-checkbox\", 23);\r\n        i0.ɵɵpipe(36, \"formControl\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(37, \"div\", 24);\r\n        i0.ɵɵelementStart(38, \"span\");\r\n        i0.ɵɵelementStart(39, \"a\", 25);\r\n        i0.ɵɵtext(40, \"Forgot Password?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(41, \"div\", 26);\r\n        i0.ɵɵelementStart(42, \"div\", 27);\r\n        i0.ɵɵelementStart(43, \"button\", 28);\r\n        i0.ɵɵtext(44, \"Login\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(45, \"div\", 29);\r\n        i0.ɵɵelementStart(46, \"span\");\r\n        i0.ɵɵtext(47, \"By using Salezconnect, you are agree to our \");\r\n        i0.ɵɵelementStart(48, \"a\", 30);\r\n        i0.ɵɵtext(49, \"Terms of services\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtext(50, \" and \");\r\n        i0.ɵɵelementStart(51, \"a\", 30);\r\n        i0.ɵɵtext(52, \"Privacy policy\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(53, \"span\", 31);\r\n        i0.ɵɵelement(54, \"img\", 32);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(55, \"div\", 33);\r\n        i0.ɵɵtext(56, \"DON\\u2019T HAVE AN ACCOUNT? \");\r\n        i0.ɵɵelementStart(57, \"a\", 34);\r\n        i0.ɵɵtext(58, \" SIGN UP\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(59, \"div\", 35);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(60, SCLoginComponent_app_custom_loader_60_Template, 1, 0, \"app-custom-loader\", 36);\r\n        i0.ɵɵpipe(61, \"async\");\r\n    } if (rf & 2) {\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\r\n        i0.ɵɵadvance(22);\r\n        i0.ɵɵproperty(\"label\", \"Email\")(\"formControl\", i0.ɵɵpipeBind1(27, 19, ctx.email))(\"autofocus\", true)(\"required\", true)(\"errorMsg\", ctx.isEmailFieldValid ? ctx.errorMessage : \"\");\r\n        i0.ɵɵadvance(3);\r\n        i0.ɵɵproperty(\"label\", \"Password\")(\"isError\", ctx.password.invalid && ctx.password.touched)(\"formControl\", i0.ɵɵpipeBind1(30, 21, ctx.password))(\"autofocus\", true)(\"required\", true)(\"type\", ctx.hide ? \"password\" : \"text\")(\"errorMsg\", \" Password is required\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(32, 23, ctx.users$));\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"formControl\", i0.ɵɵpipeBind1(36, 25, ctx.isRememberMe))(\"label\", \"Remember me\");\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"disabled\", !ctx.loginForm.valid)(\"type\", \"primary\");\r\n        i0.ɵɵadvance(17);\r\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(61, 27, ctx.isLoginLoading$));\r\n    } }, directives: [i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i3.InputComponent, i2.NgControlStatus, i2.FormControlDirective, i2.RequiredValidator, i4.NgIf, i5.CheckboxComponent, i6.ButtonComponent, i7.CustomloaderComponent], pipes: [i8.FormControlPipe, i4.AsyncPipe], styles: [\"[_ngcontent-%COMP%]:root{--blue-subTitle: #99bbff;--blue-E8EEFD: #e8eefd;--tabBgColor: #e5e8ed;--color-ff6b6b: #ff6b6b;--color-f0f6ff: #f0f6ff;--color-cacdd6: #cacdd6;--color-f5f5f5: #f5f5f5;--color-eee: #eee;--color-ddd: #ddd;--color-b8d5ff: #b8d5ff;--color-4ecdc4: #4ecdc4;--color-f6f7f8: #f6f7f8;--color-d8dce3: #d8dce3;--color-f6d0d4: #f6d0d4;--color-e7f0f9: #e7f0f9;--color-c3cad6: #c3cad6;--color-d2d9ef: #d2d9ef;--color-c6d9ff: #c6d9ff;--color-D8DBE2: #d8dbe2;--color-CCDDFF: #ccddff;--color-6699ff: #6699ff;--color-9dcaff: #9dcaff;--color-seagreen: seagreen;--color-ca98f5: #ca98f5;--color-b3bcc9: #b3bcc9;--color-e8e8e8: #e8e8e8;--color-dae9ff: #dae9ff;--color-dcdfe7: #dcdfe7;--color-e7ebf2: #e7ebf2;--color-dbeaff: #dbeaff;--color-ecf4ff: #ecf4ff;--color-f9f9fb: #f9f9fb;--color-f8f8f8: #f8f8f8;--color-EBFFD7: #ebffd7;--color-f3f3f3: #f3f3f3;--color-f5f8ff: #f5f8ff;--color-f1f1f1: #f1f1f1;--color-E8EAEF: #e8eaef;--color-efeeea: #efeeea;--color-a0a8b9: #a0a8b9;--color-808fa5: #808fa5;--color-66666: #666666;--color-c5ccd8: #969fb2;--color-9AA1AF: #9aa1af;--color-6a7386: #6a7386;--color-999999: #999999;--color-333435: #333435;--color-ef9da7: #ef9da7;--color-e0c59a: #e0c59a}[_ngcontent-%COMP%]:root{--ss-background-default-shadow: 0 2px 12px 0 #E0E5EC;--ss-background-hoverd-shadow: 0 10px 24px 0 #E0E5EC}header[_ngcontent-%COMP%]{text-align:center;padding:12px 20px;line-height:35px;background-color:var(--ss-header-bg-color)}header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:40px;width:150px}.main-form-container[_ngcontent-%COMP%]{height:calc(100% - 95px);position:relative}.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]{border-radius:4px;margin:0 auto}.signupIntext[_ngcontent-%COMP%]{padding-top:30px;padding-bottom:5px;text-align:center}.signupIntext[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--secondary-font);font-weight:bold;line-height:14px;font-size:12px;text-transform:uppercase}.signupIntext[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-family:var(--ss-secondary-font-family)}.form-heading[_ngcontent-%COMP%]{margin-top:56px}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:24px;font-weight:300}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:1px}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]{float:right}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   p.signup[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]{margin:0 auto}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]{max-width:500px;margin:auto;position:relative;padding:25px 58px 70px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container.signup-container[_ngcontent-%COMP%]{max-width:576px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";left:0px;top:10px;width:73px;height:73px;border-radius:4px;background:#ebffd7;background:var(--color-EBFFD7);z-index:1}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .login-bgImg[_ngcontent-%COMP%]{position:absolute;right:0px;bottom:0px;width:128px;height:275px;z-index:1}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]{background-color:var(--ss-white-color);border-radius:2px;box-shadow:0 2px 12px #e0e5ec;box-shadow:var(--ss-background-default-shadow);transition:all .3s ease-out;-o-transition:all .3s ease-out;width:auto;margin:auto;position:relative;z-index:2}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]{display:block;font-size:13px;color:var(--ss-label-text-color);font-family:var(--ss-secondary-font-family);letter-spacing:0;line-height:16px;text-align:left;font-size:.8125rem;padding-bottom:5px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]{margin-bottom:1rem}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]{text-align:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]{display:flex;justify-content:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .social-login-container[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .google-login-btn[_ngcontent-%COMP%]{margin-right:.5rem}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]{margin:0 auto}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:2.4px;text-align:center;position:relative;overflow:hidden}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;vertical-align:baseline;zoom:1;position:relative;padding:2px 8px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{content:\\\"\\\";display:block;width:1000px;position:absolute;top:.73em;width:116px;border:1px solid #eaeaea}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before{right:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .field-container[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]   .no-background[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{left:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .connectForm[_ngcontent-%COMP%]   .sign-in-error-container[_ngcontent-%COMP%]{min-height:500px;display:flex;flex-direction:column;justify-content:center}.form-heading[_ngcontent-%COMP%]   .form-container.register-container[_ngcontent-%COMP%]{padding:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]{padding-bottom:65px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]{width:200px;height:50px;border:1px solid #ccddff;border-radius:4px;background-color:var(--ss-white-color);float:left;transition:box-shadow .3s ease-out,transform .3s ease-out,opacity .2s ease-out;box-shadow:0 2px 20px #0000000d;cursor:pointer}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]:hover{box-shadow:#2d2d2d0d 0 1px 1px,#3131310d 0 2px 2px,#2a2a2a0d 0 4px 4px,#2020200d 0 8px 8px,#3131310d 0 16px 16px,#2323230d 0 32px 32px;transform:translateY(-2px)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]   .social-img[_ngcontent-%COMP%]{height:40px;width:40px;box-sizing:content-box;margin-left:36px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social[_ngcontent-%COMP%]   .social-p[_ngcontent-%COMP%]{padding-top:12px;color:var(--ss-black-color);font-size:13px;float:right;margin-right:56px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .login-container[_ngcontent-%COMP%]   .social.login[_ngcontent-%COMP%]{margin-right:12px;margin-left:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{padding:20px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{border:none;width:100%}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field.less[_ngcontent-%COMP%]{width:50%;vertical-align:top}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:var(--ss-error-color);margin:4px;font-weight:normal;font-size:12px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .remember-me-container[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--primary-font);line-height:18px;font-size:12px;font-weight:700;cursor:pointer}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .remember-me-container[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover{text-decoration:none}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]{margin:0 auto;border-radius:4px;background-color:var(--ss-primary-color);color:var(--ss-white-color);border:none;cursor:pointer;min-width:110px;text-transform:uppercase;font-family:var(--secondary-font);line-height:16px;font-size:13px;opacity:1!important;height:38px;font-family:var(--ss-secondary-font-family);border:1px solid transparent}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:hover{background-color:var(--ss-white-color);color:var(--ss-primary-color)!important;border:1px solid var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:inline-block;padding-top:8px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:relative}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   div.btn-submit[_ngcontent-%COMP%]{position:absolute;right:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{margin:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-forgotBox[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]:before{left:24px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-termCondition[_ngcontent-%COMP%]{color:var(--ss-black-color);font-family:var(--secondary-font);font-size:12px;line-height:18px;text-align:center}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .login-termCondition[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-family:var(--ss-secondary-font-family);color:var(--ss-primary-color)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;color:#f9f9fb;color:var(--color-f9f9fb)}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]:disabled:hover{box-shadow:0 2px 20px #0000000d}.terms[_ngcontent-%COMP%]{padding-top:60px;text-align:center;margin:0 auto}.terms[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ss-black-color);font-size:12px;font-weight:500;letter-spacing:1px;line-height:14px}.terms[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ss-primary-color);cursor:pointer}.form-error[_ngcontent-%COMP%]{padding-top:unset!important}.ss-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:18px!important;width:20px!important;border:2px solid var(--ss-quinary-color);background-color:var(--ss-white-color);border-radius:2px}  .mat-checkbox .mat-checkbox-frame{border-color:var(--ss-black-color)}  .mat-checkbox-checked .mat-checkbox-background{background-color:#808fa5!important;background-color:var(--color-808fa5)!important}.mat-icon[_ngcontent-%COMP%]{cursor:pointer;margin-right:10px}.img-container[_ngcontent-%COMP%]{margin:auto}.top[_ngcontent-%COMP%]{margin-top:54px}.material-icons[_ngcontent-%COMP%]{display:inline-flex;vertical-align:middle}.login-container[_ngcontent-%COMP%]{padding:45px 20px!important}.float-right[_ngcontent-%COMP%]{width:100%!important}.signInOption[_ngcontent-%COMP%]{font-size:13px;font-family:var(--ss-secondary-font-family);font-weight:700;margin-top:10px}.signInOption[_ngcontent-%COMP%]   .signInlink[_ngcontent-%COMP%]{color:var(--ss-primary-color);cursor:pointer;text-decoration:none}.signInOption[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{text-decoration:none}.social-signup-text[_ngcontent-%COMP%]{margin-top:-15px;margin-bottom:15px;width:100%;position:relative;text-align:center;font:700 14px/20px Roboto,\\\"Helvetica Neue\\\",sans-serif}.social-signup-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{text-decoration:none;padding:0 7px;background-color:#fff;display:inline-block;position:relative}.social-signup-text[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;width:100%;height:.5px;background-color:#e5e8ed;top:10px;left:0;z-index:0}.btn-outline-dark[_ngcontent-%COMP%]:hover{background-color:transparent!important;border-color:var(--ss-primary-color)!important}.social-icon-wraper[_ngcontent-%COMP%]{display:flex}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:34px!important;height:34px!important;line-height:14px!important}.social-icon-wraper[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-icon-tag[_ngcontent-%COMP%]{padding:8px 6px;border-radius:2px!important;border:1.5px solid var(--ss-quinary-color);font-family:var(--ss-secondary-font-family);font-weight:700}.show-mobile-text[_ngcontent-%COMP%]{display:none}.login-form[_ngcontent-%COMP%]{width:100%}.remember-me-container[_ngcontent-%COMP%]   .d-inline-block[_ngcontent-%COMP%]{width:50%;font-weight:700}.error-message[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}@media (max-width: 1023px){.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]{max-width:600px}.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .create-account-steps[_ngcontent-%COMP%]{border-radius:4px;float:right;margin-top:222px;width:250px!important;right:-226px!important}.form-heading[_ngcontent-%COMP%]{padding:10px;margin-top:56px}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.form-heading[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .mat-form-field.less[_ngcontent-%COMP%]{width:100%}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{text-align:center}.form-heading[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]   .signup[_ngcontent-%COMP%]{width:100%}}@media (max-width: 1440px){.main-form-container[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .create-account-steps[_ngcontent-%COMP%]{width:260px;right:-236px}}\"] });\r\n__decorate([\r\n    Select(ScLoginState.getLoginUserDetails),\r\n    __metadata(\"design:type\", Observable)\r\n], SCLoginComponent.prototype, \"users$\", void 0);\r\n__decorate([\r\n    Select(ScLoginState.isLoginLoading),\r\n    __metadata(\"design:type\", Observable)\r\n], SCLoginComponent.prototype, \"isLoginLoading$\", void 0);\r\n"]}, "metadata": {}, "sourceType": "module"}